import React, { useEffect, useState } from 'react';
import { View, Button, Text, StyleSheet } from 'react-native';
import InMobiInterstitial from './InMobiInterstitial';

interface InMobiEvent {
  placementId: string;
  message?: string;
}

interface InMobiInterstitialAdComponentProps {
  accountId: string;
  onAdClicked?: (event: InMobiEvent) => void;
  onAdDismissed?: (event: InMobiEvent) => void;
  onAdLoaded?: (event: InMobiEvent) => void;
  onAdLoadFailed?: (event: InMobiEvent) => void;
  placementId: string;
}

const InMobiInterstitialAdComponent: React.FC<InMobiInterstitialAdComponentProps> = ({
  accountId,
  onAdClicked,
  onAdDismissed,
  onAdLoaded,
  onAdLoadFailed,
  placementId,
}) => {
  const [adStatus, setAdStatus] = useState<string>('Not Loaded');
  const [interstitial, setInterstitial] = useState<InMobiInterstitial | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isAdReady, setIsAdReady] = useState<boolean>(false);

  useEffect(() => {
    // Initialize the SDK when component mounts
    const initializeSDK = async (): Promise<void> => {
      try {
        console.log('initializeSDK called with accountId:', accountId);
        const consentObj = {};
        await InMobiInterstitial.initializeSDK(accountId, 'p-UYSkvZs9ad-zz', JSON.stringify(consentObj));
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize InMobi SDK:', error);
      }
    };

    initializeSDK();

    // Clean up on component unmount
    return () => {
      if (interstitial) {
        interstitial.removeAllListeners();
      }
    };
  }, [accountId]);

  useEffect(() => {
    if (isInitialized && placementId) {
      const newInterstitial = new InMobiInterstitial(placementId);

      // Set up event listeners
      newInterstitial.addEventListener('inMobiInterstitialAdLoaded', (event: InMobiEvent) => {
        console.log('Event recevied: inMobiInterstitialAdLoaded', event);
        setAdStatus('Ad Loaded');
        setIsAdReady(true);
        if (onAdLoaded) onAdLoaded(event);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdLoadFailed', (event: InMobiEvent) => {
        setAdStatus(`Load Failed: ${event.message || 'Unknown error'}`);
        setIsAdReady(false);
        if (onAdLoadFailed) onAdLoadFailed(event);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdWillPresent', (_: InMobiEvent) => {
        setAdStatus('Ad Will Present');
      });

      newInterstitial.addEventListener('inMobiInterstitialAdDidPresent', (_: InMobiEvent) => {
        setAdStatus('Ad Did Present');
      });

      newInterstitial.addEventListener('inMobiInterstitialAdPresentFailed', (event: InMobiEvent) => {
        setAdStatus(`Ad Present Failed: ${event.message || 'Unknown error'}`);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdDidDismiss', (event: InMobiEvent) => {
        setAdStatus('Ad Dismissed');
        setIsAdReady(false);
        if (onAdDismissed) onAdDismissed(event);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdImpression', (event: InMobiEvent) => {
        console.log('Ad Impression Recorded', event);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdUserLeftApplication', (event: InMobiEvent) => {
        console.log('User Left Application', event);
      });

      newInterstitial.addEventListener('inMobiInterstitialAdClicked', (event: InMobiEvent) => {
        if (onAdClicked) onAdClicked(event);
      });

      setInterstitial(newInterstitial);
    }
  }, [isInitialized, placementId, onAdLoaded, onAdLoadFailed, onAdDismissed, onAdClicked]);

  const loadAd = async (): Promise<void> => {
    if (!interstitial) return;

    setAdStatus('Loading Ad...');
    try {
      await interstitial.loadAd();
      // The actual status update will come through the event listener
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setAdStatus(`Error Loading Ad: ${errorMessage}`);
      setIsAdReady(false);
    }
  };

  const showAd = async (): Promise<void> => {
    if (!interstitial) return;

    try {
      const isReady = await interstitial.isReady();
      if (isReady) {
        setAdStatus('Showing Ad...');
        await interstitial.show();
      } else {
        setAdStatus('Ad Not Ready');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setAdStatus(`Error Showing Ad: ${errorMessage}`);
    }
  };

  const settings = (): void => {
    if (interstitial) {
      interstitial.showCMPPreferences();
    } else {
      console.warn('Interstitial ad not initialized yet');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.statusText}>Ad Status: {adStatus}</Text>
      <View style={styles.buttonContainer}>
        <Button title="Load Ad" onPress={loadAd} disabled={!isInitialized} />
        <Button title="Show Ad" onPress={showAd} disabled={!isAdReady} />
        <Button title="Settings" onPress={settings} disabled={!isInitialized} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginVertical: 10,
  },
  statusText: {
    marginBottom: 10,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
});

export default InMobiInterstitialAdComponent;
