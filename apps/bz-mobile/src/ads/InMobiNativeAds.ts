import { requireNativeComponent, UIManager, findNodeHandle, Platform, ViewStyle, StyleProp } from 'react-native';

// TypeScript interfaces for InMobi components
export interface InMobiAdProps {
  placementId: string;
  autoShowOnLoad?: boolean;
  onAdShown?: (layout: any) => void;
  onAdLoaded?: () => void;
  onAdLoadFailed?: (event: any) => void;
  onAdImpression?: () => void;
  onAdClicked?: () => void;
  onAdStatusChanged?: () => void;
  style?: StyleProp<ViewStyle>;
}

export const InFeedAd = requireNativeComponent<InMobiAdProps>('RNInMobiInFeedAd');
export const SplashAd = requireNativeComponent<InMobiAdProps>('RNInMobiSplashAd');
export const PreRollAd = requireNativeComponent<InMobiAdProps>('RNInMobiPreRollAd');

export const send = (ref: any, command: { vm: string; name: string }, args: any[] = []) => {
  try {
    const viewId = findNodeHandle(ref.current);
    if (!viewId) return;

    const viewManagerConfig = UIManager.getViewManagerConfig(command.vm);
    if (!viewManagerConfig) return;

    const commands = viewManagerConfig.Commands;
    if (!commands || !commands[command.name]) return;

    UIManager.dispatchViewManagerCommand(
      viewId,
      Platform.OS === 'ios' ? command.name : `${commands[command.name]}`,
      args,
    );
  } catch (error) {
    console.error('Error sending command to InMobi:', error);
  }
};
