import { NativeModules, NativeEventEmitter } from 'react-native';

interface InMobiEventListener {
  listener: (event: InMobiEvent) => void;
  subscription: { remove: () => void };
}

interface EventListeners {
  [eventType: string]: InMobiEventListener[];
}

interface InMobiEvent {
  placementId: string;
  message?: string;
}

interface InMobiLocation {
  latitude: number;
  longitude: number;
  provider?: string;
}

interface InMobiInterstitialAdManagerInterface {
  initSDK: (accountId: string) => Promise<string>;
  setGender: (gender: string) => void;
  setAge: (age: number) => void;
  setAgeGroup: (ageGroup: string) => void;
  setAgeRestricted: (enabled: boolean) => void;
  setLocation: (city: string, state: string, country: string) => void;
  setLocationLatLng: (latitude: number, longitude: number) => void;
  setLastKnownLocationFromGPS: () => Promise<InMobiLocation | null>;
  setLastKnownLocationFromNetwork: () => Promise<InMobiLocation | null>;
  loadInterstitialAd: (placementId: string) => Promise<boolean>;
  initCMPSDK: (cmpCode: string) => Promise<string>;
  showCMPPreferences: () => void;
  isInterstitialAdReady: (placementId: string) => Promise<boolean>;
  showInterstitialAd: (placementId: string) => Promise<boolean>;
  addListener: (eventType: string) => void;
  removeListeners: (count?: number) => void;
}

const { InMobiInterstitialAdManager } = NativeModules as unknown as {
  InMobiInterstitialAdManager: InMobiInterstitialAdManagerInterface;
};

const inMobiEventEmitter = new NativeEventEmitter(InMobiInterstitialAdManager);

class InMobiInterstitial {
  placementId: string;
  eventListeners: EventListeners;

  constructor(placementId: string) {
    this.placementId = placementId;
    this.eventListeners = {};
  }

  static async initializeSDK(accountId: string, cmpCode: string) {
    await InMobiInterstitialAdManager.initCMPSDK(cmpCode);
    await InMobiInterstitialAdManager.initSDK(accountId);
    return;
  }

  static showCMPPreferences(): void {
    InMobiInterstitialAdManager.showCMPPreferences();
  }

  /**
   * @param gender "MALE" or "FEMALE"
   */
  setGender(gender: string): void {
    return InMobiInterstitialAdManager.setGender(gender);
  }

  setAge(age: number): void {
    return InMobiInterstitialAdManager.setAge(age);
  }

  /**
   * @param ageGroup
   * BELOW_18,
   * BETWEEN_18_AND_24,
   * BETWEEN_25_AND_29,
   * BETWEEN_30_AND_34,
   * BETWEEN_35_AND_44,
   * BETWEEN_45_AND_54,
   * BETWEEN_55_AND_65,
   * ABOVE_65;
   */
  setAgeGroup(ageGroup: string): void {
    return InMobiInterstitialAdManager.setAgeGroup(ageGroup);
  }

  setAgeRestricted(enabled: boolean): void {
    return InMobiInterstitialAdManager.setAgeRestricted(enabled);
  }

  setLocation(city: string, state: string, country: string): void {
    return InMobiInterstitialAdManager.setLocation(city, state, country);
  }

  setLocationLatLng(latitude: number, longitude: number): void {
    return InMobiInterstitialAdManager.setLocationLatLng(latitude, longitude);
  }

  setLastKnownLocationFromGPS(): Promise<InMobiLocation | null> {
    return InMobiInterstitialAdManager.setLastKnownLocationFromGPS();
  }

  setLastKnownLocationFromNetwork(): Promise<InMobiLocation | null> {
    return InMobiInterstitialAdManager.setLastKnownLocationFromNetwork();
  }

  loadAd(): Promise<boolean> {
    console.log(`Loading ad for placement ID: ${this.placementId}`);
    return InMobiInterstitialAdManager.loadInterstitialAd(this.placementId);
  }

  isReady(): Promise<boolean> {
    return InMobiInterstitialAdManager.isInterstitialAdReady(this.placementId);
  }

  show(): Promise<boolean> {
    return InMobiInterstitialAdManager.showInterstitialAd(this.placementId);
  }

  addEventListener(eventType: string, listener: (event: InMobiEvent) => void): () => void {
    console.log('addEventListener called for event:', eventType);
    if (!this.eventListeners[eventType]) {
      this.eventListeners[eventType] = [];
    }

    // // Add this line to explicitly tell the native module to listen for this event type
    // InMobiInterstitialAdManager.addListener(eventType);

    const subscription = inMobiEventEmitter.addListener(eventType, (event: InMobiEvent) => {
      if (event.placementId === this.placementId) {
        listener(event);
      }
    });

    this.eventListeners[eventType].push({ listener, subscription });
    return () => this.removeEventListener(eventType, listener);
  }

  removeEventListener(eventType: string, listener: (event: InMobiEvent) => void): void {
    if (!this.eventListeners[eventType]) return;

    const index = this.eventListeners[eventType].findIndex(item => item.listener === listener);
    if (index !== -1) {
      this.eventListeners[eventType][index].subscription.remove();
      this.eventListeners[eventType].splice(index, 1);
    }
  }

  removeAllListeners(): void {
    Object.keys(this.eventListeners).forEach(eventType => {
      this.eventListeners[eventType].forEach(item => {
        item.subscription.remove();
      });
    });
    this.eventListeners = {};
  }
}

export default InMobiInterstitial;
