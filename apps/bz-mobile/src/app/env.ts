import { TestIds } from 'react-native-google-mobile-ads';
export const DATAAPI_TOKEN = '81DC3A5A39D6D1A9D26FA6DF35A34';
console.log('process.env.BENZINGA_CHAT_STREAMKEY', process.env.BENZINGA_CHAT_STREAMKEY);
const PRD_ENV = {
  'benzinga-article': {
    key: process.env.BENZINGA_ARTICLE_KEY,
    url: 'https://www.benzinga.com/',
  },
  'benzinga-authentication': {
    googleClientId: process.env.BENZINGA_AUTHENTICATION_GOOGLECLIENTID,
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-calendar': {
    dataUrl: 'https://api.benzinga.com/api/',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-calendar-earnings': {
    token: process.env.BENZINGA_CALENDAR_EARNINGS_TOKEN,
  },
  'benzinga-calendar-conference-calls': {
    token: process.env.BENZINGA_CALENDAR_CONFERENCE_CALLS_TOKEN,
  },
  'benzinga-calendar-guidance': {
    token: process.env.BENZINGA_CALENDAR_GUIDANCE_TOKEN,
  },
  'benzinga-calendar-ipo': {
    token: process.env.BENZINGA_CALENDAR_IPO_TOKEN,
  },
  'benzinga-calendar-ratings': {
    token: process.env.BENZINGA_CALENDAR_RATINGS_TOKEN,
  },
  'benzinga-calendar-dividends': {
    token: process.env.BENZINGA_CALENDAR_DIVIDENDS_TOKEN,
  },
  'benzinga-calendar-economics': {
    token: process.env.BENZINGA_CALENDAR_ECONOMICS_TOKEN,
  },
  'benzinga-calendar-fda': {
    token: process.env.BENZINGA_CALENDAR_FDA_TOKEN,
  },
  'benzinga-calendar-ma': {
    token: process.env.BENZINGA_CALENDAR_MA_TOKEN,
  },
  'benzinga-calendar-option-activity': {
    token: process.env.BENZINGA_CALENDAR_OPTION_ACTIVITY_TOKEN,
  },
  'benzinga-chart': {
    key: process.env.BENZINGA_CHART_KEY,
    url: 'https://data-api-pro.benzinga.com/rest/',
  },
  'benzinga-chat': {
    streamKey: process.env.BENZINGA_CHAT_STREAMKEY,
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-iap': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-layout': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-movers': {
    url: 'https://www.benzinga.com/api/',
  },
  'benzinga-news': {
    contentKey: process.env.BENZINGA_NEWS_CONTENTKEY,
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'https://www.benzinga.com',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-notes': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-notification': {
    rootUrl: 'https://www.benzinga.com/',
    url: 'https://api.benzinga.com/',
  },
  'benzinga-permissions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-trade-ideas': {
    url: 'https://www.benzinga.com/lavapress/api/',
    premiumUrl: 'https://www.benzinga.com/research/api/',
  },
  'benzinga-scanner': {
    key: process.env.BENZINGA_SCANNER_KEY,
  },
  'benzinga-securities': {
    key: process.env.BENZINGA_SECURITIES_KEY,
    url: 'https://data-api-pro.benzinga.com/rest/',
  },
  'benzinga-shop': {
    url: 'https://accounts.benzinga.com/api/v1/',
  },
  'benzinga-signals': {
    restfulUrl: 'https://signals.benzinga.io/signals/api/',
    socketUrl: 'wss://signals.benzinga.io/signals/ws',
  },
  'benzinga-subscriptions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-videos': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-price-alerts': {
    url: 'https://data-api-pro.benzinga.com/rest/v1/',
  },
  'benzinga-ads': {
    homeUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_HOMEUNITID,
    newsUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_NEWSUNITID,
    quoteUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_QUOTEUNITID,
    equitiesUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_EQUITIESUNITID,
    cryptoUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_CRYPTOUNITID,
    cannabisUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_CANNABISUNITID,
    premarketUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_PREMARKETUNITID,
  },
  'benzinga-onboarding': {
    url: 'https://www.benzinga.com/',
    rootUrl: 'https://api.benzinga.com',
    fastLinkConfig: process.env.BENZINGA_ONBOARDING_FASTLINKCONFIG,
    fastLinkUrl: process.env.BENZINGA_ONBOARDING_FASTLINK_AUTH_URL,
  },
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const STG_ENV = {
  'benzinga-article': {
    key: process.env.BENZINGA_ARTICLE_KEY,
    url: 'https://www.benzinga.com/',
  },
  'benzinga-authentication': {
    googleClientId: process.env.BENZINGA_AUTHENTICATION_GOOGLECLIENTID,
    url: 'https://accounts.zingbot.bz/',
  },
  'benzinga-calendar': {
    dataUrl: 'https://api.benzinga.com/api/',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-calendar-earnings': {
    token: process.env.BENZINGA_CALENDAR_EARNINGS_TOKEN,
  },
  'benzinga-calendar-conference-calls': {
    token: process.env.BENZINGA_CALENDAR_CONFERENCE_CALLS_TOKEN,
  },
  'benzinga-calendar-guidance': {
    token: process.env.BENZINGA_CALENDAR_GUIDANCE_TOKEN,
  },
  'benzinga-calendar-ipo': {
    token: process.env.BENZINGA_CALENDAR_IPO_TOKEN,
  },
  'benzinga-calendar-ratings': {
    token: process.env.BENZINGA_CALENDAR_RATINGS_TOKEN,
  },
  'benzinga-calendar-dividends': {
    token: process.env.BENZINGA_CALENDAR_DIVIDENDS_TOKEN,
  },
  'benzinga-calendar-economics': {
    token: process.env.BENZINGA_CALENDAR_ECONOMICS_TOKEN,
  },
  'benzinga-calendar-fda': {
    token: process.env.BENZINGA_CALENDAR_FDA_TOKEN,
  },
  'benzinga-calendar-ma': {
    token: process.env.BENZINGA_CALENDAR_MA_TOKEN,
  },
  'benzinga-calendar-option-activity': {
    token: process.env.BENZINGA_CALENDAR_OPTION_ACTIVITY_TOKEN,
  },
  'benzinga-chart': {
    key: process.env.BENZINGA_CHART_KEY,
    url: 'https://data-api-pro.benzinga.com/rest/',
  },
  'benzinga-chat': {
    realmUuid: process.env.BENZINGA_CHAT_REALMUUID,
    streamKey: process.env.BENZINGA_CHAT_STREAMKEY,
    url: 'https://accounts.zingbot.bz/',
  },
  'benzinga-iap': {
    url: 'https://accounts.zingbot.bz/',
  },
  'benzinga-layout': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-news': {
    contentKey: process.env.BENZINGA_NEWS_CONTENTKEY,
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'https://www.benzinga.com',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-notes': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-permissions': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-trade-ideas': {
    url: 'https://www.benzinga.com/lavapress/api/',
    premiumUrl: 'https://bz.zingbot.bz/research/api',
  },
  'benzinga-scanner': {
    key: process.env.BENZINGA_SCANNER_KEY,
    url: 'https://data-api-pro.zingbot.bz/rest/v2/scanner/us/equity/scan',
    socketUrl: 'wss://data-api-pro.zingbot.bz/scannerSocket',
  },
  'benzinga-securities': {
    key: process.env.BENZINGA_SECURITIES_KEY,
    url: 'https://data-api-pro.zingbot.bz/',
  },
  'benzinga-notification': {
    rootUrl: 'https://www.benzinga.com/',
    url: 'https://api.zingbot.bz/',
  },
  'benzinga-shop': {
    url: 'https://accounts.zingbot.bz/api/v1/',
  },
  'benzinga-signals': {
    restfulUrl: 'https://signals.benzinga.io/signals/api/',
    socketUrl: 'wss://signals.benzinga.io/signals/ws',
  },
  'benzinga-subscriptions': {
    url: 'https://accounts.zingbot.bz/',
  },
  'benzinga-videos': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-price-alerts': {
    url: 'https://data-api-pro.benzinga.com/rest/v1/',
  },
  'benzinga-ads': {
    homeUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_HOMEUNITID,
    newsUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_NEWSUNITID,
    quoteUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_QUOTEUNITID,
    equitiesUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_EQUITIESUNITID,
    cryptoUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_CRYPTOUNITID,
    cannabisUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_CANNABISUNITID,
    premarketUnitID: __DEV__ ? TestIds.BANNER : process.env.BENZINGA_ADS_PREMARKETUNITID,
  },
  'benzinga-onboarding': {
    url: 'https://www.benzinga.com/',
    rootUrl: 'https://api.benzinga.com',
    fastLinkConfig: process.env.BENZINGA_ONBOARDING_FASTLINKCONFIG,
    fastLinkUrl: process.env.BENZINGA_ONBOARDING_FASTLINK_AUTH_URL,
  },
};

export const SESSION_ENV = __DEV__ ? PRD_ENV : PRD_ENV;
