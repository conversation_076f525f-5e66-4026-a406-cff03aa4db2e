import React, { ReactNode } from 'react';

import ErrorBoundaryFallback from './ErrorBoundaryFallback';
import { _checkForUpdates } from '../services/updates';

import Data from '../data-mobile/data';
import UserData from '../services/app';
import { CoralogixRum } from '@coralogix/react-native-sdk';

export type ErrorBoundaryProps = {
  children: Exclude<NonNullable<ReactNode>, string | number | boolean>;
};

class ErrorBoundary extends React.Component {
  state: { hasError: boolean; error: string };
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: '' };
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true, error: JSON.stringify(_error) };
  }

  componentDidCatch(error: Error, info: { componentStack: string }) {
    // Example "componentStack":
    //   in ComponentThatThrows (created by <PERSON>pp)
    //   in ErrorBoundary (created by <PERSON>pp)
    //   in View (created by App)
    //   in App
    console.log('Error Boundary catched error:', error, info.componentStack);
    this.setState({
      hasError: true,
      error:
        (error?.message || 'Error message not found.') +
        '\n\n' +
        JSON.stringify(info.componentStack) +
        '\n\n' +
        JSON.stringify(error?.stack || { stack: 'Stack not available' }),
    });
    CoralogixRum.error(error.message, error.stack);
    Data.tracking().trackErrorEvent('emit', {
      error_message: error.message,
      user_id: `${UserData.user()?.benzingaUid ?? -1}`,
    });
  }

  resetError: () => void = async () => {
    this.setState({ hasError: false, error: '' });
    await _checkForUpdates();
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorBoundaryFallback
          resetError={this.resetError}
          errorString={this.state.error}
          hasError={this.state.hasError}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
