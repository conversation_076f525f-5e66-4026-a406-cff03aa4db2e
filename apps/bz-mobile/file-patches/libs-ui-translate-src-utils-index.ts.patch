diff --git a/libs/ui/translate/src/utils/index.ts b/libs/ui/translate/src/utils/index.ts
index 5c05bec296..8ee37197ab 100644
--- a/libs/ui/translate/src/utils/index.ts
+++ b/libs/ui/translate/src/utils/index.ts
@@ -14,15 +14,15 @@ export const translate = (slug: string, options?: TOptions) => {
 };

 export const setLanguage = async (lang: string, namespaces?: string[]) => {
-  if (Array.isArray(namespaces)) {
-    for (const namespace of namespaces) {
-      const translations = await import(`../${lang}/${namespace}.json`);
-      await i18n.addResourceBundle(lang, namespace, translations.default, true, true);
-    }
-  } else {
-    const translations = await import(`../${lang}/common.json`);
-    await i18n.addResourceBundle(lang, 'common', translations.default, true, true);
-  }
+  // if (Array.isArray(namespaces)) {
+  //   for (const namespace of namespaces) {
+  //     const translations = await import(`../${lang}/${namespace}.json`);
+  //     await i18n.addResourceBundle(lang, namespace, translations.default, true, true);
+  //   }
+  // } else {
+  //   const translations = await import(`../${lang}/common.json`);
+  //   await i18n.addResourceBundle(lang, 'common', translations.default, true, true);
+  // }

   await i18n.changeLanguage(lang);
   return i18n.getDataByLanguage(lang);
