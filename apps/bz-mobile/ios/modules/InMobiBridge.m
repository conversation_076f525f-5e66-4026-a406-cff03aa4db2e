#import "InMobiBridge.h"
#import "InMobiPackage.h"
#import "RNInFeedAdManager.h"
#import "RNSplashAdManager.h"
#import "RNPreRollAdManager.h"
#import "InMobiInterstitialAdManager.h"
#import <React/RCTBridge.h>
#import <React/RCTUIManager.h>

@implementation InMobiBridge

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

+ (void)registerModules {
    // This method can be called from AppDelegate to ensure all modules are registered
    // The actual registration happens automatically when the modules are imported
    NSLog(@"InMobi modules registration initiated");
}

@end


