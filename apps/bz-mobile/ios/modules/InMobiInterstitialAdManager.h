#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <InMobiSDK/InMobiSDK.h>
#import <InMobiCMP/InMobiCMP.h>
#import <CoreLocation/CoreLocation.h>
@import InMobiSDK;

@interface InMobiInterstitialAdManager : RCTEventEmitter <RCTBridgeModule, IMInterstitialDelegate, ChoiceCmpDelegate, CCPADelegate, CLLocationManagerDelegate>

@property (nonatomic, strong) NSMutableDictionary *interstitialAds;
@property (nonatomic, assign) BOOL hasListeners;
@property (nonatomic, strong) CLLocationManager *locationMgr;

@end
