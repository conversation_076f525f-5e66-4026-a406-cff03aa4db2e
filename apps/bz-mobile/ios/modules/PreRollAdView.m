#import "PreRollAdView.h"

@implementation PreRollAdView {
    NSString *_placementId;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupPreRollAd];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame inMobiPackage:(InMobiPackage *)inMobiPackage {
    self = [super initWithFrame:frame inMobiPackage:inMobiPackage];
    if (self) {
        [self setupPreRollAd];
    }
    return self;
}

- (void)setupPreRollAd {
    // Pre-roll ad specific setup
    self.backgroundColor = [UIColor clearColor];
    self.autoShowOnLoad = NO; // Pre-roll ads typically don't auto-show
}

- (void)setPlacementId:(NSString *)placementId {
    _placementId = placementId;

    // Convert string to long long and set it in the parent class
    if (placementId && placementId.length > 0) {
        long long placementIdLong = [placementId longLongValue];
        [super setPlacementId:placementIdLong];
        NSLog(@"[PreRollAdView] Set placement ID: %@ -> %lld", placementId, placementIdLong);
    } else {
        NSLog(@"[PreRollAdView] Invalid placement ID: %@", placementId);
    }
}

- (NSString *)placementId {
    return _placementId;
}

@end


