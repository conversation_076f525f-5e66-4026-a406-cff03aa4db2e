#import "RNInFeedAdManager.h"
#import "InFeedAdView.h"
#import <React/RCTUIManager.h>
#import <React/RCTUtils.h>

@implementation RNInFeedAdManager

RCT_EXPORT_MODULE(RNInMobiInFeedAd)

- (instancetype)initWithInMobiPackage:(InMobiPackage *)inMobiPackage {
    self = [super init];
    if (self) {
        self.inMobiPackage = inMobiPackage;
    }
    return self;
}

- (UIView *)view {
    InFeedAdView *adView = [[InFeedAdView alloc] initWithFrame:CGRectZero inMobiPackage:self.inMobiPackage];
    return adView;
}

RCT_EXPORT_VIEW_PROPERTY(placementId, NSString)
RCT_EXPORT_VIEW_PROPERTY(autoShowOnLoad, BOOL)

// Event properties
RCT_EXPORT_VIEW_PROPERTY(onAdLoaded, RCTBubblingEventBlock)
RCT_EXPORT_VIEW_PROPERTY(onAdLoadFailed, RCTBubblingEventBlock)
RCT_EXPORT_VIEW_PROPERTY(onAdClicked, RCTBubblingEventBlock)
RCT_EXPORT_VIEW_PROPERTY(onAdImpression, RCTBubblingEventBlock)
RCT_EXPORT_VIEW_PROPERTY(onAdStatusChanged, RCTBubblingEventBlock)
RCT_EXPORT_VIEW_PROPERTY(onAdShown, RCTBubblingEventBlock)

RCT_EXPORT_METHOD(init:(nonnull NSNumber *)reactTag
                  placementId:(NSString *)placementId
                  autoShow:(BOOL)autoShow)
{
    [self.bridge.uiManager addUIBlock:^(RCTUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        InFeedAdView *view = (InFeedAdView *)viewRegistry[reactTag];
        if ([view isKindOfClass:[InFeedAdView class]]) {
            [view setPlacementId:placementId];
            [view setAutoShowOnLoad:autoShow];
        }
    }];
}

RCT_EXPORT_METHOD(load:(nonnull NSNumber *)reactTag)
{
    [self.bridge.uiManager addUIBlock:^(RCTUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        InFeedAdView *view = (InFeedAdView *)viewRegistry[reactTag];
        if ([view isKindOfClass:[InFeedAdView class]]) {
            [view load];
        }
    }];
}

RCT_EXPORT_METHOD(show:(nonnull NSNumber *)reactTag)
{
    [self.bridge.uiManager addUIBlock:^(RCTUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        InFeedAdView *view = (InFeedAdView *)viewRegistry[reactTag];
        if ([view isKindOfClass:[InFeedAdView class]]) {
            [view show];
        }
    }];
}

RCT_EXPORT_METHOD(click:(nonnull NSNumber *)reactTag)
{
    [self.bridge.uiManager addUIBlock:^(RCTUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        InFeedAdView *view = (InFeedAdView *)viewRegistry[reactTag];
        if ([view isKindOfClass:[InFeedAdView class]]) {
            [view click];
        }
    }];
}

RCT_EXPORT_METHOD(destroy:(nonnull NSNumber *)reactTag)
{
    [self.bridge.uiManager addUIBlock:^(RCTUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        InFeedAdView *view = (InFeedAdView *)viewRegistry[reactTag];
        if ([view isKindOfClass:[InFeedAdView class]]) {
            [view destroy];
        }
    }];
}

- (NSArray<NSString *> *)supportedEvents {
    return @[
        @"onAdLoaded",
        @"onAdLoadFailed",
        @"onAdClicked",
        @"onAdImpression",
        @"onAdStatusChanged",
        @"onAdShown"
    ];
}



+ (BOOL)requiresMainQueueSetup {
    return YES;
}

@end
