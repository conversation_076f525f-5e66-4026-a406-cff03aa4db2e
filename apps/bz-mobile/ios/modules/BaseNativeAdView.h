#import <UIKit/UIKit.h>
#import <React/RCTView.h>
#import <React/RCTComponent.h>
#import <InMobiSDK/InMobiSDK.h>
#import "InMobiPackage.h"

@interface BaseNativeAdView : RCTView <IMNativeDelegate>

@property (nonatomic, strong) IMNative *nativeAd;
@property (nonatomic, assign) long long placementId;
@property (nonatomic, assign) BOOL autoShowOnLoad;
@property (nonatomic, assign) long long finalCalculatedHeight;
@property (nonatomic, strong) InMobiPackage *inMobiPackage;

// Event properties
@property (nonatomic, copy) RCTBubblingEventBlock onAdLoaded;
@property (nonatomic, copy) RCTBubblingEventBlock onAdLoadFailed;
@property (nonatomic, copy) RCTBubblingEventBlock onAdClicked;
@property (nonatomic, copy) RCTBubblingEventBlock onAdImpression;
@property (nonatomic, copy) RCTBubblingEventBlock onAdStatusChanged;
@property (nonatomic, copy) RCTBubblingEventBlock onAdShown;

- (instancetype)initWithFrame:(CGRect)frame;
- (instancetype)initWithFrame:(CGRect)frame inMobiPackage:(InMobiPackage *)inMobiPackage;
- (void)setPlacementId:(long long)placementId;
- (void)setAutoShowOnLoad:(BOOL)autoShowOnLoad;
- (void)load;
- (void)show;
- (void)click;
- (void)destroy;
- (void)emit:(NSString *)event params:(NSDictionary *)params;

@end
