#import "InFeedAdView.h"

@implementation InFeedAdView {
    NSString *_placementId;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupInFeedAd];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame inMobiPackage:(InMobiPackage *)inMobiPackage {
    self = [super initWithFrame:frame inMobiPackage:inMobiPackage];
    if (self) {
        [self setupInFeedAd];
    }
    return self;
}

- (void)setupInFeedAd {
    // In-feed specific setup if needed
    self.backgroundColor = [UIColor clearColor];
}

- (void)setPlacementId:(NSString *)placementId {
    _placementId = placementId;

    // Convert string to long long and set it in the parent class
    if (placementId && placementId.length > 0) {
        long long placementIdLong = [placementId longLongValue];
        [super setPlacementId:placementIdLong];
        NSLog(@"[InFeedAdView] Set placement ID: %@ -> %lld", placementId, placementIdLong);
    } else {
        NSLog(@"[InFeedAdView] Invalid placement ID: %@", placementId);
    }
}

- (NSString *)placementId {
    return _placementId;
}

@end


