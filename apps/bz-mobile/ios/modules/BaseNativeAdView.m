#import "BaseNativeAdView.h"
#import "InMobiPackage.h"
#import <React/RCTEventDispatcher.h>
#import <React/RCTUtils.h>

@implementation BaseNativeAdView {
    // Private instance variables to avoid infinite recursion in getter/setter methods
    long long _placementId;
    BOOL _autoShowOnLoad;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _placementId = -1;
        _autoShowOnLoad = YES;
        [self initView];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame inMobiPackage:(InMobiPackage *)inMobiPackage {
    self = [super initWithFrame:frame];
    if (self) {
        _placementId = -1;
        _autoShowOnLoad = YES;
        self.inMobiPackage = inMobiPackage;
        [self initView];
    }
    return self;
}

- (void)initView {
    self.backgroundColor = [UIColor clearColor];
    self.clipsToBounds = NO;

    #ifdef DEBUG
    self.backgroundColor = [UIColor colorWithRed:0.0 green:1.0 blue:0.0 alpha:0.2];
    #endif
}

- (void)setPlacementId:(long long)placementId {
    _placementId = placementId;
}

- (long long)placementId {
    return _placementId;
}

- (void)setAutoShowOnLoad:(BOOL)autoShowOnLoad {
    _autoShowOnLoad = autoShowOnLoad;
}

- (BOOL)autoShowOnLoad {
    return _autoShowOnLoad;
}

- (CGFloat)targetWidth {
    CGFloat width = self.frame.size.width > 0 ? self.frame.size.width : [UIScreen mainScreen].bounds.size.width;
    return MAX(1, width);
}

- (void)emit:(NSString *)event params:(NSDictionary *)params {
    if (params == nil) {
        params = @{};
    }

    // Call the appropriate event block directly
    if ([event isEqualToString:@"onAdLoaded"] && self.onAdLoaded) {
        self.onAdLoaded(params);
    } else if ([event isEqualToString:@"onAdLoadFailed"] && self.onAdLoadFailed) {
        self.onAdLoadFailed(params);
    } else if ([event isEqualToString:@"onAdClicked"] && self.onAdClicked) {
        self.onAdClicked(params);
    } else if ([event isEqualToString:@"onAdImpression"] && self.onAdImpression) {
        self.onAdImpression(params);
    } else if ([event isEqualToString:@"onAdStatusChanged"] && self.onAdStatusChanged) {
        self.onAdStatusChanged(params);
    } else if ([event isEqualToString:@"onAdShown"] && self.onAdShown) {
        self.onAdShown(params);
    }

    NSLog(@"BaseNativeAdView: Emitting event %@ with params: %@", event, params);
}

- (void)load {
    if (_placementId <= 0) {
        NSLog(@"BaseNativeAdView: Invalid placement ID");
        return;
    }

    self.nativeAd = [[IMNative alloc] initWithPlacementId:_placementId];
    NSLog(@"Loading InMobi native ad for placement id : %lld", _placementId);
    self.nativeAd.delegate = self;
    [self.nativeAd load];
}

- (void)show {
  if (self.nativeAd && self.nativeAd.isReady) {
        UIView *adView = [self.nativeAd primaryViewOfWidth:self.targetWidth];
        if (adView) {
            [self addSubview:adView];
            adView.frame = self.bounds;
          NSLog(@"InMobi view loaded with frame : %f, %f, %f, %f", adView.frame.origin.x, adView.frame.origin.y, adView.frame.size.width, adView.frame.size.height);
//            adView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;

            [self emit:@"onAdShown" params:nil];
        }
    }
}

- (void)click {
    // Handle ad click if needed
    [self emit:@"onAdClicked" params:nil];
}

- (void)destroy {
    if (self.nativeAd) {
        self.nativeAd.delegate = nil;
        self.nativeAd = nil;
    }

    for (UIView *subview in self.subviews) {
        [subview removeFromSuperview];
    }
}

#pragma mark - IMNativeDelegate

- (void)nativeDidFinishLoading:(IMNative *)native {
    [self emit:@"onAdLoaded" params:nil];
    if (_autoShowOnLoad) {
        [self show];
    }
}

- (void)native:(IMNative *)native didFailToLoadWithError:(IMRequestStatus *)status {
    NSDictionary *params = @{
      @"error": status.description ?: @"Unknown error"
    };
    [self emit:@"onAdLoadFailed" params:params];
}

- (void)nativeWillPresentScreen:(IMNative *)native {
    [self emit:@"onAdImpression" params:nil];
}

- (void)nativeDidPresentScreen:(IMNative *)native {
    [self emit:@"onAdShown" params:nil];
}

- (void)nativeWillDismissScreen:(IMNative *)native {
    // Handle dismiss if needed
}

- (void)nativeDidDismissScreen:(IMNative *)native {
    // Handle dismiss if needed
}

- (void)userWillLeaveApplicationFromNative:(IMNative *)native {
    [self emit:@"onAdClicked" params:nil];
}

@end
