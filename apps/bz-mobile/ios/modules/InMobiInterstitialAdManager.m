#import "InMobiInterstitialAdManager.h"
#import <React/RCTUtils.h>

@implementation InMobiInterstitialAdManager

@synthesize interstitialAds;
@synthesize hasListeners;
@synthesize locationMgr;


RCT_EXPORT_MODULE();

- (instancetype)init {
    self = [super init];
    if (self) {
        interstitialAds = [NSMutableDictionary dictionary];
        hasListeners = NO;
        locationMgr = [[CLLocationManager alloc] init];
    }
    return self;
}

- (NSArray<NSString *> *)supportedEvents {
    return @[
        @"inMobiInterstitialAdLoaded",
        @"inMobiInterstitialAdLoadFailed",
        @"inMobiInterstitialAdWillPresent",
        @"inMobiInterstitialAdDidPresent",
        @"inMobiInterstitialAdPresentFailed",
        @"inMobiInterstitialAdDidDismiss",
        @"inMobiInterstitialAdImpression",
        @"inMobiInterstitialAdClicked",
        @"inMobiInterstitialAdUserLeftApplication",

        @"inMobiCMPOnActionButtonClicked",
        @"inMobiCMPOnCCPAConsentGiven",
        @"inMobiCMPOnCMPUIStatusChanged",
        @"inMobiCMPOnCmpError",
        @"inMobiCMPOnCmpLoaded",
        @"inMobiCMPOnGoogleBasicConsentChange",
        @"inMobiCMPOnGoogleVendorConsentGiven",
        @"inMobiCMPOnIABVendorConsentGiven",
        @"inMobiCMPOnNonIABVendorConsentGiven",
        @"inMobiCMPOnReceiveUSRegulationsConsent",
        @"inMobiCMPOnUserMovedToOtherState",
    ];
}

- (void)startObserving {
    hasListeners = YES;
}

- (void)stopObserving {
    hasListeners = NO;
}

RCT_EXPORT_METHOD(initSDK:(NSString *)accountId
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // __weak typeof(self) weakSelf = self;

    dispatch_async(dispatch_get_main_queue(), ^{
        [IMSdk setLogLevel:IMSDKLogLevelDebug];
        [IMUnifiedIdService enableDebugMode:true];
        [IMSdk shouldAutoManageAVAudioSession:YES];

        [IMSdk initWithAccountID:accountId
           andCompletionHandler:^(NSError * _Nullable error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (error) {
                    NSLog(@"SDK Initialization Error - %@", error.description);
                    reject(@"INIT_ERROR", error.description, error);
                } else {
                    NSLog(@"IM Media SDK successfully initialized");
                    resolve(accountId);
                }
            });
        }];
    });
        self.locationMgr.desiredAccuracy = kCLLocationAccuracyHundredMeters;
        self.locationMgr.delegate = self;
        [self.locationMgr requestWhenInUseAuthorization];
}

RCT_EXPORT_METHOD(loadInterstitialAd:(NSString *)placementId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    // Implementation will require the InMobi SDK integration
    __weak typeof(self) weakSelf = self;
    // This is a placeholder for actual ad loading
    // Create and load interstitial ad
    IMInterstitial *interstitial = [[IMInterstitial alloc] initWithPlacementId:[placementId longLongValue]];
    interstitial.delegate = weakSelf;
    [interstitial load];
    weakSelf.interstitialAds[placementId] = interstitial;
    resolve(@YES);
}

RCT_EXPORT_METHOD(isInterstitialAdReady:(NSString *)placementId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        // Check if interstitial ad is ready
        IMInterstitial *interstitial = weakSelf.interstitialAds[placementId];
        BOOL isReady = [interstitial isReady];
        resolve(@(isReady));
    });
}

RCT_EXPORT_METHOD(showInterstitialAd:(NSString *)placementId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        // Show interstitial ad
        IMInterstitial *interstitial = weakSelf.interstitialAds[placementId];
        if (interstitial != nil && [interstitial isReady]) {
            UIViewController *rootViewController = RCTKeyWindow().rootViewController;
            [interstitial showFrom:rootViewController];
            resolve(@YES);
        } else {
            resolve(@NO);
        }
    });
}

// User and demographic methods
RCT_EXPORT_METHOD(setGender:(NSString *)gender) {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([gender isEqualToString:@"MALE"]) {
          [IMSdk setGender:IMSDKGenderMale];
        } else if ([gender isEqualToString:@"FEMALE"]) {
          [IMSdk setGender:IMSDKGenderFemale];
        }
    });
}

RCT_EXPORT_METHOD(setAge:(NSInteger)age) {
    dispatch_async(dispatch_get_main_queue(), ^{
        [IMSdk setAge:age];
    });
}

RCT_EXPORT_METHOD(setAgeGroup:(NSString *)ageGroup) {
    dispatch_async(dispatch_get_main_queue(), ^{
        /**
         * BELOW_18,
         * BETWEEN_18_AND_24,
         * BETWEEN_25_AND_29,
         * BETWEEN_30_AND_34,
         * BETWEEN_35_AND_44,
         * BETWEEN_45_AND_54,
         * BETWEEN_55_AND_65,
         * ABOVE_65;
         */
        if ([ageGroup isEqualToString:@"BELOW_18"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_18_AND_24"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_25_AND_29"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_30_AND_34"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_35_AND_44"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_45_AND_54"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupBelow18];
        } else if ([ageGroup isEqualToString:@"BETWEEN_55_AND_65"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupAbove65];
        } else if ([ageGroup isEqualToString:@"ABOVE_65"]) {
          [IMSdk setAgeGroup:IMSDKAgeGroupAbove65];
        }
    });
}

RCT_EXPORT_METHOD(setAgeRestricted:(BOOL)isAgeRestricted) {
    dispatch_async(dispatch_get_main_queue(), ^{
        [IMSdk setIsAgeRestricted:isAgeRestricted];
    });
}

// Location methods
RCT_EXPORT_METHOD(setLocation:(NSString *)city state:(NSString *)state country:(NSString *)country) {
    dispatch_async(dispatch_get_main_queue(), ^{
        [IMSdk setLocationWithCity:city state:state country:country];
    });
}

RCT_EXPORT_METHOD(setLocationLatLng:(double)latitude longitude:(double)longitude) {
    dispatch_async(dispatch_get_main_queue(), ^{
        CLLocation *location = [[CLLocation alloc] initWithLatitude:latitude longitude:longitude];
        [IMSdk setLocation:location];
    });
}

RCT_EXPORT_METHOD(setLastKnownLocationFromGPS:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    CLLocationManager *locationManager = [[CLLocationManager alloc] init];

    if (![CLLocationManager locationServicesEnabled]) {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:1 userInfo:@{NSLocalizedDescriptionKey: @"Location services not enabled"}];
        reject(@"LOCATION_ERROR", @"Location services not enabled", error);
        return;
    }

    CLAuthorizationStatus status;
    if (@available(iOS 14.0, *)) {
        status = [locationManager authorizationStatus];
    } else {
        // Use deprecated method for iOS 13 and below
        status = [CLLocationManager authorizationStatus];
    }

    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:2 userInfo:@{NSLocalizedDescriptionKey: @"Location permissions not granted"}];
        reject(@"LOCATION_ERROR", @"Location permissions not granted", error);
        return;
    }

    CLLocation *location = locationManager.location;
    if (location) {
        [IMSdk setLocation:location];
        NSDictionary *locationDict = @{
            @"latitude": @(location.coordinate.latitude),
            @"longitude": @(location.coordinate.longitude)
        };
        resolve(locationDict);
    } else {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:3 userInfo:@{NSLocalizedDescriptionKey: @"Failed to get location"}];
        reject(@"LOCATION_ERROR", @"Failed to get location", error);
    }
}

RCT_EXPORT_METHOD(setLastKnownLocationFromNetwork:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    // On iOS, there's no direct equivalent to Android's NETWORK_PROVIDER
    // We'll use the same implementation as GPS but with lower desired accuracy
    CLLocationManager *locationManager = [[CLLocationManager alloc] init];
    locationManager.desiredAccuracy = kCLLocationAccuracyKilometer; // Lower accuracy for network-like behavior

    if (![CLLocationManager locationServicesEnabled]) {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:1 userInfo:@{NSLocalizedDescriptionKey: @"Location services not enabled"}];
        reject(@"LOCATION_ERROR", @"Location services not enabled", error);
        return;
    }


    CLAuthorizationStatus status;
    if (@available(iOS 14.0, *)) {
        status = [locationManager authorizationStatus];
    } else {
        // Use deprecated method for iOS 13 and below
        status = [CLLocationManager authorizationStatus];
    }

    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:2 userInfo:@{NSLocalizedDescriptionKey: @"Location permissions not granted"}];
        reject(@"LOCATION_ERROR", @"Location permissions not granted", error);
        return;
    }

    CLLocation *location = locationManager.location;
    if (location) {
        [IMSdk setLocation:location];
        NSDictionary *locationDict = @{
            @"latitude": @(location.coordinate.latitude),
            @"longitude": @(location.coordinate.longitude)
        };
        resolve(locationDict);
    } else {
        NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:3 userInfo:@{NSLocalizedDescriptionKey: @"Failed to get location"}];
        reject(@"LOCATION_ERROR", @"Failed to get location", error);
    }
}

// Notifies that the ad is received with Meta info.
- (void)interstitial:(IMInterstitial *)interstitial didReceiveWithMetaInfo:(NSObject *)metaInfo {
  NSLog(@"InMobi: interstitial didReceiveAdWithMetaInfo: %@", metaInfo);
}
- (void)interstitialDidFinishLoading:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialDidFinishLoading");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdLoaded" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)interstitial:(IMInterstitial *)interstitial didFailToLoadWithError:(IMRequestStatus *)error {
    NSLog(@"InMobi: Interstitial failed to load ad");
    NSLog(@"Error : %@",error.description);
  if (self.hasListeners) {
      // Find the placementId by iterating through the dictionary
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdLoadFailed" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId], @"message":error.description}];
      }
  }
}
- (void)interstitial:(IMInterstitial *)interstitial didFailToPresentWithError:(IMRequestStatus *)error {
    NSLog(@"InMobi: Interstitial didFailToPresentWithError: %@", error.description);
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdPresentFailed" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)interstitialWillPresent:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialWillPresent");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdWillPresent" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)interstitialDidPresent:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialDidPresent");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdDidPresent" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)interstitialWillDismiss:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialWillDismiss");
}
- (void)interstitialDidDismiss:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialDidDismiss");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdDidDismiss" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)userWillLeaveApplicationFromInterstitial:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: userWillLeaveApplicationFromInterstitial");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdUserLeftApplication" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}
- (void)interstitial:(IMInterstitial *)interstitial didInteractWithParams:
(NSDictionary *)params {
    NSLog(@"InMobi: InterstitialDidInteractWithParams");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdClicked" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}

// Add impression tracking
- (void)interstitialDidReceiveAd:(IMInterstitial *)interstitial {
    NSLog(@"InMobi: interstitialDidReceiveAd - Impression received");
    if (self.hasListeners) {
      if (interstitial && interstitial.placementId) {
        [self sendEventWithName:@"inMobiInterstitialAdImpression" body:@{@"placementId": [NSString stringWithFormat:@"%lld", interstitial.placementId]}];
      }
    }
}

- (void)cmpDidErrorWithError:(NSError * _Nonnull)error {
    if (self.hasListeners) {
        [self sendEventWithName:@"inMobiCMPOnCmpError" body:@{@"message": error.localizedDescription}];
        NSLog(@"ChoiceCmp: onCmpError - %@", error.localizedDescription);
    }
}
- (void)cmpDidLoadWithInfo:(PingResponse * _Nonnull)info {
    if (self.hasListeners) {
          [self sendEventWithName:@"inMobiCMPOnCmpLoaded" body:@{@"cmpId": [NSString stringWithFormat:@"%ld", (long)info.cmpId]}];
        NSLog(@"ChoiceCmp: onCmpLoaded - %@", [NSString stringWithFormat:@"%ld", (long)info.cmpId]);
    }
}
- (void)cmpUIStatusChangedWithInfo:(DisplayInfo * _Nonnull)info {
    if (self.hasListeners) {
          [self sendEventWithName:@"inMobiCMPOnCMPUIStatusChanged" body:@{@"status": [NSString stringWithFormat:@"%ld", (long)info.displayStatus]}];
          NSLog(@"ChoiceCmp: onCMPUIStatusChanged - %@", [NSString stringWithFormat:@"%ld", (long)info.displayStatus]);
        }
}
- (void)didReceiveAdditionalConsentWithAcData:(ACData * _Nonnull)acData updated:(BOOL)updated {
    if (self.hasListeners) {
        [self sendEventWithName:@"inMobiCMPOnGoogleVendorConsentGiven" body:@{@"acData": acData.acString}];
        NSLog(@"ChoiceCmp: onGoogleVendorConsentGiven - %@", acData.acString);
    }
}
- (void)didReceiveIABVendorConsentWithGdprData:(GDPRData * _Nonnull)gdprData updated:(BOOL)updated {
    if (self.hasListeners) {
          [self sendEventWithName:@"inMobiCMPOnIABVendorConsentGiven" body:@{@"gdprData": [NSString stringWithFormat:@"%ld", (long)gdprData.gppString]}];
        NSLog(@"ChoiceCmp: onIABVendorConsentGiven - %@", [NSString stringWithFormat:@"%ld", (long)gdprData.gppString]);
    }
}
- (void)didReceiveNonIABVendorConsentWithNonIabData:(NonIABData * _Nonnull)nonIabData updated:(BOOL)updated {
    if (self.hasListeners) {
          [self sendEventWithName:@"inMobiCMPOnNonIABVendorConsentGiven" body:@{@"nonIABData": nonIabData.description}];
        NSLog(@"ChoiceCmp: onNonIABVendorConsentGiven - %@", nonIabData.description);
    }
}
- (void)didReceiveCCPAConsent:(NSString * _Nonnull)string {
    if (self.hasListeners) {
        [self sendEventWithName:@"inMobiCMPOnCCPAConsentGiven" body:@{@"consentString": string}];
        NSLog(@"ChoiceCmp: onCCPAConsentGiven - %@", string);
    }
}
- (void)didReceiveUSRegulationsConsentWithUsRegData:(USRegulationsData * _Nonnull)usRegData {
    if (self.hasListeners) {
        [self sendEventWithName:@"inMobiCMPOnReceiveUSRegulationsConsent" body:@{@"usRegulationData": usRegData.description}];
        NSLog(@"ChoiceCmp: onReceiveUSRegulationsConsent - %@", usRegData.description);
    }
}

- (void)userDidMoveToOtherState {
    if (self.hasListeners) {
        [self sendEventWithName:@"inMobiCMPOnUserMovedToOtherState" body:@{}];
        NSLog(@"ChoiceCmp: onUserMovedToOtherState");
    }
}

- (void)didReceiveActionButtonTapWithAction:(enum ActionButtons)action {
    if (self.hasListeners) {
      [self sendEventWithName:@"inMobiCMPOnActionButtonClicked" body:@{@"actionButton": [NSString stringWithFormat:@"%ld", (long)action]}];
        NSLog(@"ChoiceCmp: onActionButtonClicked - %@", [NSString stringWithFormat:@"%ld", (long)action]);
    }
}

- (void)didReceiveCCPAConsentWithString:(NSString * _Nonnull)consentString {
  if (self.hasListeners) {
    [self sendEventWithName:@"inMobiCMPOnCCPAConsentGiven" body:@{@"consentString": consentString}];
    NSLog(@"ChoiceCmp: didReceiveCCPAConsentWithString - %@", consentString);
  }
}

RCT_EXPORT_METHOD(initCMPSDK:(NSString *)cmpCode resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *bundleId = [[NSBundle mainBundle] bundleIdentifier];
        NSLog(@"ChoiceCmp: initCMPSDK - %@", bundleId);

      [[ChoiceCmp shared] startChoiceWithPcode:cmpCode delegate:self ccpaDelegate:self shouldDisplayIDFA:false];
      resolve(@YES);
    });
}

RCT_EXPORT_METHOD(showCMPPreferences) {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIViewController *rootViewController = RCTKeyWindow().rootViewController;
        if (rootViewController) {
          [[ChoiceCmp shared] forceDisplayUI];
        } else {
            NSLog(@"InMobiInterstitialAdManager: rootViewController is null while showCMPPreferences");
        }
    });
}

#pragma mark - CLLocationManagerDelegate
-(void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager {
  CLAuthorizationStatus status;
  if (@available(iOS 14.0, *)) {
      status = [manager authorizationStatus];
  } else {
      // Use deprecated method for iOS 13 and below
      status = [CLLocationManager authorizationStatus];
  }

  if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
    NSError *error = [NSError errorWithDomain:@"com.benzinga.app.inmobi" code:2 userInfo:@{NSLocalizedDescriptionKey: @"Location permissions not granted"}];
    NSLog(@"InMobo Location update error: %@", error);
    return;
  }

  [self.locationMgr startUpdatingLocation];

}

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations {
    CLLocation *clLocation = locations.lastObject;
    [IMSdk setLocation:clLocation];
    [self.locationMgr stopUpdatingLocation];
}


@end
