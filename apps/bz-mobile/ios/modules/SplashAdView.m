#import "SplashAdView.h"

@implementation SplashAdView {
    NSString *_placementId;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSplashAd];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame inMobiPackage:(InMobiPackage *)inMobiPackage {
    self = [super initWithFrame:frame inMobiPackage:inMobiPackage];
    if (self) {
        [self setupSplashAd];
    }
    return self;
}

- (void)setupSplashAd {
    // Splash ad specific setup
    self.backgroundColor = [UIColor clearColor];
    self.autoShowOnLoad = YES; // Splash ads typically auto-show
}

- (void)setPlacementId:(NSString *)placementId {
    _placementId = placementId;

    // Convert string to long long and set it in the parent class
    if (placementId && placementId.length > 0) {
        long long placementIdLong = [placementId longLongValue];
        [super setPlacementId:placementIdLong];
        NSLog(@"[SplashAdView] Set placement ID: %@ -> %lld", placementId, placementIdLong);
    } else {
        NSLog(@"[SplashAdView] Invalid placement ID: %@", placementId);
    }
}

- (NSString *)placementId {
    return _placementId;
}

@end


