PODS:
  - boost (1.83.0)
  - connatix-player-sdk-react-native (2.4.6):
    - ConnatixPlayerSDKObjc (= 4.3.24)
    - React-Core
  - ConnatixAppMeasurement (1.5.3)
  - ConnatixPlayerSDKObjc (4.3.24):
    - ConnatixAppMeasurement (= 1.5.3)
  - DoubleConversion (1.1.6)
  - EASClient (0.12.0):
    - ExpoModulesCore
  - EXApplication (5.9.1):
    - ExpoModulesCore
  - EXAV (14.0.7):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (16.0.2):
    - ExpoModulesCore
  - EXImageLoader (4.7.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.13.1)
  - EXManifests (0.14.3):
    - ExpoModulesCore
  - EXNotifications (0.28.16):
    - ExpoModulesCore
  - Expo (51.0.39):
    - ExpoModulesCore
  - expo-dev-client (4.0.29):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (4.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Main (= 4.0.29)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Main (4.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Unsafe (4.0.29):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu (5.0.23):
    - DoubleConversion
    - expo-dev-menu/Main (= 5.0.23)
    - expo-dev-menu/ReactNativeCompatibles (= 5.0.23)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu-interface (1.8.4)
  - expo-dev-menu/Main (5.0.23):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (5.0.23):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/SafeAreaView (5.0.23):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/Vendored (5.0.23):
    - DoubleConversion
    - expo-dev-menu/SafeAreaView
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAppleAuthentication (6.4.2):
    - ExpoModulesCore
  - ExpoAsset (10.0.10):
    - ExpoModulesCore
  - ExpoBlur (13.0.3):
    - ExpoModulesCore
  - ExpoClipboard (6.0.3):
    - ExpoModulesCore
  - ExpoCrypto (13.0.2):
    - ExpoModulesCore
  - ExpoDevice (6.0.2):
    - ExpoModulesCore
  - ExpoDocumentPicker (12.0.2):
    - ExpoModulesCore
  - ExpoFileSystem (17.0.1):
    - ExpoModulesCore
  - ExpoFont (12.0.10):
    - ExpoModulesCore
  - ExpoHaptics (13.0.1):
    - ExpoModulesCore
  - ExpoImageManipulator (12.0.5):
    - EXImageLoader
    - ExpoModulesCore
    - SDWebImageWebPCoder
  - ExpoImagePicker (15.0.7):
    - ExpoModulesCore
  - ExpoKeepAwake (13.0.2):
    - ExpoModulesCore
  - ExpoLinearGradient (13.0.2):
    - ExpoModulesCore
  - ExpoMediaLibrary (16.0.4):
    - ExpoModulesCore
    - React-Core
  - ExpoModulesCore (1.12.26):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoRandom (14.0.1):
    - ExpoModulesCore
  - ExpoSecureStore (13.0.2):
    - ExpoModulesCore
  - ExpoSharing (12.0.1):
    - ExpoModulesCore
  - ExpoStoreReview (7.0.2):
    - ExpoModulesCore
  - ExpoSystemUI (3.0.7):
    - ExpoModulesCore
  - ExpoWebBrowser (13.0.3):
    - ExpoModulesCore
  - EXSplashScreen (0.27.7):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - EXStructuredHeaders (3.8.0)
  - EXUpdates (0.25.28):
    - DoubleConversion
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - ReachabilitySwift
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - "sqlite3 (~> 3.45.3+1)"
    - Yoga
  - EXUpdatesInterface (0.16.2):
    - ExpoModulesCore
  - FBLazyVector (0.74.5)
  - Firebase/AnalyticsWithoutAdIdSupport (10.24.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 10.24.0)
  - Firebase/CoreOnly (10.24.0):
    - FirebaseCore (= 10.24.0)
  - FirebaseAnalytics/WithoutAdIdSupport (10.24.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.24.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.24.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - fmt (9.1.0)
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (11.7.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.24.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleUserMessagingPlatform (2.5.0)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.74.5):
    - hermes-engine/Pre-built (= 0.74.5)
  - hermes-engine/Pre-built (0.74.5)
  - InMobiCMP (2.2.0)
  - InMobiSDK (10.8.3)
  - JitsiWebRTC (124.0.2)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.0.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - lottie-ios (= 4.5.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.74.5)
  - RCTRequired (0.74.5)
  - RCTTypeSafety (0.74.5):
    - FBLazyVector (= 0.74.5)
    - RCTRequired (= 0.74.5)
    - React-Core (= 0.74.5)
  - ReachabilitySwift (5.2.4)
  - React (0.74.5):
    - React-Core (= 0.74.5)
    - React-Core/DevSupport (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-RCTActionSheet (= 0.74.5)
    - React-RCTAnimation (= 0.74.5)
    - React-RCTBlob (= 0.74.5)
    - React-RCTImage (= 0.74.5)
    - React-RCTLinking (= 0.74.5)
    - React-RCTNetwork (= 0.74.5)
    - React-RCTSettings (= 0.74.5)
    - React-RCTText (= 0.74.5)
    - React-RCTVibration (= 0.74.5)
  - React-callinvoker (0.74.5)
  - React-Codegen (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.74.5)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.74.5)
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor (= 0.74.5)
  - React-debug (0.74.5)
  - React-Fabric (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.74.5)
    - React-Fabric/attributedstring (= 0.74.5)
    - React-Fabric/componentregistry (= 0.74.5)
    - React-Fabric/componentregistrynative (= 0.74.5)
    - React-Fabric/components (= 0.74.5)
    - React-Fabric/core (= 0.74.5)
    - React-Fabric/imagemanager (= 0.74.5)
    - React-Fabric/leakchecker (= 0.74.5)
    - React-Fabric/mounting (= 0.74.5)
    - React-Fabric/scheduler (= 0.74.5)
    - React-Fabric/telemetry (= 0.74.5)
    - React-Fabric/templateprocessor (= 0.74.5)
    - React-Fabric/textlayoutmanager (= 0.74.5)
    - React-Fabric/uimanager (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.74.5)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.74.5)
    - React-Fabric/components/modal (= 0.74.5)
    - React-Fabric/components/rncore (= 0.74.5)
    - React-Fabric/components/root (= 0.74.5)
    - React-Fabric/components/safeareaview (= 0.74.5)
    - React-Fabric/components/scrollview (= 0.74.5)
    - React-Fabric/components/text (= 0.74.5)
    - React-Fabric/components/textinput (= 0.74.5)
    - React-Fabric/components/unimplementedview (= 0.74.5)
    - React-Fabric/components/view (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.74.5)
    - RCTTypeSafety (= 0.74.5)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.74.5)
  - React-graphics (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core/Default (= 0.74.5)
    - React-utils
  - React-hermes (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor
  - React-ImageManager (0.74.5):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.74.5):
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
  - React-jsinspector (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-runtimeexecutor (= 0.74.5)
  - React-jsitracing (0.74.5):
    - React-jsi
  - React-logger (0.74.5):
    - glog
  - React-Mapbuffer (0.74.5):
    - glog
    - React-debug
  - react-native-get-random-values (1.11.0):
    - React-Core
  - react-native-netinfo (11.3.1):
    - React-Core
  - react-native-pager-view (6.3.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.10.5):
    - React-Core
  - react-native-track-player (4.1.1):
    - React-Core
    - SwiftAudioEx (= 1.1.0)
  - react-native-webrtc (124.0.3):
    - JitsiWebRTC (~> 124.0.0)
    - React-Core
  - react-native-webview (13.8.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.74.5)
  - React-NativeModulesApple (0.74.5):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.74.5)
  - React-RCTActionSheet (0.74.5):
    - React-Core/RCTActionSheetHeaders (= 0.74.5)
  - React-RCTAnimation (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-CoreModules
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.74.5):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.74.5)
  - React-RCTNetwork (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.74.5):
    - React-Core/RCTTextHeaders (= 0.74.5)
    - Yoga
  - React-RCTVibration (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.74.5)
  - React-RuntimeApple (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-utils
  - React-RuntimeCore (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.74.5):
    - React-jsi (= 0.74.5)
  - React-RuntimeHermes (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.74.5)
  - ReactCommon (0.74.5):
    - ReactCommon/turbomodule (= 0.74.5)
  - ReactCommon/turbomodule (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - ReactCommon/turbomodule/bridging (= 0.74.5)
    - ReactCommon/turbomodule/core (= 0.74.5)
  - ReactCommon/turbomodule/bridging (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
  - ReactCommon/turbomodule/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-utils (= 0.74.5)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNDeviceInfo (10.13.2):
    - React-Core
  - RNFBAnalytics (19.2.2):
    - Firebase/AnalyticsWithoutAdIdSupport (= 10.24.0)
    - React-Core
    - RNFBApp
  - RNFBApp (19.2.2):
    - Firebase/CoreOnly (= 10.24.0)
    - React-Core
  - RNGestureHandler (2.16.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGoogleMobileAds (14.1.0):
    - DoubleConversion
    - glog
    - Google-Mobile-Ads-SDK (= 11.7.0)
    - GoogleUserMessagingPlatform (= 2.5.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNIap (12.15.1):
    - React-Core
  - RNImageCropPicker (0.41.2):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.2)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.2):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNPermissions (4.1.5):
    - React-Core
  - RNReanimated (3.10.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.31.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.1.0):
    - React-Core
  - RNVectorIcons (10.1.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - segment-analytics-react-native (2.19.1):
    - React-Core
    - sovran-react-native
  - SocketRocket (0.7.0)
  - sovran-react-native (1.1.1):
    - React-Core
  - "sqlite3 (3.45.3+1)":
    - "sqlite3/common (= 3.45.3+1)"
  - "sqlite3/common (3.45.3+1)"
  - SwiftAudioEx (1.1.0)
  - taboola-react-native-taboola (3.1.5):
    - React-Core
    - TaboolaSDK (= 3.8.25)
  - TaboolaSDK (3.8.25)
  - TOCropViewController (2.7.4)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - connatix-player-sdk-react-native (from `../../../node_modules/connatix-player-sdk-react-native`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EASClient (from `../../../node_modules/expo-eas-client/ios`)
  - EXApplication (from `../../../node_modules/expo-application/ios`)
  - EXAV (from `../../../node_modules/expo-av/ios`)
  - EXConstants (from `../../../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../../../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../../../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../../../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../../../node_modules/expo-notifications/ios`)
  - Expo (from `../../../node_modules/expo`)
  - expo-dev-client (from `../../../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../../../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../../../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../../../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAppleAuthentication (from `../../../node_modules/expo-apple-authentication/ios`)
  - ExpoAsset (from `../../../node_modules/expo-asset/ios`)
  - ExpoBlur (from `../../../node_modules/expo-blur/ios`)
  - ExpoClipboard (from `../../../node_modules/expo-clipboard/ios`)
  - ExpoCrypto (from `../../../node_modules/expo-crypto/ios`)
  - ExpoDevice (from `../../../node_modules/expo-device/ios`)
  - ExpoDocumentPicker (from `../../../node_modules/expo-document-picker/ios`)
  - ExpoFileSystem (from `../../../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../../../node_modules/expo-font/ios`)
  - ExpoHaptics (from `../../../node_modules/expo-haptics/ios`)
  - ExpoImageManipulator (from `../../../node_modules/expo-image-manipulator/ios`)
  - ExpoImagePicker (from `../../../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../../../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../../../node_modules/expo-linear-gradient/ios`)
  - ExpoMediaLibrary (from `../../../node_modules/expo-media-library/ios`)
  - ExpoModulesCore (from `../../../node_modules/expo-modules-core`)
  - ExpoRandom (from `../../../node_modules/expo-random/ios`)
  - ExpoSecureStore (from `../../../node_modules/expo-secure-store/ios`)
  - ExpoSharing (from `../../../node_modules/expo-sharing/ios`)
  - ExpoStoreReview (from `../../../node_modules/expo-store-review/ios`)
  - ExpoSystemUI (from `../../../node_modules/expo-system-ui/ios`)
  - ExpoWebBrowser (from `../../../node_modules/expo-web-browser/ios`)
  - EXSplashScreen (from `../../../node_modules/expo-splash-screen/ios`)
  - EXStructuredHeaders (from `../../../node_modules/expo-structured-headers/ios`)
  - EXUpdates (from `../../../node_modules/expo-updates/ios`)
  - EXUpdatesInterface (from `../../../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - FirebaseCore
  - fmt (from `../../../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - InMobiCMP (= 2.2.0)
  - InMobiSDK
  - lottie-react-native (from `../../../node_modules/lottie-react-native`)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../../../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../../../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-graphics (from `../../../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../../../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../../../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../../../node_modules/react-native/ReactCommon`)
  - react-native-get-random-values (from `../../../node_modules/react-native-get-random-values`)
  - "react-native-netinfo (from `../../../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../../../node_modules/react-native-pager-view`)
  - react-native-render-html (from `../../../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - react-native-track-player (from `../../../node_modules/react-native-track-player`)
  - react-native-webrtc (from `../../../node_modules/react-native-webrtc`)
  - react-native-webview (from `../../../node_modules/react-native-webview`)
  - React-nativeconfig (from `../../../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../../../node_modules/react-native/React`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../../../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../../../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - RNDeviceInfo (from `../../../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../../../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../../../node_modules/@react-native-firebase/app`)"
  - RNGestureHandler (from `../../../node_modules/react-native-gesture-handler`)
  - RNGoogleMobileAds (from `../../../node_modules/react-native-google-mobile-ads`)
  - RNIap (from `../../../node_modules/react-native-iap`)
  - RNImageCropPicker (from `../../../node_modules/react-native-image-crop-picker`)
  - RNPermissions (from `../../../node_modules/react-native-permissions`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - RNVectorIcons (from `../../../node_modules/react-native-vector-icons`)
  - "segment-analytics-react-native (from `../../../node_modules/@segment/analytics-react-native`)"
  - "sovran-react-native (from `../../../node_modules/@segment/sovran-react-native`)"
  - "taboola-react-native-taboola (from `../../../node_modules/@taboola/react-native-plugin-3x`)"
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - ConnatixAppMeasurement
    - ConnatixPlayerSDKObjc
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - InMobiCMP
    - InMobiSDK
    - JitsiWebRTC
    - libwebp
    - lottie-ios
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - sqlite3
    - SwiftAudioEx
    - TaboolaSDK
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  connatix-player-sdk-react-native:
    :path: "../../../node_modules/connatix-player-sdk-react-native"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EASClient:
    :path: "../../../node_modules/expo-eas-client/ios"
  EXApplication:
    :path: "../../../node_modules/expo-application/ios"
  EXAV:
    :path: "../../../node_modules/expo-av/ios"
  EXConstants:
    :path: "../../../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../../../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../../../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../../../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../../../node_modules/expo-notifications/ios"
  Expo:
    :path: "../../../node_modules/expo"
  expo-dev-client:
    :path: "../../../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../../../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../../../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../../../node_modules/expo-dev-menu-interface/ios"
  ExpoAppleAuthentication:
    :path: "../../../node_modules/expo-apple-authentication/ios"
  ExpoAsset:
    :path: "../../../node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../../../node_modules/expo-blur/ios"
  ExpoClipboard:
    :path: "../../../node_modules/expo-clipboard/ios"
  ExpoCrypto:
    :path: "../../../node_modules/expo-crypto/ios"
  ExpoDevice:
    :path: "../../../node_modules/expo-device/ios"
  ExpoDocumentPicker:
    :path: "../../../node_modules/expo-document-picker/ios"
  ExpoFileSystem:
    :path: "../../../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../../../node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../../../node_modules/expo-haptics/ios"
  ExpoImageManipulator:
    :path: "../../../node_modules/expo-image-manipulator/ios"
  ExpoImagePicker:
    :path: "../../../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../../../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../../../node_modules/expo-linear-gradient/ios"
  ExpoMediaLibrary:
    :path: "../../../node_modules/expo-media-library/ios"
  ExpoModulesCore:
    :path: "../../../node_modules/expo-modules-core"
  ExpoRandom:
    :path: "../../../node_modules/expo-random/ios"
  ExpoSecureStore:
    :path: "../../../node_modules/expo-secure-store/ios"
  ExpoSharing:
    :path: "../../../node_modules/expo-sharing/ios"
  ExpoStoreReview:
    :path: "../../../node_modules/expo-store-review/ios"
  ExpoSystemUI:
    :path: "../../../node_modules/expo-system-ui/ios"
  ExpoWebBrowser:
    :path: "../../../node_modules/expo-web-browser/ios"
  EXSplashScreen:
    :path: "../../../node_modules/expo-splash-screen/ios"
  EXStructuredHeaders:
    :path: "../../../node_modules/expo-structured-headers/ios"
  EXUpdates:
    :path: "../../../node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../../../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-06-28-RNv0.74.3-7bda0c267e76d11b68a585f84cfdd65000babf85
  lottie-react-native:
    :path: "../../../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../../../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../../../node_modules/react-native/ReactCommon/react/featureflags"
  React-graphics:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../../../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../../../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../../../node_modules/react-native/ReactCommon"
  react-native-get-random-values:
    :path: "../../../node_modules/react-native-get-random-values"
  react-native-netinfo:
    :path: "../../../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../../../node_modules/react-native-pager-view"
  react-native-render-html:
    :path: "../../../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  react-native-track-player:
    :path: "../../../node_modules/react-native-track-player"
  react-native-webrtc:
    :path: "../../../node_modules/react-native-webrtc"
  react-native-webview:
    :path: "../../../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../../../node_modules/react-native/React"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../../../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNDeviceInfo:
    :path: "../../../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../../../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../../../node_modules/@react-native-firebase/app"
  RNGestureHandler:
    :path: "../../../node_modules/react-native-gesture-handler"
  RNGoogleMobileAds:
    :path: "../../../node_modules/react-native-google-mobile-ads"
  RNIap:
    :path: "../../../node_modules/react-native-iap"
  RNImageCropPicker:
    :path: "../../../node_modules/react-native-image-crop-picker"
  RNPermissions:
    :path: "../../../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../../../node_modules/react-native-vector-icons"
  segment-analytics-react-native:
    :path: "../../../node_modules/@segment/analytics-react-native"
  sovran-react-native:
    :path: "../../../node_modules/@segment/sovran-react-native"
  taboola-react-native-taboola:
    :path: "../../../node_modules/@taboola/react-native-plugin-3x"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  connatix-player-sdk-react-native: fd56aaed798f8fed7bdd37a7827b1153439eec0b
  ConnatixAppMeasurement: 1d6c2c0867252c75a4a5fce469ec0b15340681b5
  ConnatixPlayerSDKObjc: 3d93c938ce2e58149867f089096e59e24e8912d1
  DoubleConversion: 76ab83afb40bddeeee456813d9c04f67f78771b5
  EASClient: 95592393d6d1e60609f0afb373191d918c687b98
  EXApplication: ec862905fdab3a15bf6bd8ca1a99df7fc02d7762
  EXAV: 64e72329d2f8c2ba13608fed4a713af4e793242d
  EXConstants: 89d35611505a8ce02550e64e43cd05565da35f9a
  EXImageLoader: 1fe96c70cdc78bedc985ec4b1fab5dd8e67dc38b
  EXJSONUtils: 30c17fd9cc364d722c0946a550dfbf1be92ef6a4
  EXManifests: ebb7f551c340c0d06f3ecd9ae662e418bf68417c
  EXNotifications: d1d6f3b9a86da60e5f1bffb917fab6789bf94cce
  Expo: ed0a748eb6be0efd2c3df7f6de3f3158a14464c9
  expo-dev-client: 67bfa449de8ee3e3cd88a6945a8058819fffb0f0
  expo-dev-launcher: 5db274c8fdf72ca2f98b189d62924b014f7b90b5
  expo-dev-menu: 8b0ea59392e2dd975390ea6f0472ce350d94866a
  expo-dev-menu-interface: 5c6b79875bf0ab1251ea9962f60968fe39ed2637
  ExpoAppleAuthentication: 8a661b6f4936affafd830f983ac22463c936dad5
  ExpoAsset: 286fee7ba711ce66bf20b315e68106b13b8629fc
  ExpoBlur: 99901a4531f5d3ac4a19b362907b8f75da4ed9c8
  ExpoClipboard: 243e22ff4161bbffcd3d2db469ae860ddc1156be
  ExpoCrypto: c5c052d5f9f668c21975cb4caf072cec23c823fa
  ExpoDevice: 84b3ed79df1234c17edfbf335f6ecf3c636f74de
  ExpoDocumentPicker: 3204d5169a090ddbb23c4bf035bb86c3a6ea0ec2
  ExpoFileSystem: 2988caaf68b7cb706e36d382829d99811d9d76a5
  ExpoFont: 38dddf823e32740c2a9f37c926a33aeca736b5c4
  ExpoHaptics: 9f47be324f691b6291c17c216189ab832d1a4d69
  ExpoImageManipulator: eb92d3ac4bad0fe96258f17273b9e295e87447be
  ExpoImagePicker: 517a47896adf5d55d0a1c159e5d1e312af12e57c
  ExpoKeepAwake: dd02e65d49f1cfd9194640028ae2857e536eb1c9
  ExpoLinearGradient: 4c44b3803b441724874b232e6520b51ca6a50db1
  ExpoMediaLibrary: 157fda748932e15c09dfdd0ef33be8d13c57def8
  ExpoModulesCore: 9ac73e2f60e0ea1d30137ca96cfc8c2aa34ef2b2
  ExpoRandom: d1444df65007bdd4070009efd5dab18e20bf0f00
  ExpoSecureStore: 6506992a9f53c94ea716c54d4a63144965945c2c
  ExpoSharing: 5e6b6cbc0c232084b79ffa7243459f7dcdc5b1cb
  ExpoStoreReview: 5ce23b11d7cdcba23fa26b8cd9dd83765e2ac7bf
  ExpoSystemUI: 2072307375696c398a5d75633bdd5143fadc3d26
  ExpoWebBrowser: cf10afe886891ab495877dada977fe6c269614a4
  EXSplashScreen: a4ce3dd5d28d48e8b9132bcd9b58ee8e340db78c
  EXStructuredHeaders: cb8d1f698e144f4c5547b4c4963e1552f5d2b457
  EXUpdates: ebe3c09f7f119648a7f92295ea0d8418c799cdf6
  EXUpdatesInterface: c3a9494c2173db6442c7d5ad4e5b984972760fd3
  FBLazyVector: ac12dc084d1c8ec4cc4d7b3cf1b0ebda6dab85af
  Firebase: 91fefd38712feb9186ea8996af6cbdef41473442
  FirebaseAnalytics: b5efc493eb0f40ec560b04a472e3e1a15d39ca13
  FirebaseCore: 11dc8a16dfb7c5e3c3f45ba0e191a33ac4f50894
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  fmt: 4c2741a687cc09f0634a2e2c72a838b99f1ff120
  glog: fdfdfe5479092de0c4bdbebedd9056951f092c4f
  Google-Mobile-Ads-SDK: 204b517c9765169144cf39763c7f5d23c57a9db0
  GoogleAppMeasurement: f3abf08495ef2cba7829f15318c373b8d9226491
  GoogleUserMessagingPlatform: 6b4f48a370e77ce121d034c908cc6ee4fdafaf13
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 8c1577f3fdb849cbe7729c2e7b5abc4b845e88f8
  InMobiCMP: 1d10f2e6fe31500765161b1b0baf66cfe0ef2fd3
  InMobiSDK: 174c7910f27fb860e4f2204d621ef724efefc819
  JitsiWebRTC: b47805ab5668be38e7ee60e2258f49badfe8e1d0
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: 40f1a26ccd531a9f729d7f03922cb708a51691b1
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 5dc73daec3476616d19e8a53f0156176f7b55461
  RCTDeprecation: 3afceddffa65aee666dafd6f0116f1d975db1584
  RCTRequired: ec1239bc9d8bf63e10fb92bd8b26171a9258e0c1
  RCTTypeSafety: f5ecbc86c5c5fa163c05acb7a1c5012e15b5f994
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: fc9fa7258eff606f44d58c5b233a82dc9cf09018
  React-callinvoker: e3fab14d69607fb7e8e3a57e5a415aed863d3599
  React-Codegen: fbaeb33aefc09a9fc14eb5f783b9d49e5cbf4e37
  React-Core: c3f589f104983dec3c3eeec5e70d61aa811bc236
  React-CoreModules: 864932ddae3ead5af5bfb05f9bbc2cedcb958b39
  React-cxxreact: bd9146108c44e6dbb99bba4568ce7af0304a2419
  React-debug: d30893c49ae1bce4037ea5cd8bb2511d2a38d057
  React-Fabric: a171830e52baf8ec2b175c6a3791e01bbb92f1fb
  React-FabricImage: ad154af0067f4b5dc5a41f607e48ee343641e903
  React-featureflags: 4ae83e72d9a92452793601ac9ac7d2280e486089
  React-graphics: ed7d57965140168de86835946e8f1210c72c65dc
  React-hermes: 177b1efdf3b8f10f4ca12b624b83fb4d4ccb2884
  React-ImageManager: 3a50d0ee0bf81b1a6f23a0c5b30388293bcd6004
  React-jserrorhandler: dcd62f5ca1c724c19637595ef7f45b78018e758f
  React-jsi: 0abe1b0881b67caf8d8df6a57778dd0d3bb9d9a5
  React-jsiexecutor: f6ca8c04f19f6a3acaa9610f7fb728f39d6e3248
  React-jsinspector: db98771eae84e6f86f0ca5d9dcc572baadbfefc0
  React-jsitracing: f8367edacc50bb3f9f056a5aeafb8cee5849fafb
  React-logger: 780b9ee9cec7d44eabc4093de90107c379078cb6
  React-Mapbuffer: f544f00b98dbdd8cbae96dd2bdb8b47f719976e0
  react-native-get-random-values: d16467cf726c618e9c7a8c3c39c31faa2244bbba
  react-native-netinfo: 2e3c27627db7d49ba412bfab25834e679db41e21
  react-native-pager-view: 0f50eef500ef15dfae1f95a1c945f3d2a5ec5ade
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: df9763c5de6fa38883028e243a0b60123acb8858
  react-native-track-player: 6dc2e2633265704b8ab6d8124b80239d4ed1f911
  react-native-webrtc: a97f13953e6df748153dcb86369f51b6ccff3977
  react-native-webview: a4483a25c71098e407df1c1d9056ab907647d7c7
  React-nativeconfig: ba9a2e54e2f0882cf7882698825052793ed4c851
  React-NativeModulesApple: 84aaad2b0e546d7b839837ca537f6e72804a4cad
  React-perflogger: ed4e0c65781521e0424f2e5e40b40cc7879d737e
  React-RCTActionSheet: 49d53ff03bb5688ca4606c55859053a0cd129ea5
  React-RCTAnimation: 3075449f26cb98a52bcbf51cccd0c7954e2a71db
  React-RCTAppDelegate: 9a419c4dda9dd039ad851411546dd297b930c454
  React-RCTBlob: e81ab773a8fc1e9dceed953e889f936a7b7b3aa6
  React-RCTFabric: 47a87a3e3fa751674f7e64d0bcd58976b8c57db9
  React-RCTImage: d570531201c6dce7b5b63878fa8ecec0cc311c4c
  React-RCTLinking: af888972b925d2811633d47853c479e88c35eb4d
  React-RCTNetwork: 5728a06ff595003eca628f43f112a804f4a9a970
  React-RCTSettings: ba3665b0569714a8aaceee5c7d23b943e333fa55
  React-RCTText: b733fa984f0336b072e47512898ba91214f66ddb
  React-RCTVibration: 0cbcbbd8781b6f6123671bae9ee5dd20d621af6c
  React-rendererdebug: 9fc8f7d0bd19f2a3fe3791982af550b5e1535ff7
  React-rncore: 4013508a2f3fcf46c961919bbbd4bfdda198977e
  React-RuntimeApple: a852a6e06ab20711658873f39cb10b0033bea19d
  React-RuntimeCore: 12e5e176c0cb09926f3e6f37403a84d2e0f203a7
  React-runtimeexecutor: 0e688aefc14c6bc8601f4968d8d01c3fb6446844
  React-RuntimeHermes: 80c03a5215520c9733764ba11cbe535053c9746d
  React-runtimescheduler: 2cbd0f3625b30bba08e8768776107f6f0203159b
  React-utils: 9fa4e5d0b5e6c6c85c958f19d6ef854337886417
  ReactCommon: 9f285823dbe955099978d9bff65a7653ca029256
  RNCAsyncStorage: aa75595c1aefa18f868452091fa0c411a516ce11
  RNDeviceInfo: ff9a4419a29d343280ea19a2e381bac25cb0e146
  RNFBAnalytics: 0007c55c145aabe0e4b767fcc2677c5aa2bada53
  RNFBApp: 5ee7b01b1821f27f5749dda020fa09fa6c6975cd
  RNGestureHandler: d08fd9149ee9610bbc7e0a34c20b3ffe6a5801b3
  RNGoogleMobileAds: ce0df166b7f3e32ea8ed4ab935938b76d8ddb4d1
  RNIap: aa38c2cc8a2a75ba8112d161ca084a0b611955df
  RNImageCropPicker: d14a08134d1b5bc2d5f29a12ce3ac1d402dd2389
  RNPermissions: 2fcdf0d28d3d8f9806dd96c0355c67991a992715
  RNReanimated: b212e12a3ba71c2fd880c03cc3eee40946031e53
  RNScreens: a2d8a2555b4653d7a19706eb172f855657ac30d7
  RNSVG: a31e321979e3001f56ba9331d10ac917f8ad1851
  RNVectorIcons: f733cb2133a8e1f7dc723b97a1d70dad681124c3
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  segment-analytics-react-native: e51ba0a5699a5256deeae0638f2d157a8cebca8d
  SocketRocket: abac6f5de4d4d62d24e11868d7a2f427e0ef940d
  sovran-react-native: 5aca73bffa66562f8cd8cbb4b766e4c40da9743b
  sqlite3: 02d1f07eaaa01f80a1c16b4b31dfcbb3345ee01a
  SwiftAudioEx: f6aa653770f3a0d3851edaf8d834a30aee4a7646
  taboola-react-native-taboola: dfc75e518528eda745141f14f25c38115ca48c77
  TaboolaSDK: d42cf8e245413385b096d6c521e307049e75d0eb
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: 950bbfd7e6f04790fdb51149ed51df41f329fcc8

PODFILE CHECKSUM: 4594b1aa243b0fa26f324763697d8b6a202827c9

COCOAPODS: 1.16.2
