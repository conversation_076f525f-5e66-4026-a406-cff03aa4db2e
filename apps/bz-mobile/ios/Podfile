source 'https://cdn.cocoapods.org/'

require File.join(File.dirname(`node --print "require.resolve('expo/package.json')"`), "scripts/autolinking")
require File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "scripts/react_native_pods")
# require File.join(File.dirname(`node --print "require.resolve('@react-native-community/cli-platform-ios/package.json')"`), "native_modules")
require File.join(File.dirname(`node --print "require.resolve('react-native-permissions/package.json')"`), "scripts/setup")

# RNFBAnalytics: Using default Firebase/Analytics with Ad Ids. May require App Tracking Transparency. Not allowed for Kids apps.
# RNFBAnalytics: You may set variable `$RNFirebaseAnalyticsWithoutAdIdSupport=true` in Podfile to use analytics without ad ids.
$RNFirebaseAnalyticsWithoutAdIdSupport=true

require 'json'
podfile_properties = JSON.parse(File.read(File.join(__dir__, 'Podfile.properties.json'))) rescue {}

ENV['RCT_NEW_ARCH_ENABLED'] = podfile_properties['newArchEnabled'] == 'true' ? '1' : '0'
ENV['EX_DEV_CLIENT_NETWORK_INSPECTOR'] = podfile_properties['EX_DEV_CLIENT_NETWORK_INSPECTOR']

platform :ios, podfile_properties['ios.deploymentTarget'] || '13.4'
install! 'cocoapods',
  :deterministic_uuids => false

prepare_react_native_project!

# ⬇️ uncomment wanted permissions
setup_permissions([
  'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  'LocationAccuracy',
  # 'LocationAlways',
  'LocationWhenInUse',
  'MediaLibrary',
  'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  'StoreKit',
])

target 'Benzinga' do
  use_expo_modules!
  config = use_native_modules!

  use_frameworks! :linkage => podfile_properties['ios.useFrameworks'].to_sym if podfile_properties['ios.useFrameworks']
  use_frameworks! :linkage => ENV['USE_FRAMEWORKS'].to_sym if ENV['USE_FRAMEWORKS']

  # Flags change depending on the env values.
  # flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => podfile_properties['expo.jsEngine'] == nil || podfile_properties['expo.jsEngine'] == 'hermes',
    # :fabric_enabled => flags[:fabric_enabled],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :privacy_file_aggregation_enabled => podfile_properties['apple.privacyManifestAggregationEnabled'] != 'false',
    #
    # Uncomment to opt-in to using Flipper
    # Note that if you have use_frameworks! enabled, Flipper will not work
    # :flipper_configuration => !ENV['CI'] ? FlipperConfiguration.enabled : FlipperConfiguration.disabled,
  )

  pod 'InMobiSDK'
  pod 'InMobiCMP', '2.2.0'

  pod 'FirebaseCore', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true

  # This is required for uploading dev build on testfligh to avoid non-public API usage error
  post_install do |installer|

    # find_and_replace(File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "React/Base/RCTKeyCommands.m"), # "./node_modules/react-native/React/Base/RCTKeyCommands.m",
    #                         "_modifierFlags",
    #                         "_modifierEventFlags")
    # find_and_replace(File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "React/Base/RCTKeyCommands.m"), # "./node_modules/react-native/React/Base/RCTKeyCommands.m",
    #                         "_modifiedInput",
    #                         "_modifiedEventInput")
    # find_and_replace(File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "React/Base/RCTKeyCommands.m"), # "./node_modules/react-native/React/Base/RCTKeyCommands.m",
    #                         "_isKeyDown",
    #                         "_isKeyEventDown")
    # find_and_replace(File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "React/DevSupport/RCTPackagerClient.h"), # "./node_modules/react-native/React/DevSupport/RCTPackagerClient.h",
    #                         "handleNotification",
    #                         "handlePackageNotification")
    # find_and_replace(File.join(File.dirname(`node --print "require.resolve('react-native/package.json')"`), "React/DevSupport/RCTPackagerConnection.mm"), # "./node_modules/react-native/React/DevSupport/RCTPackagerConnection.mm",
    #                         "handleNotification",
    #                         "handlePackageNotification")

    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )
    # __apply_Xcode_12_5_M1_post_install_workaround(installer)


    # This is necessary for XCode 15.0.1 as it has deprecated some native c/cpp features
    # installer.pods_project.targets.each do |target|
    #   target.build_configurations.each do |config|
    #     config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
    #   end
    # end

    # This is necessary for Xcode 14, because it signs resource bundles by default
    # when building for devices.
    installer.target_installation_results.pod_target_installation_results
      .each do |pod_name, target_installation_result|
      target_installation_result.resource_bundle_targets.each do |resource_bundle_target|
        resource_bundle_target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
    end
  end

  post_integrate do |installer|
    begin
      expo_patch_react_imports!(installer)
    rescue => e
      Pod::UI.warn e
    end
  end
end

# target 'OneSignalNotificationServiceExtension' do
#   pod 'OneSignalXCFramework'
#   use_frameworks! :linkage => podfile_properties['ios.useFrameworks'].to_sym if podfile_properties['ios.useFrameworks']
# end

#target 'OneSignalNotificationServiceExtension' do
#  pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
#end

def find_and_replace(dir, findstr, replacestr)
  Dir[dir].each do |name|
      text = File.read(name)
      replace = text.gsub(findstr,replacestr)
      if text != replace
          puts "Fix: " + name
          File.open(name, "w") { |file| file.puts replace }
          STDOUT.flush
      end
  end
  Dir[dir + '*/'].each(&method(:find_and_replace))
end
