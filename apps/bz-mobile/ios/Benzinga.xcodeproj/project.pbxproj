// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		763ED1C723BA41CA84F89CA7 /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6A507B13A50461580BB36E5 /* noop-file.swift */; };
		96905EF65AED1B983A6B3ABC /* libPods-Benzinga.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 58EEBF8E8E6FB1BC6CAF49B5 /* libPods-Benzinga.a */; };
		ABFD00D6B1C6475DA6E44E6C /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 23E1E5A53D1242FABB7FFD28 /* GoogleService-Info.plist */; };
		AE17871D2E288164007B29D8 /* InMobiInterstitialAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AE17871B2E288164007B29D8 /* InMobiInterstitialAdManager.m */; };
		AE1C287E2C3D620200B4CF42 /* Graphik-Medium-Web.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C28772C3D620200B4CF42 /* Graphik-Medium-Web.ttf */; };
		AE1C287F2C3D620200B4CF42 /* Graphik-Semibold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C28782C3D620200B4CF42 /* Graphik-Semibold.ttf */; };
		AE1C28802C3D620200B4CF42 /* Graphik-Regular-Web.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C28792C3D620200B4CF42 /* Graphik-Regular-Web.ttf */; };
		AE1C28812C3D620200B4CF42 /* Graphik-Bold-Web.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C287A2C3D620200B4CF42 /* Graphik-Bold-Web.ttf */; };
		AE1C28822C3D620200B4CF42 /* Graphik-Light-Web.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C287B2C3D620200B4CF42 /* Graphik-Light-Web.ttf */; };
		AE1C28832C3D620200B4CF42 /* Rhode-Medium-Condensed.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C287C2C3D620200B4CF42 /* Rhode-Medium-Condensed.ttf */; };
		AE1C28842C3D620200B4CF42 /* Montserrat-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AE1C287D2C3D620200B4CF42 /* Montserrat-Regular.ttf */; };
		AE47EE392C535A0400BA55FE /* libxpc_datastores.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = AE47EE382C5359F600BA55FE /* libxpc_datastores.tbd */; };
		AE67E9752E53606A00E05708 /* RNPreRollAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E96F2E53606A00E05708 /* RNPreRollAdManager.m */; };
		AE67E9762E53606A00E05708 /* PreRollAdView.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E96A2E53606A00E05708 /* PreRollAdView.m */; };
		AE67E9772E53606A00E05708 /* SplashAdView.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9732E53606A00E05708 /* SplashAdView.m */; };
		AE67E9782E53606A00E05708 /* index.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9622E53606A00E05708 /* index.m */; };
		AE67E9792E53606A00E05708 /* InMobiPackage.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9682E53606A00E05708 /* InMobiPackage.m */; };
		AE67E97B2E53606A00E05708 /* RNInFeedAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E96D2E53606A00E05708 /* RNInFeedAdManager.m */; };
		AE67E97C2E53606A00E05708 /* RNSplashAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9712E53606A00E05708 /* RNSplashAdManager.m */; };
		AE67E97D2E53606A00E05708 /* InFeedAdView.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9642E53606A00E05708 /* InFeedAdView.m */; };
		AE67E97E2E53606A00E05708 /* BaseNativeAdView.m in Sources */ = {isa = PBXBuildFile; fileRef = AE67E9612E53606A00E05708 /* BaseNativeAdView.m */; };
		AEA935EC29D8093A007935F9 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEA935EB29D8093A007935F9 /* StoreKit.framework */; };
		AEF876B52CE3799B0033817C /* AUAudioUnitRTCAudioDevice.swift in Sources */ = {isa = PBXBuildFile; fileRef = AEF876B42CE3799B0033817C /* AUAudioUnitRTCAudioDevice.swift */; };
		AEF876B72CE37AA30033817C /* AudioSessionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = AEF876B62CE37AA30033817C /* AudioSessionHandler.swift */; };
		AEF876BA2CE37ADC0033817C /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = AEF876B92CE37ADC0033817C /* Utils.swift */; };
		AEF876BB2CE37ADC0033817C /* SimpleAudioConverter.swift in Sources */ = {isa = PBXBuildFile; fileRef = AEF876B82CE37ADC0033817C /* SimpleAudioConverter.swift */; };
		B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */; };
		BB2F792D24A3F905000567C9 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		AE9F3FFB2E2E8BF3009C006A /* Embed ExtensionKit Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "$(EXTENSIONS_FOLDER_PATH)";
			dstSubfolderSpec = 16;
			files = (
			);
			name = "Embed ExtensionKit Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Benzinga.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Benzinga.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Benzinga/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = Benzinga/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Benzinga/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Benzinga/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Benzinga/main.m; sourceTree = "<group>"; };
		23E1E5A53D1242FABB7FFD28 /* GoogleService-Info.plist */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Benzinga/GoogleService-Info.plist"; sourceTree = "<group>"; };
		4395226B0ED1117A922819F6 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		58EEBF8E8E6FB1BC6CAF49B5 /* libPods-Benzinga.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Benzinga.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		61ADAAFB279DB9634289C9EF /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-OneSignalNotificationServiceExtension/Pods-OneSignalNotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		6C2E3173556A471DD304B334 /* Pods-Benzinga.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Benzinga.debug.xcconfig"; path = "Target Support Files/Pods-Benzinga/Pods-Benzinga.debug.xcconfig"; sourceTree = "<group>"; };
		7A4D352CD337FB3A3BF06240 /* Pods-Benzinga.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Benzinga.release.xcconfig"; path = "Target Support Files/Pods-Benzinga/Pods-Benzinga.release.xcconfig"; sourceTree = "<group>"; };
		7EEF370B90B938FA814BEC8C /* libPods-OneSignalNotificationServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-OneSignalNotificationServiceExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = Benzinga/SplashScreen.storyboard; sourceTree = "<group>"; };
		AE17871A2E288164007B29D8 /* InMobiInterstitialAdManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InMobiInterstitialAdManager.h; sourceTree = "<group>"; };
		AE17871B2E288164007B29D8 /* InMobiInterstitialAdManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InMobiInterstitialAdManager.m; sourceTree = "<group>"; };
		AE1C28772C3D620200B4CF42 /* Graphik-Medium-Web.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Graphik-Medium-Web.ttf"; path = "../../src/assets/fonts/Graphik-Medium-Web.ttf"; sourceTree = "<group>"; };
		AE1C28782C3D620200B4CF42 /* Graphik-Semibold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Graphik-Semibold.ttf"; path = "../../src/assets/fonts/Graphik-Semibold.ttf"; sourceTree = "<group>"; };
		AE1C28792C3D620200B4CF42 /* Graphik-Regular-Web.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Graphik-Regular-Web.ttf"; path = "../../src/assets/fonts/Graphik-Regular-Web.ttf"; sourceTree = "<group>"; };
		AE1C287A2C3D620200B4CF42 /* Graphik-Bold-Web.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Graphik-Bold-Web.ttf"; path = "../../src/assets/fonts/Graphik-Bold-Web.ttf"; sourceTree = "<group>"; };
		AE1C287B2C3D620200B4CF42 /* Graphik-Light-Web.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Graphik-Light-Web.ttf"; path = "../../src/assets/fonts/Graphik-Light-Web.ttf"; sourceTree = "<group>"; };
		AE1C287C2C3D620200B4CF42 /* Rhode-Medium-Condensed.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Rhode-Medium-Condensed.ttf"; path = "../../src/assets/fonts/Rhode-Medium-Condensed.ttf"; sourceTree = "<group>"; };
		AE1C287D2C3D620200B4CF42 /* Montserrat-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Montserrat-Regular.ttf"; path = "../../src/assets/fonts/Montserrat-Regular.ttf"; sourceTree = "<group>"; };
		AE47EE2E2C53540200BA55FE /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		AE47EE302C53540A00BA55FE /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		AE47EE322C53541100BA55FE /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		AE47EE342C53541900BA55FE /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		AE47EE362C5356CE00BA55FE /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		AE47EE382C5359F600BA55FE /* libxpc_datastores.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxpc_datastores.tbd; path = usr/lib/libxpc_datastores.tbd; sourceTree = SDKROOT; };
		AE5BAF5B2E2E3CD1003028A1 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		AE5BAF622E2E45C8003028A1 /* libsqlite3.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libsqlite3.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AE67E9602E53606A00E05708 /* BaseNativeAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaseNativeAdView.h; sourceTree = "<group>"; };
		AE67E9612E53606A00E05708 /* BaseNativeAdView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaseNativeAdView.m; sourceTree = "<group>"; };
		AE67E9622E53606A00E05708 /* index.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = index.m; sourceTree = "<group>"; };
		AE67E9632E53606A00E05708 /* InFeedAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InFeedAdView.h; sourceTree = "<group>"; };
		AE67E9642E53606A00E05708 /* InFeedAdView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InFeedAdView.m; sourceTree = "<group>"; };
		AE67E9672E53606A00E05708 /* InMobiPackage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InMobiPackage.h; sourceTree = "<group>"; };
		AE67E9682E53606A00E05708 /* InMobiPackage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InMobiPackage.m; sourceTree = "<group>"; };
		AE67E9692E53606A00E05708 /* PreRollAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PreRollAdView.h; sourceTree = "<group>"; };
		AE67E96A2E53606A00E05708 /* PreRollAdView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PreRollAdView.m; sourceTree = "<group>"; };
		AE67E96C2E53606A00E05708 /* RNInFeedAdManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNInFeedAdManager.h; sourceTree = "<group>"; };
		AE67E96D2E53606A00E05708 /* RNInFeedAdManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNInFeedAdManager.m; sourceTree = "<group>"; };
		AE67E96E2E53606A00E05708 /* RNPreRollAdManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNPreRollAdManager.h; sourceTree = "<group>"; };
		AE67E96F2E53606A00E05708 /* RNPreRollAdManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNPreRollAdManager.m; sourceTree = "<group>"; };
		AE67E9702E53606A00E05708 /* RNSplashAdManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNSplashAdManager.h; sourceTree = "<group>"; };
		AE67E9712E53606A00E05708 /* RNSplashAdManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNSplashAdManager.m; sourceTree = "<group>"; };
		AE67E9722E53606A00E05708 /* SplashAdView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SplashAdView.h; sourceTree = "<group>"; };
		AE67E9732E53606A00E05708 /* SplashAdView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SplashAdView.m; sourceTree = "<group>"; };
		AEA276A52CE4D38A00DCCFD1 /* BenzingaDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = BenzingaDebug.entitlements; path = Benzinga/BenzingaDebug.entitlements; sourceTree = "<group>"; };
		AEA935EB29D8093A007935F9 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		AEAA89532A7A8A280054A020 /* charts */ = {isa = PBXFileReference; lastKnownFileType = folder; path = charts; sourceTree = "<group>"; };
		AEF876B42CE3799B0033817C /* AUAudioUnitRTCAudioDevice.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AUAudioUnitRTCAudioDevice.swift; sourceTree = "<group>"; };
		AEF876B62CE37AA30033817C /* AudioSessionHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioSessionHandler.swift; sourceTree = "<group>"; };
		AEF876B82CE37ADC0033817C /* SimpleAudioConverter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SimpleAudioConverter.swift; sourceTree = "<group>"; };
		AEF876B92CE37ADC0033817C /* Utils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Utils.swift; sourceTree = "<group>"; };
		BB2F792C24A3F905000567C9 /* Expo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Expo.plist; sourceTree = "<group>"; };
		CA183153469643EBBE87C974 /* Benzinga-Bridging-Header.h */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.c.h; name = "Benzinga-Bridging-Header.h"; path = "Benzinga/Benzinga-Bridging-Header.h"; sourceTree = "<group>"; };
		D6A507B13A50461580BB36E5 /* noop-file.swift */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.swift; name = "noop-file.swift"; path = "Benzinga/noop-file.swift"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-Benzinga/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				AEA935EC29D8093A007935F9 /* StoreKit.framework in Frameworks */,
				96905EF65AED1B983A6B3ABC /* libPods-Benzinga.a in Frameworks */,
				AE47EE392C535A0400BA55FE /* libxpc_datastores.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* Benzinga */ = {
			isa = PBXGroup;
			children = (
				AEA276A52CE4D38A00DCCFD1 /* BenzingaDebug.entitlements */,
				AE1C28762C3D61F700B4CF42 /* fonts */,
				AEAA89532A7A8A280054A020 /* charts */,
				BB2F792B24A3F905000567C9 /* Supporting */,
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				AE17871C2E288164007B29D8 /* modules */,
				AEF876B42CE3799B0033817C /* AUAudioUnitRTCAudioDevice.swift */,
				AEF876B62CE37AA30033817C /* AudioSessionHandler.swift */,
				AEF876B82CE37ADC0033817C /* SimpleAudioConverter.swift */,
				AEF876B92CE37ADC0033817C /* Utils.swift */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */,
				23E1E5A53D1242FABB7FFD28 /* GoogleService-Info.plist */,
				D6A507B13A50461580BB36E5 /* noop-file.swift */,
				CA183153469643EBBE87C974 /* Benzinga-Bridging-Header.h */,
			);
			name = Benzinga;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AE5BAF622E2E45C8003028A1 /* libsqlite3.a */,
				AE5BAF5B2E2E3CD1003028A1 /* libsqlite3.tbd */,
				AE47EE382C5359F600BA55FE /* libxpc_datastores.tbd */,
				AE47EE362C5356CE00BA55FE /* AdSupport.framework */,
				AE47EE342C53541900BA55FE /* CoreGraphics.framework */,
				AE47EE322C53541100BA55FE /* SystemConfiguration.framework */,
				AE47EE302C53540A00BA55FE /* MessageUI.framework */,
				AE47EE2E2C53540200BA55FE /* AudioToolbox.framework */,
				AEA935EB29D8093A007935F9 /* StoreKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				58EEBF8E8E6FB1BC6CAF49B5 /* libPods-Benzinga.a */,
				7EEF370B90B938FA814BEC8C /* libPods-OneSignalNotificationServiceExtension.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* Benzinga */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				D65327D7A22EEC0BE12398D9 /* Pods */,
				D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Benzinga.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		92DBD88DE9BF7D494EA9DA96 /* Benzinga */ = {
			isa = PBXGroup;
			children = (
				FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */,
			);
			name = Benzinga;
			sourceTree = "<group>";
		};
		AE17871C2E288164007B29D8 /* modules */ = {
			isa = PBXGroup;
			children = (
				AE67E9602E53606A00E05708 /* BaseNativeAdView.h */,
				AE67E9612E53606A00E05708 /* BaseNativeAdView.m */,
				AE67E9622E53606A00E05708 /* index.m */,
				AE67E9632E53606A00E05708 /* InFeedAdView.h */,
				AE67E9642E53606A00E05708 /* InFeedAdView.m */,
				AE67E9672E53606A00E05708 /* InMobiPackage.h */,
				AE67E9682E53606A00E05708 /* InMobiPackage.m */,
				AE67E9692E53606A00E05708 /* PreRollAdView.h */,
				AE67E96A2E53606A00E05708 /* PreRollAdView.m */,
				AE67E96C2E53606A00E05708 /* RNInFeedAdManager.h */,
				AE67E96D2E53606A00E05708 /* RNInFeedAdManager.m */,
				AE67E96E2E53606A00E05708 /* RNPreRollAdManager.h */,
				AE67E96F2E53606A00E05708 /* RNPreRollAdManager.m */,
				AE67E9702E53606A00E05708 /* RNSplashAdManager.h */,
				AE67E9712E53606A00E05708 /* RNSplashAdManager.m */,
				AE67E9722E53606A00E05708 /* SplashAdView.h */,
				AE67E9732E53606A00E05708 /* SplashAdView.m */,
				AE17871A2E288164007B29D8 /* InMobiInterstitialAdManager.h */,
				AE17871B2E288164007B29D8 /* InMobiInterstitialAdManager.m */,
			);
			path = modules;
			sourceTree = "<group>";
		};
		AE1C28762C3D61F700B4CF42 /* fonts */ = {
			isa = PBXGroup;
			children = (
				AE1C287A2C3D620200B4CF42 /* Graphik-Bold-Web.ttf */,
				AE1C287B2C3D620200B4CF42 /* Graphik-Light-Web.ttf */,
				AE1C28772C3D620200B4CF42 /* Graphik-Medium-Web.ttf */,
				AE1C28792C3D620200B4CF42 /* Graphik-Regular-Web.ttf */,
				AE1C28782C3D620200B4CF42 /* Graphik-Semibold.ttf */,
				AE1C287D2C3D620200B4CF42 /* Montserrat-Regular.ttf */,
				AE1C287C2C3D620200B4CF42 /* Rhode-Medium-Condensed.ttf */,
			);
			path = fonts;
			sourceTree = "<group>";
		};
		BB2F792B24A3F905000567C9 /* Supporting */ = {
			isa = PBXGroup;
			children = (
				BB2F792C24A3F905000567C9 /* Expo.plist */,
			);
			name = Supporting;
			path = Benzinga/Supporting;
			sourceTree = "<group>";
		};
		D65327D7A22EEC0BE12398D9 /* Pods */ = {
			isa = PBXGroup;
			children = (
				6C2E3173556A471DD304B334 /* Pods-Benzinga.debug.xcconfig */,
				7A4D352CD337FB3A3BF06240 /* Pods-Benzinga.release.xcconfig */,
				61ADAAFB279DB9634289C9EF /* Pods-OneSignalNotificationServiceExtension.debug.xcconfig */,
				4395226B0ED1117A922819F6 /* Pods-OneSignalNotificationServiceExtension.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				92DBD88DE9BF7D494EA9DA96 /* Benzinga */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* Benzinga */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Benzinga" */;
			buildPhases = (
				08A4A3CD28434E44B6B9DE2E /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				CA606D9FA0F5A109B6564102 /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				800E24972A6A228C8D4807E9 /* [CP] Copy Pods Resources */,
				3E154C3CD597F40F38A7EFC7 /* [CP] Embed Pods Frameworks */,
				FB520ACB197023057DF99D76 /* [CP-User] [RNGoogleMobileAds] Configuration */,
				A1A226162832588E04C7D49E /* [CP-User] [RNFB] Core Configuration */,
				AE9F3FFB2E2E8BF3009C006A /* Embed ExtensionKit Extensions */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Benzinga;
			productName = Benzinga;
			productReference = 13B07F961A680F5B00A75B9A /* Benzinga.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1250;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Benzinga" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Benzinga */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AE1C28842C3D620200B4CF42 /* Montserrat-Regular.ttf in Resources */,
				BB2F792D24A3F905000567C9 /* Expo.plist in Resources */,
				AE1C287F2C3D620200B4CF42 /* Graphik-Semibold.ttf in Resources */,
				AE1C28802C3D620200B4CF42 /* Graphik-Regular-Web.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				AE1C28832C3D620200B4CF42 /* Rhode-Medium-Condensed.ttf in Resources */,
				3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */,
				ABFD00D6B1C6475DA6E44E6C /* GoogleService-Info.plist in Resources */,
				AE1C28812C3D620200B4CF42 /* Graphik-Bold-Web.ttf in Resources */,
				AE1C287E2C3D620200B4CF42 /* Graphik-Medium-Web.ttf in Resources */,
				AE1C28822C3D620200B4CF42 /* Graphik-Light-Web.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export EXTRA_PACKAGER_ARGS=\"--sourcemap-output $DERIVED_FILE_DIR/main.jsbundle.map\"\n\nif [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\n# The project root by default is one level up from the ios directory\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nif [[ \"$CONFIGURATION\" = *Debug* ]]; then\n  export SKIP_BUNDLING=1\nfi\nif [[ -z \"$ENTRY_FILE\" ]]; then\n  # Set the entry JS file using the bundler's entry resolution.\n  export ENTRY_FILE=\"$(\"$NODE_BINARY\" -e \"require('expo/scripts/resolveAppEntry')\" $PROJECT_ROOT ios relative | tail -n 1)\"\nfi\n# export SOURCEMAP_FILE=\"$(pwd)/../main.jsbundle.map\";\n# `\"$NODE_BINARY\" --print \"require.resolve('@sentry/cli/package.json').slice(0, -13) + '/bin/sentry-cli'\"` react-native xcode --force-foreground\n`\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/react-native-xcode.sh'\"`\n\n";
		};
		08A4A3CD28434E44B6B9DE2E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Benzinga-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3E154C3CD597F40F38A7EFC7 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Benzinga/Pods-Benzinga-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/ConnatixAppMeasurement/OMSDK_Connatix.framework/OMSDK_Connatix",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/ConnatixPlayerSDKObjc/ConnatixPlayerSDKObjc.framework/ConnatixPlayerSDKObjc",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/InMobiCMP/InMobiCMP.framework/InMobiCMP",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/InMobiSDK/InMobiSDK.framework/InMobiSDK",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/JitsiWebRTC/WebRTC.framework/WebRTC",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/TaboolaSDK/TaboolaSDK.framework/TaboolaSDK",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/OMSDK_Connatix.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ConnatixPlayerSDKObjc.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/InMobiCMP.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/InMobiSDK.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WebRTC.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/TaboolaSDK.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Benzinga/Pods-Benzinga-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		800E24972A6A228C8D4807E9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Benzinga/Pods-Benzinga-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXApplication/ExpoApplication_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/ExpoConstants_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXNotifications/ExpoNotifications_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/EXUpdates.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDevice/ExpoDevice_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoMediaLibrary/ExpoMediaLibrary_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/GoogleMobileAdsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUserMessagingPlatform/UserMessagingPlatformResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo/RNDeviceInfoPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPickerPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Entypo.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Feather.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Brands.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Foundation.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Octicons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf",
				"${PODS_ROOT}/../../../../node_modules/react-native-vector-icons/Fonts/Zocial.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/ReachabilitySwift.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/RCTI18nStrings.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-launcher/EXDevLauncher.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu/EXDevMenu.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/LottiePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/Lottie_React_Native_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/taboola-react-native-taboola/taboola-react-native-taboola_privacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoApplication_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoConstants_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoNotifications_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXUpdates.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoDevice_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoFileSystem_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoMediaLibrary_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoSystemUI_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCore_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMobileAdsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/UserMessagingPlatformResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNDeviceInfoPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNImageCropPickerPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AntDesign.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EvilIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Feather.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Foundation.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialCommunityIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Octicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SimpleLineIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Zocial.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ReachabilitySwift.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCTI18nStrings.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SDWebImage.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXDevLauncher.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXDevMenu.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LottiePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Lottie_React_Native_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/nanopb_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/taboola-react-native-taboola_privacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Benzinga/Pods-Benzinga-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A1A226162832588E04C7D49E /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		CA606D9FA0F5A109B6564102 /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-Benzinga/expo-configure-project.sh\"\n";
		};
		FB520ACB197023057DF99D76 /* [CP-User] [RNGoogleMobileAds] Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNGoogleMobileAds] Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_PROJECT_ABBREVIATION=\"RNGoogleMobileAds\"\n_JSON_ROOT=\"'react-native-google-mobile-ads'\"\n_JSON_FILE_NAME='app.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n_PACKAGE_JSON_NAME='package.json'\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -KU -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> ${_PROJECT_ABBREVIATION} build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\n# Bail out if project is using Expo\n_PACKAGE_JSON_PATH=$(dirname \"${_SEARCH_RESULT}\")/${_PACKAGE_JSON_NAME}\n_IS_PROJECT_USING_EXPO=$(ruby -KU -e \"require 'json'; package=JSON.parse(File.read('${_PACKAGE_JSON_PATH}')); puts package['dependencies'].key?('expo')\")\n\nif [[ ${_IS_PROJECT_USING_EXPO} == \"true\" ]]; then\n  echo \"info: Expo project detected, assume Expo Config Plugin is used.\"\n  exit 0\nfi\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -KU -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, app.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"google_mobile_ads_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.delay_app_measurement_init\n  _DELAY_APP_MEASUREMENT=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"delay_app_measurement_init\")\n  if [[ $_DELAY_APP_MEASUREMENT == \"true\" ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GADDelayAppMeasurementInit\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"YES\")\n  fi\n\n  # config.ios_app_id\n  _IOS_APP_ID=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"ios_app_id\")\n  if [[ $_IOS_APP_ID ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GADApplicationIdentifier\")\n    _PLIST_ENTRY_TYPES+=(\"string\")\n    _PLIST_ENTRY_VALUES+=(\"$_IOS_APP_ID\")\n  fi\n\n  # config.sk_ad_network_items\n  _SK_AD_NETWORK_ITEMS=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"sk_ad_network_items\")\n  if [[ $_SK_AD_NETWORK_ITEMS ]]; then\n    _PLIST_ENTRY_KEYS+=(\"SKAdNetworkItems\")\n    _PLIST_ENTRY_TYPES+=(\"array\")\n    _PLIST_ENTRY_VALUES+=(\"\")\n\n    oldifs=$IFS\n    IFS=\"\n\"\n    array=($(echo \"$_SK_AD_NETWORK_ITEMS\"))\n    IFS=$oldifs\n    for i in \"${!array[@]}\"; do\n      _PLIST_ENTRY_KEYS+=(\"SKAdNetworkItems:$i:SKAdNetworkIdentifier\")\n      _PLIST_ENTRY_TYPES+=(\"string\")\n      _PLIST_ENTRY_VALUES+=(\"${array[i]}\")  \n    done\n  fi\n\n    # config.user_tracking_usage_description\n  _USER_TRACKING_USAGE_DESCRIPTION=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"user_tracking_usage_description\")\n  if [[ $_USER_TRACKING_USAGE_DESCRIPTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"NSUserTrackingUsageDescription\")\n    _PLIST_ENTRY_TYPES+=(\"string\")\n    _PLIST_ENTRY_VALUES+=(\"$_USER_TRACKING_USAGE_DESCRIPTION\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"google_mobile_ads_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A ${_JSON_FILE_NAME} file was not found, whilst this file is optional it is recommended to include it to auto-configure services.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nif ! [[ -f \"${_TARGET_PLIST}\" ]]; then\n  echo \"error: unable to locate Info.plist to set properties. App will crash without GADApplicationIdentifier set.\"\n  exit 1\nfi\n\nif ! [[ $_IOS_APP_ID ]]; then\n  echo \"error: ios_app_id key not found in react-native-google-mobile-ads key in app.json. App will crash without it.\"\n  exit 1\nfi\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- ${_PROJECT_ABBREVIATION} build script finished\"\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > `$NODE_BINARY --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/.packager.env'\"`\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open `$NODE_BINARY --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/launchPackager.command'\"` || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AEF876B72CE37AA30033817C /* AudioSessionHandler.swift in Sources */,
				AEF876B52CE3799B0033817C /* AUAudioUnitRTCAudioDevice.swift in Sources */,
				AEF876BA2CE37ADC0033817C /* Utils.swift in Sources */,
				AE17871D2E288164007B29D8 /* InMobiInterstitialAdManager.m in Sources */,
				AEF876BB2CE37ADC0033817C /* SimpleAudioConverter.swift in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				AE67E9752E53606A00E05708 /* RNPreRollAdManager.m in Sources */,
				AE67E9762E53606A00E05708 /* PreRollAdView.m in Sources */,
				AE67E9772E53606A00E05708 /* SplashAdView.m in Sources */,
				AE67E9782E53606A00E05708 /* index.m in Sources */,
				AE67E9792E53606A00E05708 /* InMobiPackage.m in Sources */,
				AE67E97B2E53606A00E05708 /* RNInFeedAdManager.m in Sources */,
				AE67E97C2E53606A00E05708 /* RNSplashAdManager.m in Sources */,
				AE67E97D2E53606A00E05708 /* InFeedAdView.m in Sources */,
				AE67E97E2E53606A00E05708 /* BaseNativeAdView.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */,
				763ED1C723BA41CA84F89CA7 /* noop-file.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6C2E3173556A471DD304B334 /* Pods-Benzinga.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				ASSETCATALOG_COMPILER_SKIP_APP_STORE_DEPLOYMENT = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Benzinga/BenzingaDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7XA6MAHDCW;
				ENABLE_BITCODE = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/EXAV\"",
					"\"${PODS_ROOT}/Headers/Public/EXApplication\"",
					"\"${PODS_ROOT}/Headers/Public/EXConstants\"",
					"\"${PODS_ROOT}/Headers/Public/EXImageLoader\"",
					"\"${PODS_ROOT}/Headers/Public/EXJSONUtils\"",
					"\"${PODS_ROOT}/Headers/Public/EXNotifications\"",
					"\"${PODS_ROOT}/Headers/Public/EXSplashScreen\"",
					"\"${PODS_ROOT}/Headers/Public/EXStructuredHeaders\"",
					"\"${PODS_ROOT}/Headers/Public/EXUpdates\"",
					"\"${PODS_ROOT}/Headers/Public/Expo\"",
					"\"${PODS_ROOT}/Headers/Public/ExpoFileSystem\"",
					"\"${PODS_ROOT}/Headers/Public/ExpoMediaLibrary\"",
					"\"${PODS_ROOT}/Headers/Public/ExpoModulesCore\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNGoogleMobileAds\"",
					"\"${PODS_ROOT}/Headers/Public/RNIap\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageCropPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeHermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/TOCropViewController\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/connatix-player-sdk-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/expo-dev-launcher\"",
					"\"${PODS_ROOT}/Headers/Public/expo-dev-menu\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"${PODS_ROOT}/Headers/Public/libwebp\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-get-random-values\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-track-player\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webrtc\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"${PODS_ROOT}/Headers/Public/segment-analytics-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/sovran-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/sqlite3\"",
					"\"${PODS_ROOT}/Headers/Public/taboola-react-native-taboola\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXManifests/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/Swift Compatibility Header\"",
					"\"$(PODS_ROOT)/Headers/Private/Yoga\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-launcher/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/segment-analytics-react-native/Swift Compatibility Header\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sovran-react-native/Swift Compatibility Header\"",
				);
				INFOPLIST_FILE = Benzinga/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.news";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EASClient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXAV\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXApplication\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXJSONUtils\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXManifests\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXNotifications\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXSplashScreen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXStructuredHeaders\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Expo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAppleAuthentication\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDevice\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDocumentPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImageManipulator\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImagePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoMediaLibrary\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoRandom\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoStoreReview\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGoogleMobileAds\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNIap\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/connatix-player-sdk-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-launcher\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pager-view\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webrtc\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/segment-analytics-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sovran-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/sqlite3\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/taboola-react-native-taboola\"",
					"\"${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}\"",
					/usr/lib/swift,
				);
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EASClient/EASClient.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXAV/EXAV.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXApplication/EXApplication.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXManifests/EXManifests.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/EXUpdates.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface/EXUpdatesInterface.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAppleAuthentication/ExpoAppleAuthentication.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset/ExpoAsset.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur/ExpoBlur.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoClipboard/ExpoClipboard.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto/ExpoCrypto.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDevice/ExpoDevice.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDocumentPicker/ExpoDocumentPicker.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont/ExpoFont.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics/ExpoHaptics.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImageManipulator/ExpoImageManipulator.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImagePicker/ExpoImagePicker.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake/ExpoKeepAwake.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient/ExpoLinearGradient.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoMediaLibrary/ExpoMediaLibrary.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/ExpoModulesCore.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoRandom/ExpoRandom.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore/ExpoSecureStore.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing/ExpoSharing.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoStoreReview/ExpoStoreReview.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser/ExpoWebBrowser.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/Google_Mobile_Ads_SDK.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/RNGoogleMobileAds/RNGoogleMobileAds.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/RNIap/RNIap.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/Reachability.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx/SwiftAudioEx.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/connatix-player-sdk-react-native/connatix_player_sdk_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-launcher/EXDevLauncher.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface/EXDevMenuInterface.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu/EXDevMenu.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/Lottie.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/lottie_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player/react_native_track_player.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/segment-analytics-react-native/segment_analytics_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/sovran-react-native/sovran_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXImageLoader/EXImageLoader.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXJSONUtils/EXJSONUtils.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXStructuredHeaders/EXStructuredHeaders.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/FirebaseCore/FirebaseCore.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React-Core/React/React-Core.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React-RCTFabric/RCTFabric/React-RCTFabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_Codegen/React-Codegen.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/glog/glog.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/sqlite3/sqlite3.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap\"",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-Wno-comma",
					"-Wno-shorten-64-to-32",
					"-DREACT_NATIVE_MINOR_VERSION=74",
					"-DREANIMATED_VERSION=3.10.1",
					"-DREACT_NATIVE_TARGET_VERSION=74",
					"-DUSE_HERMES",
				);
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.Benzinga.BenzingaMobile;
				PRODUCT_NAME = Benzinga;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Benzinga/Benzinga-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A4D352CD337FB3A3BF06240 /* Pods-Benzinga.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				ASSETCATALOG_COMPILER_SKIP_APP_STORE_DEPLOYMENT = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Benzinga/Benzinga.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7XA6MAHDCW;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Benzinga/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.news";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_CFLAGS = (
					"$(inherited)",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EASClient/EASClient.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXAV/EXAV.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXApplication/EXApplication.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXManifests/EXManifests.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdates/EXUpdates.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/EXUpdatesInterface/EXUpdatesInterface.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAppleAuthentication/ExpoAppleAuthentication.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset/ExpoAsset.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur/ExpoBlur.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoClipboard/ExpoClipboard.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto/ExpoCrypto.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDevice/ExpoDevice.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDocumentPicker/ExpoDocumentPicker.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont/ExpoFont.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics/ExpoHaptics.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImageManipulator/ExpoImageManipulator.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImagePicker/ExpoImagePicker.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake/ExpoKeepAwake.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient/ExpoLinearGradient.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoMediaLibrary/ExpoMediaLibrary.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/ExpoModulesCore.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoRandom/ExpoRandom.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore/ExpoSecureStore.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSharing/ExpoSharing.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoStoreReview/ExpoStoreReview.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser/ExpoWebBrowser.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/Google_Mobile_Ads_SDK.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/RNGoogleMobileAds/RNGoogleMobileAds.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/RNIap/RNIap.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/Reachability.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/SwiftAudioEx/SwiftAudioEx.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/connatix-player-sdk-react-native/connatix_player_sdk_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface/EXDevMenuInterface.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-ios/Lottie.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/lottie-react-native/lottie_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-track-player/react_native_track_player.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/segment-analytics-react-native/segment_analytics_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/sovran-react-native/sovran_react_native.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXImageLoader/EXImageLoader.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXJSONUtils/EXJSONUtils.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/EXStructuredHeaders/EXStructuredHeaders.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/FirebaseCore/FirebaseCore.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React-Core/React/React-Core.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React-RCTFabric/RCTFabric/React-RCTFabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_Codegen/React-Codegen.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/glog/glog.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/sqlite3/sqlite3.modulemap\"",
					"-fmodule-map-file=\"${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap\"",
					"$(inherited)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-Wno-comma",
					"-Wno-shorten-64-to-32",
					"-DREACT_NATIVE_MINOR_VERSION=74",
					"-DREANIMATED_VERSION=3.10.1",
					"-DNDEBUG",
				);
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.Benzinga.BenzingaMobile;
				PRODUCT_NAME = Benzinga;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Benzinga/Benzinga-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Benzinga" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Benzinga" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
