package com.benzinga.app.inmobi;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

import java.util.Map;

import timber.log.Timber;

public class RNInFeedAdManager extends SimpleViewManager<InFeedAdView> {
  public static final String REACT_CLASS = "RNInMobiInFeedAd";

  // Commands
  private static final int CMD_INIT = 1;
  private static final int CMD_LOAD = 2;
  private static final int CMD_SHOW = 3;
  private static final int CMD_CLICK = 4;
  private static final int CMD_DESTROY = 5;

  private InMobiPackage inMobiPackage;

  public RNInFeedAdManager(InMobiPackage inMobiPackage) {
    this.inMobiPackage = inMobiPackage;
  }

  @NonNull @Override public String getName() {
    return REACT_CLASS;
  }

  @NonNull @Override protected InFeedAdView createViewInstance(@NonNull ThemedReactContext reactContext) {
    return new InFeedAdView(reactContext, inMobiPackage);
  }

  @ReactProp(name = "placementId")
  public void setPlacementId(InFeedAdView view, String placementId) {
    try {
      if (placementId != null && !placementId.isEmpty()) {
        long placementIdLong = Long.parseLong(placementId);
        view.setPlacementId(placementIdLong);
        Timber.tag("RNInFeedAdManager").d("Set placement ID: %s -> %d", placementId, placementIdLong);
      } else {
        Timber.tag("RNInFeedAdManager").e("Invalid placement ID: %s", placementId);
      }
    } catch (NumberFormatException e) {
      Timber.tag("RNInFeedAdManager").e("Error parsing placement ID %s: %s", placementId, e.getMessage());
    }
  }

  @ReactProp(name = "autoShowOnLoad", defaultBoolean = true)
  public void setAutoShowOnLoad(InFeedAdView view, boolean auto) {
    view.setAutoShowOnLoad(auto);
  }

  @Nullable @Override
  public Map<String, Integer> getCommandsMap() {
    return MapBuilder.of(
      "init", CMD_INIT,
      "load", CMD_LOAD,
      "show", CMD_SHOW,
      "click", CMD_CLICK,
      "destroy", CMD_DESTROY
    );
  }

  @Override public void receiveCommand(@NonNull InFeedAdView root, String commandId, @Nullable ReadableArray args) {
    try {
      int id = Integer.parseInt(commandId);

      switch (id) {
        case CMD_INIT:
          if (args != null && args.size() > 0) {
            String placementIdStr = args.getString(0);
            try {
              if (placementIdStr != null && !placementIdStr.isEmpty()) {
                long placementId = Long.parseLong(placementIdStr);
                root.setPlacementId(placementId);
                Timber.tag("RNInFeedAdManager").d("Init command - Set placement ID: %s -> %d", placementIdStr, placementId);
              } else {
                Timber.tag("RNInFeedAdManager").e("Init command - Invalid placement ID: %s", placementIdStr);
              }
            } catch (NumberFormatException e) {
              Timber.tag("RNInFeedAdManager").e("Init command - Error parsing placement ID %s: %s", placementIdStr, e.getMessage());
            }
          }
          if (args != null && args.size() > 1) {
            boolean autoShow = args.getBoolean(1);
            root.setAutoShowOnLoad(autoShow);
          }
          break;
        case CMD_LOAD:
          root.load();
          break;
        case CMD_SHOW:
          root.show();
          break;
        case CMD_CLICK:
          root.click();
          break;
        case CMD_DESTROY:
          root.destroy();
          break;
        default:
          break;
      }
    } catch (Exception e) {
      // Silent fail for production
    }
  }

  @Nullable @Override
  public Map getExportedCustomDirectEventTypeConstants() {
    return MapBuilder.builder()
      .put("onAdLoaded", MapBuilder.of("registrationName", "onAdLoaded"))
      .put("onAdLoadFailed", MapBuilder.of("registrationName", "onAdLoadFailed"))
      .put("onAdClicked", MapBuilder.of("registrationName", "onAdClicked"))
      .put("onAdImpression", MapBuilder.of("registrationName", "onAdImpression"))
      .put("onAdStatusChanged", MapBuilder.of("registrationName", "onAdStatusChanged"))
      .put("onAdShown", MapBuilder.of("registrationName", "onAdShown"))
      .build();
  }
}
