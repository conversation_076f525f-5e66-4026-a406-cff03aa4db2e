package com.benzinga.app.inmobi;

import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.benzinga.app.BuildConfig;
import com.benzinga.app.R;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.events.RCTEventEmitter;
import com.inmobi.ads.AdMetaInfo;
import com.inmobi.ads.InMobiNative;
import com.inmobi.ads.listeners.NativeAdEventListener;
import com.inmobi.ads.InMobiAdRequestStatus;
import com.inmobi.ads.listeners.VideoEventListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

import timber.log.Timber;

/**
 * Base view that wraps an InMobiNative and exposes common lifecycle + event wiring.
 * Each ad format (In-Feed, Splash, Pre-Roll) subclasses this and may tweak sizing/behavior.
 *
 * Uses InMobi default native layouts via getPrimaryViewOfWidth(...).
 */
public abstract class BaseNativeAdView extends FrameLayout {
  protected @Nullable InMobiNative nativeAd;
  protected long placementId = -1L;
  protected boolean autoShowOnLoad = true;

  protected long finalCalculatedHeight = 0L;

  protected InMobiPackage inMobiPackage;

  public BaseNativeAdView(@NonNull Context context) { super(context); init(); }
  public BaseNativeAdView(@NonNull Context context, @NonNull InMobiPackage inMobiPackage) {
    super(context); init();
    this.inMobiPackage = inMobiPackage;
  }
  public BaseNativeAdView(@NonNull Context context, @Nullable AttributeSet attrs) { super(context, attrs); init(); }
  public BaseNativeAdView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) { super(context, attrs, defStyleAttr); init(); }

  private void init() {
    setLayoutParams(new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    setClipToPadding(false);
    setClipChildren(false);

    // Set background color for debugging
    if (BuildConfig.DEBUG) {
      setBackgroundColor(0x3300FF00); // Semi-transparent green
    }
  }

  public void setPlacementId(long placementId) {
    this.placementId = placementId;
  }

  public void setAutoShowOnLoad(boolean autoShowOnLoad) {
    this.autoShowOnLoad = autoShowOnLoad;
  }

  /** Optional: override for custom width calculation */
  protected int targetWidthPx() {
    DisplayMetrics dm = getResources().getDisplayMetrics();
    int width = Math.max(1, getWidth() > 0 ? getWidth() : dm.widthPixels);
    return width;
  }

  protected void emit(String event, @Nullable com.facebook.react.bridge.WritableMap params) {
    try {
      ReactContext reactContext = (ReactContext) getContext();
      reactContext.getJSModule(RCTEventEmitter.class).receiveEvent(getId(), event, params == null ? Arguments.createMap() : params);
    } catch (Exception e) {
      Timber.tag("BaseNativeAdView").e("Error emitting event %s: %s", event, e.getMessage());
    }
  }

  private final NativeAdEventListener listener = new NativeAdEventListener() {
    @Override public void onAdLoadSucceeded(@NonNull InMobiNative inMobiNative, @NonNull AdMetaInfo inMobiAdMeta) {
      emit("onAdLoaded", null);
      if (autoShowOnLoad) {
        show();
      }
    }

    @Override public void onAdLoadFailed(@NonNull InMobiNative inMobiNative, @NonNull InMobiAdRequestStatus error) {
      Timber.tag("BaseNativeAdView").e("onAdLoadFailed: %s - %s", error.getStatusCode(), error.getMessage());
      com.facebook.react.bridge.WritableMap map = Arguments.createMap();
      map.putString("status", String.valueOf(error.getStatusCode()));
      map.putString("message", String.valueOf(error.getMessage()));
      emit("onAdLoadFailed", map);
    }

    @Override public void onAdImpression(@NonNull InMobiNative inMobiNative) {
      emit("onAdImpression", null);
    }

    @Override public void onAdClicked(@NonNull InMobiNative inMobiNative, Map<Object, Object> details) {
      emit("onAdClicked", null);
    }

    @Override public void onAdStatusChanged(@NonNull InMobiNative inMobiNative) {
      emit("onAdStatusChanged", null);
    }
  };

  private final VideoEventListener videoEventListener = new VideoEventListener() {
    @Override
    public void onAudioStateChanged(@NonNull InMobiNative inMobiNative, boolean b) {
      emit("onAudioStateChanged", null);
    }

    @Override
    public void onVideoCompleted(@NonNull InMobiNative inMobiNative) {
      emit("onVideoCompleted", null);
    }

    @Override
    public void onVideoSkipped(@NonNull InMobiNative inMobiNative) {
      emit("onVideoSkipped", null);
    }
  };

  /** Call from JS: builds/loads a new InMobiNative for current placementId */
  public void load() {

    if (placementId <= 0) {
      Timber.tag("BaseNativeAdView").e("Invalid placementId: %d", placementId);
      com.facebook.react.bridge.WritableMap map = Arguments.createMap();
      map.putString("message", "Invalid placementId: " + placementId);
      emit("onAdLoadFailed", map);
      return;
    }

    try {
      destroy();
      nativeAd = new InMobiNative(getContext(), placementId, listener);
      nativeAd.setVideoEventListener(videoEventListener);

      nativeAd.load();

      if (inMobiPackage != null && inMobiPackage.mNativeAds != null) {
        inMobiPackage.mNativeAds.add(nativeAd);
      }
    } catch (Exception e) {
      Timber.tag("BaseNativeAdView").e("Error in load: %s", e.getMessage());
      com.facebook.react.bridge.WritableMap map = Arguments.createMap();
      map.putString("message", "Error loading ad: " + e.getMessage());
      emit("onAdLoadFailed", map);
    }
  }

  /** Renders the default native layout into this container */
  public void show() {
    if (nativeAd == null) {
      Timber.tag("BaseNativeAdView").w("Cannot show: nativeAd is null");
      return;
    }

    try {
      removeAllViews();
      int width = targetWidthPx();
      //Detect whether its a backfill response or normal native
      JSONObject customContent = nativeAd.getCustomAdContent();
      boolean isBackFillBanner = false;
      try {
          isBackFillBanner = customContent.getBoolean("isHTMLResponse");
      } catch (JSONException e) {
          isBackFillBanner = false;
      }

      View primary;
      if (isBackFillBanner) {
        primary = nativeAd.getPrimaryViewOfWidth(getContext(), this, this, Math.round(getResources().getDisplayMetrics().density * 250));
      } else {
        primary = nativeAd.getPrimaryViewOfWidth(getContext(), this, this, width);
      }

      if (primary == null) {
        Timber.tag("BaseNativeAdView").e("getPrimaryViewOfWidth returned null");
        return;
      }

      // Bind clickable areas (recommended by InMobi)
      addView(primary, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

      setVisibility(VISIBLE);
      requestLayout();
      invalidate();

      // call send method after one second delay on same thread
      new android.os.Handler().postDelayed(new Runnable() {
        @Override
        public void run() {
          // Create writable map with finalCalculatedHeight and pass it to emit params
          com.facebook.react.bridge.WritableMap map = Arguments.createMap();
          map.putString("height", String.valueOf(finalCalculatedHeight));
          emit("onAdShown", map);
        }
      }, 1000);

    } catch (Exception e) {
      Timber.tag("BaseNativeAdView").e("Error in show: %s", e.getMessage());
    }
  }

  public void click() {
    if (nativeAd != null) {
      nativeAd.reportAdClickAndOpenLandingPage();
      emit("onAdClicked", null);
    }
  }

  /** Helper method to find scrollable parent */
  private View findScrollParent(View view) {
    View parent = (View) view.getParent();
    if (parent == null) return null;

    String className = parent.getClass().getSimpleName();
    if (className.contains("ScrollView") || className.contains("RecyclerView") ||
        className.contains("ListView") || className.contains("NestedScrollView")) {
      return parent;
    }

    return findScrollParent(parent);
  }

  private final Runnable measureAndLayout = new Runnable() {
    @Override
    public void run() {
      measure(
        MeasureSpec.makeMeasureSpec(getWidth(), MeasureSpec.EXACTLY),
        MeasureSpec.makeMeasureSpec(getHeight(), MeasureSpec.EXACTLY)
      );
      layout(getLeft(), getTop(), getRight(), getBottom());
    }
  };

  @Override
  public void requestLayout() {
    super.requestLayout();
    // In Java, you access the member variable directly
    post(measureAndLayout);
  }

  @Override
  protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    int maxWidth = 0;
    int maxHeight = 0;

    for (int i = 0; i < getChildCount(); i++) {
      View child = getChildAt(i);
      if (child.getVisibility() != GONE) {
        // In Java, use measureChild or handle width/height constraints directly
        // For simplicity, let's assume you want to measure with the given specs
        // but allow the child to be as big as it wants (MeasureSpec.UNSPECIFIED for height here is a bit unusual
        // if the parent is also defining its size based on children.
        // Typically, you'd pass child-specific LayoutParams-derived MeasureSpecs.
        // However, to directly translate the Kotlin "MeasureSpec.UNSPECIFIED" for the child's height:
        measureChild(child, widthMeasureSpec, MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        // Or, if you want to respect padding and margins (more common):
        // measureChildWithMargins(child, widthMeasureSpec, 0, heightMeasureSpec, 0);

        maxWidth = Math.max(maxWidth, child.getMeasuredWidth());
        maxHeight = Math.max(maxHeight, child.getMeasuredHeight());
      }
    }

    final int finalWidth = Math.max(maxWidth, getSuggestedMinimumWidth());
    final int finalHeight = Math.max(maxHeight, getSuggestedMinimumHeight());

    setMeasuredDimension(finalWidth, finalHeight);

    finalCalculatedHeight = finalHeight;

    // Kept this part as it is as we may need it till we completely migrate to new architecture and turbo modules
    // Handling React Native specific parts
    // Make sure ThemedReactContext and UIManagerModule are correctly imported
    // and that your 'context' is indeed a ThemedReactContext.
    // This part requires your project to have React Native dependencies.

    // if (getContext() instanceof ThemedReactContext) {
    //     ThemedReactContext themedReactContext = (ThemedReactContext) getContext();
    //     themedReactContext.runOnNativeModulesQueueThread(new Runnable() {
    //         @Override
    //         public void run() {
    //             UIManagerModule uiManagerModule = themedReactContext.getNativeModule(UIManagerModule.class);
    //             if (uiManagerModule != null) {
    //                 Timber.tag("BaseNativeAdView").d("uiManagerModule.updateNodeSize called with: %d, %d", finalWidth, finalHeight);
    //                 uiManagerModule.updateNodeSize(getId(), finalWidth, finalHeight);
    //             }
    //         }
    //     });
    // }

  }

  public void destroy() {
    removeAllViews();
    if (nativeAd != null) {
      if (inMobiPackage != null && inMobiPackage.mNativeAds != null) {
        inMobiPackage.mNativeAds.remove(nativeAd);
      }
      nativeAd.destroy();
      nativeAd = null;
    }
  }
}
