package com.benzinga.app.inmobi;

import android.app.Activity;
import android.location.Location;
import android.location.LocationManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.benzinga.app.MainApplication;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.inmobi.ads.AdMetaInfo;
import com.inmobi.ads.InMobiAdRequestStatus;
import com.inmobi.ads.InMobiInterstitial;
import com.inmobi.ads.listeners.InterstitialAdEventListener;
import com.inmobi.cmp.ChoiceCmp;
import com.inmobi.cmp.ChoiceCmpCallback;
import com.inmobi.cmp.core.model.ACData;
import com.inmobi.cmp.core.model.GDPRData;
import com.inmobi.cmp.core.model.gbc.GoogleBasicConsents;
import com.inmobi.cmp.core.model.mspa.USRegulationData;
import com.inmobi.cmp.data.model.ChoiceStyle;
import com.inmobi.cmp.model.ActionButton;
import com.inmobi.cmp.model.ChoiceError;
import com.inmobi.cmp.model.DisplayInfo;
import com.inmobi.cmp.model.NonIABData;
import com.inmobi.cmp.model.PingReturn;
import com.inmobi.sdk.InMobiSdk;
import com.inmobi.sdk.SdkInitializationListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import timber.log.Timber;

public class InMobiInterstitialAdModule extends ReactContextBaseJavaModule {
    private static final String INMOBI_MODULE_TAG = "InMobiInterstitialAdMod";
    private final ReactApplicationContext reactContext;
    private Map<String, Object> interstitialAds = new HashMap<>();

    public InMobiInterstitialAdModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return "InMobiInterstitialAdManager";
    }

    private void sendEvent(String eventName, @Nullable WritableMap params) {
      reactContext
          .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
          .emit(eventName, params);
  }

  @ReactMethod
  public void addListener(String eventName) {
    // This method is required for React Native's event emitter to work on Android
    // No implementation needed, just having the method is enough
  }

  @ReactMethod
  public void removeListeners(Integer count) {
    // This method is required for React Native's event emitter to work on Android
    // No implementation needed, just having the method is enough
  }

    @ReactMethod
    public void initSDK(String accountId, Promise promise) {
        try {
          InMobiSdk.setLogLevel(InMobiSdk.LogLevel.DEBUG);
          JSONObject consentObject = new JSONObject();
          InMobiSdk.init(getReactApplicationContext(), accountId, consentObject, new SdkInitializationListener() {
              @Override
              public void onInitializationComplete(@Nullable Error error) {
                if (null != error) {
                  promise.reject("INIT_ERROR", error.getMessage(), error);
                } else {
                  promise.resolve(accountId);
                }
              }
            });
        } catch (Exception e) {
            promise.reject("INIT_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void setGender(String gender) {
      InMobiSdk.setGender(gender.equals("MALE") ? InMobiSdk.Gender.MALE : InMobiSdk.Gender.FEMALE);
    }

    @ReactMethod
    public void setAge(int age) {
      InMobiSdk.setAge(age);
    }

    @ReactMethod
    public void setAgeGroup(String ageGroup) {
      /**
       * BELOW_18,
       * BETWEEN_18_AND_24,
       * BETWEEN_25_AND_29,
       * BETWEEN_30_AND_34,
       * BETWEEN_35_AND_44,
       * BETWEEN_45_AND_54,
       * BETWEEN_55_AND_65,
       * ABOVE_65;
       */
      InMobiSdk.setAgeGroup(InMobiSdk.AgeGroup.valueOf(ageGroup));
    }

    @ReactMethod
    public void setAgeRestricted(boolean isAgeRestricted) {
      InMobiSdk.setIsAgeRestricted(isAgeRestricted);
    }

    @ReactMethod
    public void setLocation(String city, String state, String country) {
      InMobiSdk.setLocationWithCityStateCountry(city, state, country);
    }

    @ReactMethod
    public void setLocationLatLng(double latitude, double longitude) {
      Location location = new Location(InMobiInterstitialAdModule.class.getName());
      location.setLatitude(latitude);
      location.setLongitude(longitude);
      InMobiSdk.setLocation(location);
    }

    @ReactMethod
    public void setLastKnownLocationFromGPS(Promise promise) {
      android.location.LocationManager locationManager = (android.location.LocationManager)
        getReactApplicationContext().getSystemService(android.content.Context.LOCATION_SERVICE);
      android.location.Location location = null;
      try {
        if (reactContext.checkSelfPermission(android.Manifest.permission.ACCESS_FINE_LOCATION) ==
          android.content.pm.PackageManager.PERMISSION_GRANTED ||
          reactContext.checkSelfPermission(android.Manifest.permission.ACCESS_COARSE_LOCATION) ==
            android.content.pm.PackageManager.PERMISSION_GRANTED) {
          location = locationManager.getLastKnownLocation(android.location.LocationManager.GPS_PROVIDER);
          InMobiSdk.setLocation(location);
          promise.resolve(location);
        } else {
          promise.reject("LOCATION_ERROR", new Throwable("Location permissions not granted"));
        }
      } catch (Exception e) {
        Timber.tag(INMOBI_MODULE_TAG).e("Error getting location: %s", e.getMessage());
        promise.reject("LOCATION_ERROR", e);
      }
    }

  @ReactMethod
  public void setLastKnownLocationFromNetwork(Promise promise) {
    android.location.LocationManager locationManager = (android.location.LocationManager)
      getReactApplicationContext().getSystemService(android.content.Context.LOCATION_SERVICE);
    android.location.Location location = null;
    try {
      if (reactContext.checkSelfPermission(android.Manifest.permission.ACCESS_COARSE_LOCATION) ==
          android.content.pm.PackageManager.PERMISSION_GRANTED) {
        location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
        InMobiSdk.setLocation(location);
        promise.resolve(location);
      } else {
        promise.reject("LOCATION_ERROR", new Throwable("Location permissions not granted"));
      }
    } catch (Exception e) {
      Timber.tag(INMOBI_MODULE_TAG).e("Error getting location: %s", e.getMessage());
      promise.reject("LOCATION_ERROR", e);
    }
  }

    @ReactMethod
    public void loadInterstitialAd(String placementId, Promise promise) {
        try {
            InMobiInterstitial interstitial = new InMobiInterstitial(reactContext, Long.parseLong(placementId), new InterstitialAdEventListener() {
              @Override
              public void onAdLoadSucceeded(@NonNull InMobiInterstitial ad, @NonNull AdMetaInfo info) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdLoaded", params);
              }

              @Override
              public void onAdLoadFailed(@NonNull InMobiInterstitial ad, @NonNull InMobiAdRequestStatus status) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                params.putString("message", String.valueOf(status.getMessage()));
                sendEvent("inMobiInterstitialAdLoadFailed", params);
              }

              @Override
              public void onAdClicked(@NonNull InMobiInterstitial ad, Map < Object, Object > params) {
                WritableMap eventParams = Arguments.createMap();
                eventParams.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdClicked", eventParams);
              }

              @Override
              public void onAdWillDisplay(@NonNull InMobiInterstitial ad) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdWillPresent", params);
              }

              @Override
              public void onAdDisplayed(@NonNull InMobiInterstitial ad, @NonNull AdMetaInfo info) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdDidPresent", params);
              }

              @Override
              public void onAdDisplayFailed(@NonNull InMobiInterstitial ad) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdPresentFailed", params);
              }

              @Override
              public void onAdDismissed(@NonNull InMobiInterstitial ad) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdDidDismiss", params);
              }

              @Override
              public void onAdImpression(@NonNull InMobiInterstitial ad) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdImpression", params);
              }

              @Override
              public void onUserLeftApplication(@NonNull InMobiInterstitial ad) {
                WritableMap params = Arguments.createMap();
                params.putString("placementId", String.valueOf(placementId));
                sendEvent("inMobiInterstitialAdUserLeftApplication", params);
              }
            });
            interstitial.load();
            interstitialAds.put(placementId, interstitial);
            promise.resolve(true);
        } catch (Exception e) {
            Timber.tag(INMOBI_MODULE_TAG).e(e);
            promise.reject("LOAD_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void isInterstitialAdReady(String placementId, Promise promise) {
        try {
            // Check if interstitial ad is ready
            InMobiInterstitial interstitial = (InMobiInterstitial) interstitialAds.get(placementId);
            boolean isReady = interstitial != null && interstitial.isReady();
            promise.resolve(isReady);
        } catch (Exception e) {
            promise.reject("CHECK_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void showInterstitialAd(String placementId, Promise promise) {
        try {
            // Show interstitial ad
            InMobiInterstitial interstitial = (InMobiInterstitial) interstitialAds.get(placementId);
            if (interstitial != null && interstitial.isReady()) {
                interstitial.show();
                promise.resolve(true);
            } else {
                promise.resolve(false);
            }
            promise.resolve(true); // Placeholder
        } catch (Exception e) {
            promise.reject("SHOW_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void initCMPSDK(String cmpCode, Promise promise) {
      try {
        MainApplication application = (MainApplication) getReactApplicationContext().getApplicationContext();
        String packageName = getReactApplicationContext().getPackageName();
        ChoiceCmp.startChoice(application, packageName, cmpCode, new ChoiceCmpCallback() {
          @Override
          public void onActionButtonClicked(@NonNull ActionButton actionButton) {
            WritableMap params = Arguments.createMap();
            params.putString("actionButton", String.valueOf(actionButton.toString()));
            sendEvent("inMobiCMPOnActionButtonClicked", params);
          }

          @Override
          public void onCCPAConsentGiven(@NonNull String consentString) {
            WritableMap params = Arguments.createMap();
            params.putString("consentString", consentString);
            sendEvent("inMobiCMPOnCCPAConsentGiven", params);
          }

          @Override
          public void onCMPUIStatusChanged(@NonNull DisplayInfo status) {
            WritableMap params = Arguments.createMap();
            params.putString("status", status.getDisplayStatus().getValue());
            sendEvent("inMobiCMPOnCMPUIStatusChanged", params);
          }

          @Override
          public void onCmpError(@NonNull ChoiceError error) {
            WritableMap params = Arguments.createMap();
            params.putString("message", error.getMessage());
            sendEvent("inMobiCMPOnCmpError", params);
          }

          @Override
          public void onCmpLoaded(@NonNull PingReturn info) {
            WritableMap params = Arguments.createMap();
            params.putString("cmpId", String.valueOf(info.getCmpId()));
            sendEvent("inMobiCMPOnCmpLoaded", params);
          }

          @Override
          public void onGoogleBasicConsentChange(@NonNull GoogleBasicConsents consents) {
            WritableMap params = Arguments.createMap();
            params.putString("consents", consents.toString());
            sendEvent("inMobiCMPOnGoogleBasicConsentChange", params);
          }

          @Override
          public void onGoogleVendorConsentGiven(@NonNull ACData acData) {
            WritableMap params = Arguments.createMap();
            params.putString("acData", acData.getAcString());
            sendEvent("inMobiCMPOnGoogleVendorConsentGiven", params);
          }

          @Override
          public void onIABVendorConsentGiven(@NonNull GDPRData gdprData) {
            WritableMap params = Arguments.createMap();
            params.putString("gdprData", gdprData.getGppString());
            sendEvent("inMobiCMPOnIABVendorConsentGiven", params);
          }

          @Override
          public void onNonIABVendorConsentGiven(@NonNull NonIABData nonIABData) {
            WritableMap params = Arguments.createMap();
            params.putString("nonIABData", nonIABData.getMetadata());
            sendEvent("inMobiCMPOnNonIABVendorConsentGiven", params);
          }

          @Override
          public void onReceiveUSRegulationsConsent(@NonNull USRegulationData usRegulationData) {
            WritableMap params = Arguments.createMap();
            params.putString("usRegulationData", usRegulationData.toString());
            sendEvent("inMobiCMPOnReceiveUSRegulationsConsent", params);
          }

          @Override
          public void onUserMovedToOtherState() {
            WritableMap params = Arguments.createMap();
            sendEvent("inMobiCMPOnUserMovedToOtherState", params);
          }
        }, new ChoiceStyle.Builder().build());
        promise.resolve(true);
      } catch (Exception e) {
        promise.reject("CMP_INIT_ERROR", e.getMessage(), e);
      }
    }

    @ReactMethod
    public void showCMPPreferences() {
      Activity activity = getCurrentActivity();
      if (activity != null) {
        ChoiceCmp.forceDisplayUI(activity);
      } else {
        Timber.tag(INMOBI_MODULE_TAG).e("Activity is null while showCMPPreferences");
      }
    }
}
