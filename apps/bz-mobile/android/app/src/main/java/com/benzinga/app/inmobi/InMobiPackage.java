package com.benzinga.app.inmobi;

import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;
import com.inmobi.ads.InMobiNative;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class InMobiPackage implements ReactPackage {

  // Hold instances of InMobiNative objects for later use
  final List<InMobiNative> mNativeAds = new ArrayList<>();


  @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
      List<ViewManager> managers = new ArrayList<>();
      managers.add(new RNInFeedAdManager(this));
      managers.add(new RNSplashAdManager(this));
      managers.add(new RNPreRollAdManager(this));
      return managers;
    }

    @Override
    public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();
        modules.add(new InMobiInterstitialAdModule(reactContext));
        return modules;
    }
}
