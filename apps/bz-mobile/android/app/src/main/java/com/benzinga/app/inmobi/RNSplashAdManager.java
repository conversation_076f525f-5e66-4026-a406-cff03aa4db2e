package com.benzinga.app.inmobi;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.common.MapBuilder;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;
import java.util.Map;

import timber.log.Timber;

public class RNSplashAdManager extends SimpleViewManager<SplashAdView> {
  public static final String REACT_CLASS = "RNInMobiSplashAd";

  private static final int CMD_INIT = 1;
  private static final int CMD_LOAD = 2;
  private static final int CMD_SHOW = 3;
  private static final int CMD_DESTROY = 4;
  final InMobiPackage inMobiPackage;

  public RNSplashAdManager(@NonNull InMobiPackage inMobiPackage) {
    this.inMobiPackage = inMobiPackage;
  }

  @NonNull @Override public String getName() { return REACT_CLASS; }
  @NonNull @Override protected SplashAdView createViewInstance(@NonNull ThemedReactContext reactContext) {
    return new SplashAdView(reactContext, inMobiPackage);
  }

  @ReactProp(name = "placementId")
  public void setPlacementId(SplashAdView view, String placementId) {
    try {
      if (placementId != null && !placementId.isEmpty()) {
        long placementIdLong = Long.parseLong(placementId);
        view.setPlacementId(placementIdLong);
        Timber.tag("RNSplashAdManager").d("Set placement ID: %s -> %d", placementId, placementIdLong);
      } else {
        Timber.tag("RNSplashAdManager").e("Invalid placement ID: %s", placementId);
      }
    } catch (NumberFormatException e) {
      Timber.tag("RNSplashAdManager").e("Error parsing placement ID %s: %s", placementId, e.getMessage());
    }
  }

  @ReactProp(name = "autoShowOnLoad", defaultBoolean = true)
  public void setAutoShowOnLoad(SplashAdView view, boolean auto) { view.setAutoShowOnLoad(auto); }

  @Nullable @Override public Map<String, Integer> getCommandsMap() {
    return MapBuilder.of("init", 1, "load", 2, "show", 3, "destroy", 4);
  }

  @Override public void receiveCommand(@NonNull SplashAdView root, String commandId, @Nullable ReadableArray args) {
    int id = Integer.parseInt(commandId);
    switch (id) {
      case CMD_INIT:
        if (args != null && args.size() > 0) {
          String placementIdStr = args.getString(0);
          try {
            if (placementIdStr != null && !placementIdStr.isEmpty()) {
              long placementId = Long.parseLong(placementIdStr);
              root.setPlacementId(placementId);
              Timber.tag("RNSplashAdManager").d("Init command - Set placement ID: %s -> %d", placementIdStr, placementId);
            } else {
              Timber.tag("RNSplashAdManager").e("Init command - Invalid placement ID: %s", placementIdStr);
            }
          } catch (NumberFormatException e) {
            Timber.tag("RNSplashAdManager").e("Init command - Error parsing placement ID %s: %s", placementIdStr, e.getMessage());
          }
        }
        if (args != null && args.size() > 1) root.setAutoShowOnLoad(args.getBoolean(1));
        break;
      case CMD_LOAD: root.load(); break;
      case CMD_SHOW: root.show(); break;
      case CMD_DESTROY: root.destroy(); break;
    }
  }

  @Nullable @Override public Map getExportedCustomDirectEventTypeConstants() {
    return MapBuilder.<String, Object>builder()
      .put("onAdLoaded", MapBuilder.of("registrationName", "onAdLoaded"))
      .put("onAdLoadFailed", MapBuilder.of("registrationName", "onAdLoadFailed"))
      .put("onAdClicked", MapBuilder.of("registrationName", "onAdClicked"))
      .put("onAdImpression", MapBuilder.of("registrationName", "onAdImpression"))
      .put("onAdStatusChanged", MapBuilder.of("registrationName", "onAdStatusChanged"))
      .build();
  }
}
