package com.benzinga.app.inmobi;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/** In-Feed native ad: fits within scrolling feeds (RecyclerView/List). */
public class InFeedAdView extends BaseNativeAdView {
  public InFeedAdView(@NonNull Context context) { super(context); }
  public InFeedAdView(@NonNull Context context, @NonNull InMobiPackage inMobiPackage) { super(context, inMobiPackage); }
  public InFeedAdView(@NonNull Context context, @Nullable AttributeSet attrs) { super(context, attrs); }
}
