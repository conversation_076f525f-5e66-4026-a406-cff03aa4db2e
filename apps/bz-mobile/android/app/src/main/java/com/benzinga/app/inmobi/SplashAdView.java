package com.benzinga.app.inmobi;

import android.content.Context;
import android.util.AttributeSet;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/** Splash native ad: show at app launch inside your own splash layout (not full‑screen interstitial). */
public class SplashAdView extends BaseNativeAdView {
  public SplashAdView(@NonNull Context context) { super(context); }
  public SplashAdView(@NonNull Context context, @NonNull InMobiPackage inMobiPackage) { super(context, inMobiPackage); }
  public SplashAdView(@NonNull Context context, @Nullable AttributeSet attrs) { super(context, attrs); }
}
