package com.benzinga.app.inmobi;

import android.content.Context;
import android.util.AttributeSet;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/** Pre‑Roll native ad: renders the default video-capable native layout before content. */
public class PreRollAdView extends BaseNativeAdView {
  public PreRollAdView(@NonNull Context context) { super(context); }
  public PreRollAdView(@NonNull Context context, @NonNull InMobiPackage inMobiPackage) { super(context, inMobiPackage); }
  public PreRollAdView(@NonNull Context context, @Nullable AttributeSet attrs) { super(context, attrs); }
}
