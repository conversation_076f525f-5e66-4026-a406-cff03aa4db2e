const styledReturn = jest.fn();

styledReturn.attrs = jest.fn().mockReturnValue(jest.fn());
export const keyframes = jest.fn().mockReturnValue(jest.fn());

const styled = jest.fn().mockReturnValue(styledReturn);

styled.div = jest.fn();
styled.button = jest.fn();
styled.span = jest.fn();
styled.a = jest.fn();
styled.ul = jest.fn();
styled.li = jest.fn();
styled.dialog = jest.fn();
styled.p = jest.fn();
styled.h1 = jest.fn();
styled.h2 = jest.fn();
styled.h6 = jest.fn();
styled.h5 = jest.fn();
styled.input = jest.fn();
styled.img = jest.fn();
styled.table = jest.fn();
styled.small = jest.fn();
styled.keyframes = jest.fn();

export { styled as default, styled };
const css = jest.fn();

export { css };
