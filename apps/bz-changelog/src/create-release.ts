import * as fsp from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import {
  GitLabCreateMergeRequest,
  GitLabMergeRequestApprovalRules,
  GitLabProjectMember,
  GitLabRelease,
  GitLabUser,
} from './entities';
import { capitalize } from '@benzinga/utils';
import { updateMD } from './update-file';

dotenv.config({ path: path.resolve(__dirname, '../../../../../../.env') });

const GITLAB_HOST = 'https://gitlab.benzinga.io';
const GITLAB_API_URL = `${GITLAB_HOST}/api/v4`;
const GITLAB_PROJECT_ID = 195;
const GITLAB_PRIVATE_TOKEN = process.env.GITLAB_PRIVATE_TOKEN;

export type ReleaseStrategy = 'major' | 'minor' | 'patch';
export type ProjectName = 'bz' | 'money' | 'india';

interface ProjectMapValue {
  branchPrefix: string;
  name: string;
  tagPrefix: string;
  releaseNamePrefix: string;
}

interface ChangelogEntry {
  version: string;
  features: string[];
  bugs: string[];
  tasks: string[];
}

const projectsMap: Record<ProjectName, ProjectMapValue> = {
  bz: {
    branchPrefix: 'BZFE',
    name: 'bz',
    releaseNamePrefix: 'BenzingaNext',
    tagPrefix: 'benzinga/benzinga-next',
  },
  india: {
    branchPrefix: 'INDIA',
    name: 'india',
    releaseNamePrefix: 'BenzingaIndia',
    tagPrefix: 'benzinga/benzinga-india',
  },
  money: {
    branchPrefix: 'MNY',
    name: 'money',
    releaseNamePrefix: 'MoneyNext',
    tagPrefix: 'benzinga/money-next',
  },
};

enum GitLabAccessLevel {
  NO_ACCESS = 0,
  MINIMAL_ACCESS = 5,
  GUEST = 10,
  REPORTER = 20,
  DEVELOPER = 30,
  MAINTAINER = 40,
  OWNER = 50,
}

const execAsync = promisify(exec);

const runCommand = async (command: string, showStdout = true): Promise<string | null> => {
  try {
    const { stderr, stdout } = await execAsync(command);
    if (stderr) {
      console.error(`Command warning: ${stderr}`);
    }
    if (showStdout) {
      return stdout.trim();
    }
    return null;
  } catch (error) {
    console.error(`Command failed: ${error}`);
    throw error;
  }
};

const validateTagName = (tagName: string): boolean => {
  const tagRegex = /^[a-zA-Z0-9-]+\/[a-zA-Z0-9-]+@\d+\.\d+\.\d+$/;
  return tagRegex.test(tagName);
};

const checkIfTagExists = async (tagName: string): Promise<boolean> => {
  try {
    await runCommand(`git rev-parse ${tagName}`, false);
    return true;
  } catch (error) {
    return false;
  }
};

const updatePackageVersion = async (packagePath: string, newVersion: string): Promise<void> => {
  const fullPath = path.resolve(packagePath);
  const packageJson = JSON.parse(await fsp.readFile(fullPath, 'utf-8'));
  packageJson.version = newVersion;
  await fsp.writeFile(fullPath, JSON.stringify(packageJson, null, 2));
};

const getPackageVersion = async (packagePath: string): Promise<string> => {
  const fullPath = path.resolve(packagePath);
  const packageJson = JSON.parse(await fsp.readFile(fullPath, 'utf-8'));
  return packageJson.version;
};

const bumpVersion = (currentVersion: string, strategy: ReleaseStrategy): string => {
  const [major, minor, patch] = currentVersion.split('.').map(Number);
  switch (strategy) {
    case 'major':
      return `${major + 1}.0.0`;
    case 'minor':
      return `${major}.${minor + 1}.0`;
    case 'patch':
      return `${major}.${minor}.${patch + 1}`;
    default:
      throw new Error(`Invalid bump strategy: ${strategy}`);
  }
};

const parseChangelog = (content: string): ChangelogEntry[] => {
  const lines = content.split('\n');
  const entries: ChangelogEntry[] = [];
  let currentEntry: ChangelogEntry | null = null;
  let currentSection: 'features' | 'bugs' | 'tasks' | null = null;

  for (const line of lines) {
    if (line.startsWith('# v')) {
      if (currentEntry) {
        entries.push(currentEntry);
      }
      currentEntry = {
        bugs: [],
        features: [],
        tasks: [],
        version: line.substring(2).trim(),
      };
      currentSection = null;
    } else if (line.startsWith('## Feature')) {
      currentSection = 'features';
    } else if (line.startsWith('## Bug')) {
      currentSection = 'bugs';
    } else if (line.startsWith('## Task')) {
      currentSection = 'tasks';
    } else if (currentEntry && currentSection && line.trim()) {
      currentEntry[currentSection].push(line.trim());
    }
  }

  if (currentEntry) {
    entries.push(currentEntry);
  }

  return entries;
};

const formatChangelogEntry = (entry: ChangelogEntry): string => {
  let formatted = `# ${entry.version}\n\n`;

  if (entry.features.length > 0) {
    formatted += '## Features:\n' + entry.features.map(f => `${f}`).join('\n') + '\n\n';
  }
  if (entry.bugs.length > 0) {
    formatted += '## Bugs:\n' + entry.bugs.map(b => `${b}`).join('\n') + '\n\n';
  }
  if (entry.tasks.length > 0) {
    formatted += '## Tasks:\n' + entry.tasks.map(t => `${t}`).join('\n') + '\n\n';
  }

  return formatted.trim();
};

const getChangelogEntryForVersion = (entries: ChangelogEntry[], version: string): ChangelogEntry | undefined => {
  return entries.find(entry => entry.version === version);
};

const isGitLabTokenValid = async (
  token: string,
  apiUrl: string,
): Promise<{ hasAccess: boolean; userId: number | undefined }> => {
  try {
    const userResponse = await fetch(`${apiUrl}/user`, {
      headers: {
        'PRIVATE-TOKEN': token ?? '',
      },
    });

    if (!userResponse.ok) {
      console.error(`Token validation failed. Status: ${userResponse.status}`);
      return {
        hasAccess: false,
        userId: undefined,
      };
    }

    const userData: GitLabUser = await userResponse.json();

    const projectResponse = await fetch(`${apiUrl}/projects/${GITLAB_PROJECT_ID}/members/all/${userData.id}`, {
      headers: { 'PRIVATE-TOKEN': token },
    });

    if (!projectResponse.ok) {
      console.error(`Failed to fetch project access. Status: ${projectResponse.status}`);
      return {
        hasAccess: false,
        userId: userData.id,
      };
    }
    const projectMember: GitLabProjectMember = await projectResponse.json();
    const accessLevel = projectMember.access_level;

    if (accessLevel >= GitLabAccessLevel.DEVELOPER) {
      return {
        hasAccess: true,
        userId: userData.id,
      };
    } else {
      console.error(`User does not have sufficient permissions. Developer access or higher is required.`);
      return {
        hasAccess: false,
        userId: userData.id,
      };
    }
  } catch (error) {
    console.error('Error validating GitLab token:', error);
    return {
      hasAccess: false,
      userId: undefined,
    };
  }
};

const createGitLabRelease = async (
  tagName: string,
  releaseName: string,
  releaseNotes: string,
  privateToken = GITLAB_PRIVATE_TOKEN,
) => {
  const url = `${GITLAB_API_URL}/projects/${GITLAB_PROJECT_ID}/releases`;
  const response = await fetch(url, {
    body: JSON.stringify({
      description: releaseNotes,
      name: releaseName,
      tag_name: tagName,
    }),
    headers: {
      'Content-Type': 'application/json',
      'PRIVATE-TOKEN': privateToken ?? '',
    },
    method: 'POST',
  });

  if (!response.ok) {
    throw new Error(`Failed to create GitLab release: ${response.statusText}`);
  }

  const release: GitLabRelease = await response.json();
  console.log(`GitLab release created: ${release.name} - ${release._links?.self}`);
  return release;
};

export const createMergeRequest = async (
  sourceBranch: string,
  targetBranch: string,
  title: string,
  description: string,
  userId?: number,
): Promise<GitLabCreateMergeRequest> => {
  if (!GITLAB_PROJECT_ID || !GITLAB_PRIVATE_TOKEN) {
    throw new Error('GitLab project ID or private token not set. Please check your environment variables.');
  }

  const url = `${GITLAB_API_URL}/projects/${GITLAB_PROJECT_ID}/merge_requests`;
  try {
    const createMergeRequestResponse = await fetch(url, {
      body: JSON.stringify({
        approvals_before_merge: 0,
        assignee_id: userId,
        description: description,
        remove_source_branch: true,
        source_branch: sourceBranch,
        target_branch: targetBranch,
        title: title,
      }),
      headers: {
        'Content-Type': 'application/json',
        'PRIVATE-TOKEN': GITLAB_PRIVATE_TOKEN ?? '',
      },
      method: 'POST',
    });

    if (!createMergeRequestResponse.ok) {
      const errorBody = await createMergeRequestResponse.text();
      throw new Error(
        `Failed to create merge request: ${createMergeRequestResponse.status} ${createMergeRequestResponse.statusText}\n${errorBody}`,
      );
    }

    const mergeRequest: GitLabCreateMergeRequest = await createMergeRequestResponse.json();
    console.log(`Merge request created: ${mergeRequest.web_url}`);

    return mergeRequest;
  } catch (error) {
    console.error('Error creating merge request:', error);
    throw error;
  }
};

const updateApprovalRules = async (id: number) => {
  try {
    const url = `${GITLAB_API_URL}/projects/${GITLAB_PROJECT_ID}/merge_requests/${id}/approval_rules`;
    const approvalRulesResponse = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'PRIVATE-TOKEN': GITLAB_PRIVATE_TOKEN ?? '',
      },
      method: 'GET',
    });

    if (!approvalRulesResponse.ok) {
      throw new Error(`Failed to get approval rules: ${approvalRulesResponse.statusText}`);
    }

    const approvalRules: GitLabMergeRequestApprovalRules[] = await approvalRulesResponse.json();

    if (Array.isArray(approvalRules)) {
      const qaApprovalRuleId: number | undefined = approvalRules.find(rule => rule.name === 'QA')?.id;

      console.log(`Approval rule for QA found: ${qaApprovalRuleId}`);

      if (qaApprovalRuleId) {
        const approvalRuleUrl = `${url}/${qaApprovalRuleId}`;
        const approvalRuleResponse = await fetch(approvalRuleUrl, {
          body: JSON.stringify({
            approvals_required: 0,
          }),
          headers: {
            'Content-Type': 'application/json',
            'PRIVATE-TOKEN': GITLAB_PRIVATE_TOKEN ?? '',
          },
          method: 'PUT',
        });
        if (!approvalRuleResponse.ok) {
          console.warn(
            `Failed to update merge request approval rule for QA: ${approvalRuleResponse.statusText} - ${approvalRuleUrl}`,
          );
        } else {
          console.log('Successfully set merge request approval rule for QA to 0');
        }
      } else {
        console.error('Could not find approval rule id for QA role');
      }
    }
  } catch (error) {
    console.error('Error updating approval rules:', error);
    throw error;
  }
};

export const createReleaseBranchAndTag = async (
  projectName: ProjectName,
  releaseStrategy: ReleaseStrategy,
  privateToken = GITLAB_PRIVATE_TOKEN,
) => {
  const project = projectsMap[projectName.toLowerCase()] as ProjectMapValue;
  if (!project) {
    console.error('Invalid project name. Expected: bz, money, or india');
    process.exit(1);
  }

  if (!privateToken) {
    console.error('GITLAB_PRIVATE_TOKEN is not set');
    process.exit(1);
  }

  if (!['major', 'minor', 'patch'].includes(releaseStrategy)) {
    console.error('Invalid release strategy. Expected: major, minor, or patch');
    process.exit(1);
  }

  const isTokenValid = await isGitLabTokenValid(privateToken, GITLAB_API_URL);

  if (!isTokenValid.hasAccess) {
    process.exit(1);
  }

  try {
    const SOURCE_BRANCH = 'master';

    await runCommand(`git checkout ${SOURCE_BRANCH}`);
    await runCommand('git pull');
    await runCommand('yarn install');
    console.log(`Checked out latest ${SOURCE_BRANCH}`);

    const projectPackageJsonPath = `apps/${project.name}/package.json`;
    const changelogPath = `apps/${project.name}/CHANGELOG.md`;
    const currentVersion = await getPackageVersion(projectPackageJsonPath);
    const newVersion = bumpVersion(currentVersion, releaseStrategy);
    const tagName = `${project.tagPrefix}@${newVersion}`;

    if (!validateTagName(tagName)) {
      console.error('Invalid tag name format. Expected format: project/app-name@x.y.z');
      process.exit(1);
    }

    const tagMessage = `Release ${newVersion}: ${capitalize(releaseStrategy)}`;

    const releaseBranchName = `release/${project.branchPrefix}-v${newVersion}`;

    const tagAlreadyExists = await checkIfTagExists(tagName);

    if (tagAlreadyExists) {
      console.error(`Tag ${tagName} already exists. Aborting release process.`);
      process.exit(1);
    }

    await runCommand(`git checkout -b ${releaseBranchName}`);
    console.log(`Created and checked out ${releaseBranchName}`);

    console.log('Updating changelog...');
    await updateMD(project.name, true);
    console.log('Changelog updated');

    const changelogContent = await fsp.readFile(changelogPath, 'utf-8');
    const parsedChangelog = parseChangelog(changelogContent);
    const versionEntry = getChangelogEntryForVersion(parsedChangelog, `v${newVersion}`);

    if (!versionEntry) {
      console.warn(`No changelog entry found for version ${newVersion}`);
    }

    const totalNumberOfChanges =
      Number(versionEntry?.bugs?.length) + Number(versionEntry?.tasks?.length) + Number(versionEntry?.features?.length);

    if (isNaN(totalNumberOfChanges) || totalNumberOfChanges === 0) {
      console.error(
        `This release has no changes. Please ensure there are changelog entries for version ${newVersion} before creating a release.`,
      );
      await runCommand(`git checkout ${SOURCE_BRANCH}`);
      await runCommand(`git branch -D ${releaseBranchName}`);
      process.exit(1);
    }

    console.log('Updating package version...');
    await updatePackageVersion(projectPackageJsonPath, newVersion);
    console.log(`Package version updated to ${newVersion}`);

    await runCommand(`git add ${projectPackageJsonPath}`);
    await runCommand(`git add ${changelogPath}`);
    await runCommand('git add changelog/');

    const formattedChangelog = versionEntry
      ? formatChangelogEntry(versionEntry)
      : 'No changelog entry found for this version.';

    await runCommand(`git commit -m "Bumped version to ${newVersion} and updated changelog"`);
    console.log('Committed changelog update');

    await runCommand(`git tag -a ${tagName} -m "${tagMessage}"`);
    console.log(`Created tag ${tagName}`);

    await runCommand(`git push -u origin ${releaseBranchName}`, false);
    console.log(`🚀 Pushed branch ${releaseBranchName} to remote`);

    await runCommand(`git push origin ${tagName}`);
    console.log(`🚀 Pushed tag ${tagName} to remote`);

    console.log('Creating GitLab release...');
    const fullReleaseNotes = `${formattedChangelog}`;
    const release = await createGitLabRelease(
      tagName,
      `${project.releaseNamePrefix} Version ${newVersion}`,
      fullReleaseNotes,
      privateToken,
    );

    console.log('Creating GitLab merge request...');
    const mergeRequest = await createMergeRequest(
      releaseBranchName,
      SOURCE_BRANCH,
      `${project.releaseNamePrefix} Release ${newVersion}`,
      `This merge request bumps the version to ${newVersion} and updates the changelog.`,
      isTokenValid.userId,
    );

    if (mergeRequest.iid) {
      console.log(`Updating GitLab approval rules for merge request: ${mergeRequest.iid}`);
      await updateApprovalRules(mergeRequest.iid);
    } else {
      console.error('No merge request id found');
    }

    let successMessage = '✅ Release branch, tag, and merge request created successfully!\n\n';
    successMessage += `🔗 Release: ${release._links?.self}\n`;
    successMessage += `🔗 Tag: ${GITLAB_HOST}/benzinga/fusion/-/tags/${tagName}\n`;
    successMessage += `🔗 Merge Request: ${mergeRequest.web_url}\n`;

    console.log(successMessage);
  } catch (error) {
    console.error('Failed to create release branch and tag:', error);
  }
};
