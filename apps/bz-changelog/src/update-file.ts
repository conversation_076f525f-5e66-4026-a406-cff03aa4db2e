import fs from 'fs';
import path from 'path';
import { ChangeLog, getGitBranchName, parseGitBranchName, getProjectDisplay, getProjectFolder } from './core';

const updateMDInternal = async (projectName?: string, filterByProject?: boolean) => {
  projectName = projectName === '' ? undefined : projectName;
  const branchName = getGitBranchName();
  if (!branchName) {
    console.log(`Can not obtain branch name`);
    return false;
  }
  const branchNameParsingResult = parseGitBranchName(branchName);
  if (branchNameParsingResult === null) {
    console.log(
      `the branch name "${branchName}" does not following branch name git conventions\nhttps://gitlab.benzinga.io/benzinga/Wiki/-/blob/master/development-process/git-conventions.md`,
    );
    return false;
  }

  // This operation makes sense only on 'release' branches
  if (branchNameParsingResult.type !== 'release') {
    console.error(`Must be a release branch`);
    return false;
  }

  // Check if project identifier is extracted from branch name
  const project = (projectName ?? branchNameParsingResult.project ?? '').toLowerCase();
  if (typeof project !== 'string') {
    console.error(`Missing project identifier in release branch "${branchName}"`);
    return false;
  }

  // Check if release version is extracted from branch name
  const releaseVersion = branchNameParsingResult.releaseVersion;
  if (typeof releaseVersion !== 'string') {
    console.error(`Missing release version in release branch "${branchName}"`);
    return false;
  }

  const packageFolder = getProjectFolder(project);

  if (packageFolder === null) {
    console.error(`Folder Cant be found`);
    return false;
  }

  // Chack if package exists
  if (packageFolder && !fs.existsSync(packageFolder)) {
    console.error(`Wrong package argument "${branchName}"`);
    return false;
  }

  const typeMapping = new Map();
  // Check if changelog folder exists
  const rootFilenames = getFileNamesFromDir(path.resolve(path.join(`changelog`)));
  const packageFilenames = getFileNamesFromDir(path.resolve(path.join(packageFolder, `changelog`)));
  parseDir(rootFilenames, typeMapping, project);
  parseDir(packageFilenames, typeMapping);
  GenerateReadMe(typeMapping, packageFolder, releaseVersion, project, filterByProject);
  deleteFiles(packageFilenames);
  return true;
};

const getFileNamesFromDir = (changelogFolder: string): string[] => {
  if (!fs.existsSync(changelogFolder)) {
    console.info(`Changelog folder "${changelogFolder}" does not exist`);
    return [];
  }

  // Read the files from changelog folder
  const filenames = fs.readdirSync(changelogFolder);
  if (filenames.length === 0) {
    console.info(`There are no files in changelog folder "${changelogFolder}"`);
    return [];
  }
  return filenames.map(f => path.join(changelogFolder, f));
};

const parseDir = (filenames: string[], typeMapping: Map<string, Map<string, ChangeLog[]>>, project?: string) => {
  // Calculate final text to be appended at the top of the file
  filenames.forEach(async filePath => {
    try {
      if (!fs.existsSync(filePath)) {
        console.log(`Skipping deleted file: ${filePath}`);
        return;
      }
      const changelogItem: ChangeLog = JSON.parse(fs.readFileSync(path.resolve(filePath)).toString());
      if (project && !(changelogItem.projects ?? changelogItem.affectedProjects)?.includes(project)) {
        return;
      } else {
        changelogItem.projects = changelogItem.projects?.filter(p => project !== p);

        if ((changelogItem.projects?.length ?? 0) !== 0) {
          const fileContent = JSON.stringify(changelogItem, null, 4);
          fs.writeFileSync(filePath, fileContent);
        } else {
          fs.unlinkSync(filePath);
        }
      }

      if (!typeMapping.has(changelogItem.type)) {
        typeMapping.set(changelogItem.type, new Map());
      }

      const mapping = typeMapping.get(changelogItem.type)!;
      const epic = changelogItem.epic ?? null;
      if (mapping.has(epic)) {
        mapping.get(epic)!.push(changelogItem);
      } else {
        mapping.set(epic, [changelogItem]);
      }
    } catch (error) {
      console.error(`Error processing file ${filePath}: ${error}`);
      return;
    }
  });
};

export const GenerateReadMe = (
  typeMapping: Map<string, Map<null | string, ChangeLog[]>>,
  packageFolder: string,
  releaseVersion: string,
  project: string,
  filterByProject?: boolean,
) => {
  const capitalize = (word: string) => {
    return word[0].toUpperCase() + word.slice(1).toLowerCase();
  };

  const ticketUrl = 'https://gitlab.benzinga.io/benzinga/fusion/-/issues/';

  const priority = ['feature', 'bug', 'task'];
  const ticketChangeLog = (changelogItem: ChangeLog, indent: number) => {
    return `${' '.repeat(indent)}- ${changelogItem.description} (${getProjectDisplay(changelogItem.project)})\n  ${' '.repeat(
      indent + 2,
    )}[${changelogItem.project}-${changelogItem.issueNumber}](${ticketUrl}${changelogItem.issueNumber})`;
  };

  const addEpicToRec = (epic: string | null, items: ChangeLog[], res: string, filterByProject?: boolean) => {
    if (epic !== null) {
      return `  - ${capitalize(epic)}\n  ${items
        .filter(item => (filterByProject ? item.project.toUpperCase() === project.toUpperCase() : true))
        .map(item => ticketChangeLog(item, 2))
        .join(`\n  `)}\n${res}`;
    } else {
      return `${res}  ${items
        .filter(item => (filterByProject ? item.project.toUpperCase() === project.toUpperCase() : true))
        .map(item => ticketChangeLog(item, 0))
        .join(`\n  `)}\n`;
    }
  };

  let res = '';
  priority.forEach(type => {
    if (typeMapping.has(type) == false) {
      return;
    }
    res += `\n## ${capitalize(type)}\n`;
    let resType = '';
    const mapping = typeMapping.get(type);

    let noEpic = '';
    if (mapping?.has(null)) {
      noEpic = addEpicToRec(null, mapping.get(null)!, '', filterByProject);
      mapping.delete(null);
    }

    Array.from(mapping?.entries() ?? [])
      .sort()
      .forEach(([, values]) => {
        resType = addEpicToRec(values[0].epic ?? null, values, resType, filterByProject);
      });
    res += resType + noEpic;
  });

  // Check if CHANGELOG.md file exists inside the package
  const changelogFile = path.join(packageFolder, `CHANGELOG.md`);
  if (!fs.existsSync(changelogFile)) {
    fs.writeFileSync(changelogFile, '');
    console.info(`created file "CHANGELOG.md" in "${packageFolder}"`);
  }

  // Append text at the top of the file
  const data = fs.readFileSync(changelogFile);
  const fd = fs.openSync(changelogFile, 'w+');
  const buffer = Buffer.from(`# ${releaseVersion}\n${res}\n`);

  fs.writeSync(fd, buffer, 0, buffer.length, 0); //write new data
  fs.writeSync(fd, data, 0, data.length, buffer.length); //append old data
  // or fs.appendFile(fd, data);
  fs.closeSync(fd);
  return true;
};

const deleteFiles = (filenames: string[]) => {
  // Delete the files
  filenames.forEach(file => {
    if (fs.existsSync(file)) {
      console.log('delete ${file}');
      fs.unlinkSync(file);
    }
  });
};

export const updateMD = async (projectName?: string, filterByProject?: boolean) => {
  if ((await updateMDInternal(projectName, filterByProject)) === false) {
    console.warn('Missing changelog file for current branch');
    process.exit(1);
  }
};
