import dayjs from 'dayjs';
import { EChartsOption } from 'echarts';
import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

// TODO - better chart to show daily portfolio changes
export const HistogramChart = ({ data, memberLabel }) => {
  const congress = data.map(item => {
    return {
      name: item.date,
      value: [item.date, item.daily_port_chg],
    };
  });
  const monthly = data.reduce((acc, day) => {
    const date = dayjs(day.date.slice(0, 7)).format('MMM YY');
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(day.daily_port_chg * 100);
    return acc;
  }, {});
  const monthlyData = Object.keys(monthly).map(date => {
    const sum = monthly[date].reduce((a, b) => a + b, 0);
    return {
      name: date,
      value: [date, sum / monthly[date].length],
    };
  });
  const options = React.useMemo(() => {
    return {
      grid: {
        bottom: '22%',
        height: 'auto',
        left: 0,
        right: 0,
        top: '1%',
        width: 'auto',
      },
      series: [
        {
          color: '#4f86f2',
          data: congress,
          name: memberLabel,
          showSymbol: false,
          stack: 'one',
          type: 'bar',
        },
      ],
      title: {
        show: false,
        text: 'Dynamic Data & Time Axis',
      },
      tooltip: {
        axisPointer: {
          animation: false,
        },
        trigger: 'axis',
        valueFormatter: val => val?.toFixed(2) + '%',
      },
      xAxis: {
        axisLine: { onZero: true },
        data: congress.map(item => item.name),
        interval: 0,
        show: false,
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        axisLine: { onZero: true },
        interval: 0,
        splitLine: {
          show: false,
        },
        type: 'value',
      },
    };
  }, [congress, memberLabel]);
  return <ReactECharts notMerge={true} option={options as EChartsOption} style={{ height: '100px', width: '480px' }} />;
};
