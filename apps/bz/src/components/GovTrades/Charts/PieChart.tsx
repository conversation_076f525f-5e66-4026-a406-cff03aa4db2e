import { EChartsOption } from 'echarts';
import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

export const PieChart = ({ data }) => {
  const buys = data.invested?.amount;
  const sells = data.withdrawn?.amount;
  const label =
    buys > sells
      ? "<span style='font-weight: 600'>Congress looks to be bullish!</span><br><span>They have bought more than they've sold.</span>"
      : "<span style='font-weight: 600'>Congress looks to be bearish!</span><br><span>They have sold more than they've bought.</span>";
  const options = React.useMemo(() => {
    return {
      label: {
        show: false,
      },
      legend: {
        show: false,
      },
      series: [
        {
          color: ['#56d288', '#f85656'],
          data: [
            { label: { normal: { show: false } }, name: 'Buys', value: buys },
            { label: { normal: { show: false } }, name: 'Sales', value: sells },
          ],
          emphasis: {
            disabled: true,
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowOffsetX: 0,
            },
          },
          name: label,
          radius: '80%',
          type: 'pie',
        },
      ],
      title: {
        show: false,
      },
      tooltip: {
        backgroundColor: '#1E1B39',
        borderColor: '#1E1B39',
        formatter: () => {
          return `${label}`;
        },
        textStyle: {
          color: '#fff',
        },
        trigger: 'item',
      },
    };
  }, [buys, label, sells]);
  return <ReactECharts notMerge={true} option={options as EChartsOption} style={{ height: '140px', width: '140px' }} />;
};
