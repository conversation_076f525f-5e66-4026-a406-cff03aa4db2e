import React from 'react';
import Link from 'next/link';
import styled from '@benzinga/themetron';
import dayjs from 'dayjs';
import { numberShorthand } from '@benzinga/utils';
import { GovernmentMember } from '@benzinga/gov-trades';

export const MemberCard: React.FC<GovernmentMember> = ({ filer_info, stats }) => {
  const { chamber, display_name, headshot, party, state } = filer_info;
  const { date_of_last_recorded_trade, number_of_trades, total_volume_of_trades } = stats ?? {};
  const parsedName = display_name.replaceAll(' ', '-').toLowerCase();
  return (
    <MemberCardWrapper className="member-card">
      <Link className="w-full" href={`/gov-trades/members/${parsedName}`}>
        <img
          className="w-full picture"
          onError={({ currentTarget }) => {
            currentTarget.onerror = null; // prevents looping
            currentTarget.src = '/next-assets/images/no_picture.png';
          }}
          src={headshot}
        />
      </Link>
      <div className="content">
        <div className="flex flex-col">
          <Link href={`/gov-trades/members/${parsedName}`}>
            <h3 className="name">{display_name}</h3>
          </Link>
          <div className="description">
            {chamber} ({party}-{state})
          </div>
        </div>
        <div className="flex flex-col gap-1 w-full">
          <div className="stat-wrapper">
            <div className="label">Trades</div>
            <div className="value">{number_of_trades}</div>
          </div>
          <div className="stat-wrapper">
            <div className="label">Volume</div>
            <div className="value">${numberShorthand(total_volume_of_trades, 1)}</div>
          </div>
          <div className="stat-wrapper">
            <div className="label">Last Traded</div>
            <div className="value">{dayjs(date_of_last_recorded_trade).format('MMM DD, YY')}</div>
          </div>
        </div>
      </div>
    </MemberCardWrapper>
  );
};

const MemberCardWrapper = styled.div`
  &.member-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 0 0;
    background: #f2f8ff;
    border-radius: 12px;

    .picture {
      width: 100%;
      height: 180px; // 168
      border-top-right-radius: 12px;
      border-top-left-radius: 12px;
      object-fit: cover;
      object-position: center;
    }

    .content {
      display: flex;
      height: 156px;
      padding: 12px;
      flex-direction: column;
      justify-content: space-between;
      align-self: stretch;

      .name {
        color: #192940;
        font-size: 16px;
        font-weight: 700;
        line-height: 18px;
        margin-bottom: 4px;
        &:hover {
          color: rgb(46 121 246);
        }
      }

      .description {
        color: #5b7292;
        font-size: 14px;
        font-weight: 500;
      }

      .stat-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        font-size: 14px;
        width: 100%;

        .label {
          color: #5b7292;
        }

        .value {
          color: #192940;
          font-weight: 700;
        }
      }
      @media (max-width: 768px) {
        .stat-wrapper {
          font-size: 12px;
        }
        .name {
          font-size: 14px;
        }
        .description {
          font-size: 12px;
        }
      }
    }
  }
`;
