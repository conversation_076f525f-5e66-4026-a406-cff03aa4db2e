'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import { useRouter } from 'next/navigation';

const TabItems: TabItem[] = [
  {
    href: '/gov-trades',
    title: 'Overview',
  },
  {
    href: '/gov-trades/securities',
    title: 'Securities',
  },
  {
    href: '/gov-trades/members',
    title: 'Members',
  },
  {
    disabled: true,
    title: 'States',
  },
  {
    disabled: true,
    title: 'Chambers',
  },
  {
    disabled: true,
    title: 'Committees',
  },
  {
    disabled: true,
    title: 'Parties',
  },
];

interface GovernmentTradesHeaderProps {
  activeTabTitle?: string;
}

interface TabItem {
  disabled?: boolean;
  href?: string;
  title: string;
}

export const GovernmentTradesHeader = (props: GovernmentTradesHeaderProps) => {
  const { activeTabTitle } = props;
  const [activeTab, setActiveTab] = React.useState(TabItems.find(item => item.title === activeTabTitle) || TabItems[0]);
  const router = useRouter();

  React.useEffect(() => {
    const path = window.location.pathname;
    const tab =
      TabItems.find(item => item.href === path) || TabItems.find(item => item.title === activeTabTitle) || TabItems[0];
    setActiveTab(tab);
  }, [activeTabTitle]);

  const handleTabClick = (tab: TabItem) => {
    if (tab.disabled) return;
    setActiveTab(tab);
    if (tab?.href) {
      router.push(tab.href);
    }
  };
  return (
    <HeaderWrapper className="flex-col md:flex-row" data-testid="gov-trades-header">
      <div className="whitespace-nowrap font-bold text-2xl mr-7">Government Trades</div>
      <div className="tabs-container">
        <div className="tabs">
          {TabItems.map((item, index) => (
            <div
              className={`tab ${item.disabled ? 'disabled' : ''} ${activeTab?.title === item.title ? 'active' : ''}`}
              key={index}
              onClick={() => handleTabClick(item)}
            >
              {item.title}
            </div>
          ))}
        </div>
      </div>
    </HeaderWrapper>
  );
};

const HeaderWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px;
  gap: 8px;
  max-width: 100vw;
  background: #ffffff;

  .tabs-container {
    overflow: auto;
  }
  .tabs {
    display: flex;
    align-items: center;
    gap: 16px;
    width: fit-content;
    margin-left: auto;

    .tab {
      font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
      padding: 8px 16px;
      cursor: pointer;
      text-transform: uppercase;
      font-weight: 700;

      &:hover {
        background: #f5faff;
        border-radius: 40px;
      }

      &.disabled {
        color: #ceddf2;
        cursor: not-allowed;
      }

      &.active {
        color: #3f83f8;
        border-radius: 40px;
        border: 1px solid #ceddf2;
      }
    }
  }

  @media (max-width: 800px) {
    padding-top: 24px;
    align-items: flex-start;
    .tabs-container {
      max-width: 92vw;
    }
    .tabs {
      align-items: flex-start;
      max-width: 100%;
    }
  }
`;
