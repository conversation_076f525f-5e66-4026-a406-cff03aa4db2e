'use client';
import React, { useMemo } from 'react';
import styled from '@benzinga/themetron';
import { Icon } from '@benzinga/core-ui';
import { faSearch } from '@fortawesome/pro-regular-svg-icons';
import { GovernmentMember, SecuritiesSymbol } from '@benzinga/gov-trades';
import { TrackingManager } from '@benzinga/tracking-manager';
import { usePermission } from '@benzinga/user-context';
import { AiOutlineUnlock } from 'react-icons/ai';
import { SessionContext } from '@benzinga/session-context';

export const congressParties = [
  { name: 'Democrat', party: 'D' },
  { name: 'Republican', party: 'R' },
  { name: 'Independent', party: 'I' },
];

const MemberSearchResult = ({ member }) => {
  const session = React.useContext(SessionContext);
  const { chamber, display_name, headshot, party, state } = member.filer_info;
  const parsedName = display_name.replaceAll(' ', '-').toLowerCase();
  const partyLabel = congressParties.find(p => p.party === party)?.name || party;

  const trackSearchClick = () => {
    session.getManager(TrackingManager).trackSearchEvent('result_click', 'congress_member', {
      query: display_name,
    });
  };

  return (
    <a
      className="grid grid-cols-4 md:grid-cols-6 px-4 py-2 gap-4 hover:bg-bzblue-100 align-middle text-bzblue-900"
      href={`/gov-trades/members/${parsedName}`}
      key={member.filer_info.id}
      onClick={trackSearchClick}
    >
      <div className="col-span-3 flex flex-row gap-4 items-center">
        <div className="whitespace-nowrap">{display_name}</div>
      </div>
      <div className="hidden md:flex items-center">{chamber}</div>
      <div className="hidden md:flex items-center">{partyLabel}</div>
      <div className="flex items-center">{state}</div>
    </a>
  );
};

const SecuritySearchResult = ({ security }) => {
  const { company_name, symbol, exchange, positions_active } = security;
  const session = React.useContext(SessionContext);

  const trackSearchClick = () => {
    session.getManager(TrackingManager).trackSearchEvent('result_click', 'security', {
      query: security.company_name,
    });
  };

  return (
    <a
      className="grid grid-cols-4 md:grid-cols-6 px-4 py-2 gap-4 hover:bg-bzblue-100 items-center text-bzblue-900"
      href={`/gov-trades/securities/${symbol.replace('/USD', '-USD')}`}
      key={symbol}
      onClick={trackSearchClick}
    >
      <div className="col-span-2 md:col-span-3">{company_name}</div>
      <div className="text-xs font-bold text-bzblue-800">
        {exchange}:({symbol})
      </div>
      <div className="text-lg font-bold text-bzblue-800">{positions_active}</div>
    </a>
  );
};

export const GovSearchBarV2 = ({ members, securities }) => {
  const [search, setSearch] = React.useState<string>('');
  const [membersResults, setMembersResults] = React.useState<GovernmentMember[]>([]);
  const [securitiesResults, setSecuritiesResults] = React.useState<SecuritiesSymbol[]>([]);
  const [locked, setLocked] = React.useState<boolean>(false);
  const placeholder = 'Search by Ticker, Politician, or Committee Chamber...';
  const unlockText = 'Unlock all features with Benzinga Edge';
  const hasPermission = usePermission('com/read', 'unlimited-calendars');

  const membersFilter = useMemo(() => {
    if (!members || !search) return [];
    const filteredMembers = members?.filter(member => {
      const { chamber, display_name, state } = member.filer_info;
      const searchVal = search.toLowerCase();
      // TODO: search by full state name, not just abbreviation
      return (
        chamber.toLowerCase().includes(searchVal) ||
        display_name.toLowerCase().includes(searchVal) ||
        state.toLowerCase().includes(searchVal)
      );
    });
    return filteredMembers;
  }, [members, search]);

  const securitiesFilter = useMemo(() => {
    if (!securities || !search) return [];
    const filtered = securities.filter(
      ticker =>
        ticker.company_name.toLowerCase().startsWith(search.toLowerCase()) ||
        ticker.ticker.startsWith(search.toUpperCase()),
    );
    return filtered;
  }, [securities, search]);

  React.useEffect(() => {
    if (!search) {
      setMembersResults([]);
      setSecuritiesResults([]);
    } else {
      const membersFiltered = membersFilter;
      const securitiesFiltered = securitiesFilter;
      setMembersResults(membersFiltered);
      setSecuritiesResults(securitiesFiltered);
    }
  }, [members, membersFilter, search, securitiesFilter]);

  React.useEffect(() => {
    if (hasPermission) {
      setLocked(false);
    } else {
      setLocked(true);
    }
  }, [hasPermission]);

  return (
    <SearchBarWrapper className="relative z-10 group">
      <div className="w-full searchbar">
        <Icon icon={faSearch} />
        <input
          className="w-full inner-search"
          disabled={locked}
          onChange={e => setSearch(e.target.value)}
          placeholder={placeholder}
          type="text"
          value={search}
        />
      </div>
      <div className="absolute max-h-96 overflow-auto w-full bg-white rounded-md shadow-md z-10">
        {membersResults.length > 0 && (
          <div className="mb-4">
            <div className="grid grid-cols-4 md:grid-cols-6 gap-4 px-2 pt-2">
              <div className="col-span-3 text-sm font-bold text-bzblue-900/80">Congress Members</div>
              <div className="hidden md:flex text-sm font-bold text-bzblue-900/80">Chamber</div>
              <div className="hidden md:flex text-sm font-bold text-bzblue-900/80">Party</div>
              <div className="text-sm font-bold text-bzblue-900/80">State</div>
            </div>
            {membersResults.map(result => {
              return <MemberSearchResult key={result.filer_info.display_name} member={result} />;
            })}
          </div>
        )}
        {securitiesResults.length > 0 && (
          <div className="">
            <div className="grid grid-cols-4 md:grid-cols-6 gap-4 px-2 pt-2">
              <div className="col-span-2 md:col-span-3 text-sm font-bold text-bzblue-900/80">Securities</div>
              <div className="text-sm font-bold text-bzblue-900/80">Exchange</div>
              <div className="text-sm font-bold text-bzblue-900/80 whitespace-nowrap">Active Positions</div>
            </div>
            {securitiesResults.map(result => {
              return <SecuritySearchResult key={result.ticker} security={result} />;
            })}
          </div>
        )}
      </div>
      {membersResults.length === 0 && securitiesResults.length === 0 && search.length > 1 && (
        <div className="absolute max-h-96 overflow-scroll w-full bg-white rounded-md shadow-md z-10">
          <div className="px-4 py-2 text-bzblue-900">No results found</div>
        </div>
      )}
      {locked && (
        <a
          className="z-20 absolute hidden group-hover:flex top-0 w-full h-full text-center items-center px-4 font-bold hover:text-bzblue-700 cursor-pointer backdrop-blur-sm"
          href="https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=government-trades"
          target="_blank"
        >
          <AiOutlineUnlock />
          {unlockText}
        </a>
      )}
    </SearchBarWrapper>
  );
};

const SearchBarWrapper = styled.div`
  .searchbar {
    padding: 8px 12px;
    gap: 16px;
    display: flex;
    border-radius: 8px;
    background: #f2f8ff;
    align-items: center;

    .inner-search {
      background: transparent;
    }
  }
`;
