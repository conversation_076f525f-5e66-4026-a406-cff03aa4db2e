'use client';
import { useState, useEffect } from 'react';
import styled from '@benzinga/themetron';
import Link from 'next/link';
import { IoPersonSharp } from 'react-icons/io5';

interface TraderRowProps {
  chamber?: string;
  display_name: string;
  headshot: string;
  member_id: string;
  party: string;
  state: string;
  ttm_total_volume?: number;
  ttm_overall_return?: number;
  total_trades?: number;
  ttm_return?: number;
  type?: string;
}

export const TraderRow: React.FC<TraderRowProps> = ({
  chamber,
  display_name,
  headshot,
  party,
  state,
  total_trades,
  ttm_overall_return,
  ttm_return = 0,
  type = '',
}) => {
  const parsedName = display_name.replaceAll(' ', '-').toLowerCase();
  const [showFallback, setShowFallback] = useState(false);

  useEffect(() => {
    if (!headshot || headshot.trim() === '') {
      setShowFallback(true);
      return;
    }

    const img = new Image();
    img.onload = () => setShowFallback(false);
    img.onerror = () => setShowFallback(true);
    img.src = headshot;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [headshot]);

  return (
    <TraderRowWrapper>
      <div
        className={`trader-row placeholder:${type} ${typeof ttm_overall_return === 'number' ? 'top-performer' : ''}`}
      >
        <div className="flex gap-2">
          {showFallback ? (
            <IoPersonSharp className="trader-headshot" />
          ) : (
            <img
              alt={`${display_name} headshot`}
              className="trader-headshot"
              src={headshot}
              onError={() => setShowFallback(true)}
            />
          )}
          <div className="flex flex-col">
            <Link className="trader-name hover:text-bzblue-600" href={`/gov-trades/members/${parsedName}`}>
              {display_name}
            </Link>
            <div className="trader-caption">
              {chamber} ({party}-{state})
            </div>
          </div>
        </div>
        {/* Top performer in Securities Drilldown */}
        {typeof ttm_overall_return === 'number' ? (
          <div className="flex gap-2 items-center">
            <div className={`volume ${ttm_overall_return > 0 ? 'buy' : 'sell'}`}>
              {(ttm_overall_return * 100).toFixed(2)}%
            </div>
          </div>
        ) : (
          // Top Traders in Securities/Members Overview
          <div className="flex flex-col justify-end items-end">
            <div className={`volume ${type}`}>
              {type === 'gainer' ? `+` : ``}
              {(ttm_return * 100).toFixed(2)}%
            </div>
            <div className={`trades ${type}`}>Trades: {total_trades}</div>
          </div>
        )}
      </div>
    </TraderRowWrapper>
  );
};

const TraderRowWrapper = styled.div`
  .trader-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px 8px 8px;
    border-radius: 4px;
    background: #f2f8ff;
    gap: 8px;

    &.gainer {
      background: linear-gradient(270deg, rgba(15, 153, 61, 0.12) 0%, rgba(15, 153, 61, 0) 100%);
    }

    &.loser {
      background: linear-gradient(270deg, rgba(255, 64, 80, 0.12) 0%, rgba(255, 64, 80, 0) 100%);
    }

    &.top-performer {
      background: white;
    }

    .trader-headshot {
      height: 36px;
      width: 32px;
      border-radius: 4px;
      object-fit: cover;
    }

    .trader-name {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      text-transform: uppercase;
      color: #192940;
      &:hover {
        color: rgb(46 121 246);
      }
    }

    .trader-caption {
      color: #5b7292;
      font-size: 12px;
      line-height: 14px;
      text-transform: uppercase;
    }

    .volume {
      font-size: 14px;
      font-weight: 700;
      &.gainer {
        color: #0f993d;
      }

      &.loser {
        color: #ff4050;
      }
    }

    .trades {
      font-size: 12px;
      color: #5b7292;
      line-height: 14px;
      white-space: nowrap;
    }

    .buy {
      color: #0f993d;
    }

    .sell {
      color: #ff4050;
    }

    .direction {
      padding: 4px 8px;
      border-radius: 4px;
      text-transform: uppercase;
      font-size: 14px;
      font-weight: 700;

      &.buy {
        background: #dff3f0;
      }

      &.sell {
        background: #f3e6ee;
      }
    }
  }
`;
