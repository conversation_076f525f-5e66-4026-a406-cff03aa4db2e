'use client';
import classnames from 'classnames';
import styled from '@benzinga/themetron';
import React from 'react';
import { AFTER_MARKET, PRE_MARKET, REGULAR } from '../constants';
import { CandleChart } from '../entities/chartEntity';
import { formatLarge, formatRound, formatRoundFixed } from '../util';
import { LinkDesign } from './utils/styletron';
import { SessionContext } from '@benzinga/session-context';
import { ChartDataResponse, ChartManager } from '@benzinga/chart-manager';
import { Sparkline } from '@benzinga/core-ui';

interface Props {
  chart?: CandleChart | null;
  currentSymbol: string;
  interval: string;
  quote: any;
  type: string;
}

const Peer: React.FC<Props> = ({ currentSymbol, interval, quote, type }) => {
  const symbol = quote?.symbol;
  const session = React.useContext(SessionContext);
  const chartManager = React.useMemo(() => session.getManager(ChartManager), [session]);
  const [chartData, setChartData] = React.useState<ChartDataResponse | null>(null);

  React.useEffect(() => {
    if (!symbol) return;

    const intervalMap = {
      day: {
        interval: '1d',
        range: '20min',
      },
      month: {
        interval: '1m',
        range: '1d',
      },
      year: {
        interval: '1y',
        range: '10d',
      },
    };

    async function getChartData() {
      const res = await chartManager.getChart({
        from: intervalMap[interval].interval,
        interval: intervalMap[interval].range,
        symbol,
      });
      setChartData(res?.ok || null);
    }

    getChartData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interval, symbol]);

  // quote has loaded
  if (quote && quote.lastTradePrice) {
    let color = '';
    let price = quote.lastTradePrice;
    let { change, changePercent } = quote;

    if (type === PRE_MARKET) {
      color = 'text-grey-darker';
    } else if (quote.change < 0) {
      color = 'text-red';
    } else {
      color = 'text-green-dark';
    }

    if ((type === PRE_MARKET || type === AFTER_MARKET) && quote.ethPrice !== undefined) {
      price = quote.ethPrice;
      change = price - quote.lastTradePrice;
      changePercent = (change / quote.lastTradePrice) * 100;
      color = change > 0 ? 'text-green-dark' : 'text-red';
    }

    let data: number[] = [];

    // chart has loaded
    if (Array.isArray(chartData?.candles)) {
      data = chartData.candles.map(candle => candle.open);
      const min = Math.min(...data);
      data = data.map(point => point - min);
    }

    if (type === REGULAR) {
      while (data.length < 19) {
        data.push(0);
      }
    }

    return (
      <tr className={symbol === currentSymbol ? 'border-b-2' : 'border-b'}>
        <TableData className="py-1 pt-2 sm:py-0">
          {symbol === currentSymbol ? (
            <span className="font-bold">{symbol}</span>
          ) : (
            <LinkDesign className="no-underline font-bold" href={`/quote/${symbol}`}>
              {symbol}
            </LinkDesign>
          )}
        </TableData>
        <TableData className="py-1 pt-2 sm:py-0">
          <span>{quote?.name}</span>
        </TableData>
        <TableData className="hidden md:table-cell text-right">{formatRoundFixed(price, 2)}</TableData>
        <TableData className={classnames('hidden md:table-cell text-right', color)}>
          {formatRoundFixed(change, 2)}
        </TableData>
        <TableData className={classnames('py-1 pt-2 sm:py-0 text-right', color)}>
          {formatRound(changePercent)}%
        </TableData>
        <TableData className="quote-table-data hidden md:table-cell w-1 pl-8">
          <div className="custom-cell-width">
            <Sparkline className="block" data={data} height={23} width={100} />
          </div>
        </TableData>
        <TableData className="py-1 pt-2 sm:py-0 text-right">{formatLarge(quote.marketCap)}</TableData>
      </tr>
    );
  }

  return (
    <tr className="border-b">
      <TableData>
        <LinkDesign className="no-underline font-bold" href={`/quote/${symbol}`}>
          {symbol}
        </LinkDesign>
      </TableData>
      <TableData />
      <TableData />
      <TableData />
      <TableData />
      <TableData />
    </tr>
  );
};

const TableData = styled.td`
  &.quote-table-data {
    padding: 5px;
    .custom-cell-width {
      width: 133px;
    }
  }
`;

export default Peer;

// const fetchChart =
//   (
//     symbol: string,
//     params: {
//       from: string;
//       interval: string;
//     },
//   ) =>
//   async () => {
//     const query = {
//       symbol,
//       ...params,
//     };

//     const urlQuery = `${DATAAPI_ROOT}v2/chart?${qs.stringify(query)}&apikey=${DATAAPI_TOKEN}`;
//     const response = await safeJsonFetch<Chart>(urlQuery, { timeout: 60000 });
//     if (response.err) {
//       console.log('CHART_ERROR:', response.err);
//     } else {
//       return Promise.resolve(response.ok);
//     }
//   };
