import React from 'react';
import { DateTime } from 'luxon';
import { useTranslation } from 'react-i18next';
import styled from '@benzinga/themetron';
import { getNodeUrl, StoryObject } from '@benzinga/advanced-news-manager';
import { isProOnlyPost, isSponsoredArticle } from '@benzinga/article-manager';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { truncate } from '@benzinga/utils';
import i18n from '@benzinga/translate';
import { NoFirstRender } from '@benzinga/hooks';

const TIMEZONE = 'America/New_York';

const DateOrTimeAgo = ({ dateTimeString = '' }: { dateTimeString: string }): React.ReactElement | null => {
  const dateTimeLuxon = DateTime.fromJSDate(new Date(dateTimeString), { locale: i18n.language }).setZone(TIMEZONE);
  if (!dateTimeLuxon.isValid) {
    return null;
  }
  const dateTimeFormatted = dateTimeLuxon
    .toFormat('ff', { locale: i18n.language })
    ?.replace(' AM', 'AM')
    ?.replace(' PM', 'PM');
  const dateTime =
    dateTimeLuxon > DateTime.local().minus({ days: 7 })
      ? dateTimeLuxon.toRelative({ locale: i18n.language })
      : dateTimeFormatted;
  return (
    <>
      <span> - </span>
      <span className="text-gray-500 content-headline-datetime">{dateTime}</span>
    </>
  );
};

interface Props {
  content: StoryObject;
  isProOnly?: boolean;
  impression?: boolean;
}

const ContentHeadline = React.memo((props: Props) => {
  const { t } = useTranslation('quote', { i18n });
  const { content, impression, isProOnly } = props;

  if (!content) {
    return null;
  }

  const isSponsored = isSponsoredArticle(content);

  const href = getNodeUrl(content);

  return (
    <Container
      className="py-2 content-headline"
      data-impression={impression && content.id}
      href={href}
      key={content.id}
      rel="noopener noreferrer"
      target="_blank"
    >
      <div className="content-title">
        <span
          dangerouslySetInnerHTML={{
            __html: sanitizeHTML(isProOnly ? truncate(content.title ?? '', 45) : content.title ?? ''),
          }}
        />
      </div>
      <div className="text-gray-400 text-sm author-date-text">
        {content.author}
        {isSponsored ? (
          <>
            <span> - </span>
            <span className="news-item-sponsored-tag">{t('Sponsored.sponsored')}</span>
          </>
        ) : (
          <NoFirstRender>
            <DateOrTimeAgo dateTimeString={content.created} />
          </NoFirstRender>
        )}
      </div>
    </Container>
  );
});

ContentHeadline.displayName = 'ContentHeadline';

export default ContentHeadline;

const Container = styled.a`
  display: block;
  .news-item-sponsored-tag {
    font-size: 13px;
    color: ${({ theme }) => theme.colorPalette.blue500};
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
  }
`;
