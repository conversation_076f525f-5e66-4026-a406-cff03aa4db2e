'use client';
import React from 'react';
import { tabsData } from '../mock';
import { Tabs } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import Investing from './Investing/Investing';
import {
  FusionSection,
  FusionSectionContent,
  fusionSectionContentPadding,
  FusionSectionText,
  FusionSectionTitle,
} from '@benzinga/ui';

const tabsDataWithComponent = [...tabsData];
tabsDataWithComponent[0] = { ...tabsData[0], component: <Investing /> };

const BenzingaMoney = () => {
  const [tabKey, setTabKey] = React.useState(tabsDataWithComponent[0].key);
  const handleChangeTab = (newTab: string) => {
    setTabKey(newTab);
  };

  return (
    <StyledSection sectionPadding={false}>
      <FusionSectionContent paddingHorizontal={false} paddingVertical={false} sectionPadding>
        <Header>
          <FusionSectionTitle className="title">
            Secure Your Financial Future <br /> WITH BENZINGA MONEY
          </FusionSectionTitle>
          <FusionSectionText className="subtitle">Compare Rates And Get Expert Insights</FusionSectionText>
        </Header>
      </FusionSectionContent>
      <StyledTabsSection paddingHorizontal={false} paddingVertical={false}>
        <Tabs activeTab={tabKey} onChange={handleChangeTab} tabs={tabsDataWithComponent} variant="full" />
      </StyledTabsSection>
    </StyledSection>
  );
};

const StyledSection = styled(FusionSection)`
  background: linear-gradient(180deg, ${props => props.theme.colorPalette.gray100} 0%, rgba(225, 235, 250, 0) 100%);
  padding-top: 64px;
  padding-bottom: 64px;
`;

const Header = styled.div`
  & .title {
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 16px;
  }
  & .subtitle {
    text-align: center;
    margin-bottom: 32px;
  }
`;

const StyledTabsSection = styled(FusionSectionContent)`
  & .tab-list,
  & .tabs-content {
    ${fusionSectionContentPadding}
  }
`;

export default BenzingaMoney;
