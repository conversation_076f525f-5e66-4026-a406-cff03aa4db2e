import React from 'react';
import StickyBox from 'react-sticky-box';

import styled from '@benzinga/themetron';
import type { CallToActionFormProps } from '@benzinga/forms-ui';
import { TradeIdeasWidget } from '@benzinga/trade-ideas';
import { WordpressPost, WordpressSidebar } from '@benzinga/content-manager';
import { TradeIdea } from '@benzinga/trade-ideas-manager';
import { Podcast } from '@benzinga/content-manager';
import { MoneySidebar } from '@benzinga/money';
import { PodcastsListBlock } from '@benzinga/blocks';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

// const GoogleAdUnit = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.GoogleAdUnit };
//   }),
// );

const WatchlistSidebarWidget = React.lazy(() =>
  import('@benzinga/widgets').then(module => {
    return { default: module.WatchlistSidebarWidget };
  }),
);

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

export interface DefaultSidebarProps {
  podcasts?: Podcast[];
  post?: WordpressPost | null;
  sidebar?: WordpressSidebar | null;
  tradeIdeas?: TradeIdea[];
  showWatchlistWidget?: boolean;
  newsletter?: CallToActionFormProps;
  enableRaptiveAd?: boolean;
}

const DefaultSidebar: React.FC<React.PropsWithChildren<DefaultSidebarProps>> = ({
  children,
  enableRaptiveAd = true,
  newsletter,
  podcasts,
  post,
  showWatchlistWidget = true,
  sidebar,
  tradeIdeas,
}) => {
  return (
    <StickyBox className="default-sidebar-container" offsetBottom={20} offsetTop={20}>
      <Container>
        {showWatchlistWidget && (
          <div className="mb-4">
            <WatchlistSidebarWidget />
          </div>
        )}

        {newsletter && (
          <div className="mb-4">
            <CallToActionForm {...newsletter} />
          </div>
        )}

        {children}

        {sidebar && <MoneySidebar post={post} sidebar={sidebar} />}

        {enableRaptiveAd && !sidebar && !children && (
          // <div style={{ height: '600px', margin: 'auto', marginTop: '16px', textAlign: 'center', width: '300px' }}>
          //   <GoogleAdUnit adUnit="DotCom_600x300" dfpNetworkId="4107070" sizes={[[300, 600]]} />
          // </div>

          <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="static-sidebar" />
        )}

        {tradeIdeas?.length ? <TradeIdeasWidget items={tradeIdeas} title="ZINGERNATION" /> : null}

        {podcasts ? (
          <div className="mt-4">
            <PodcastsListBlock podcasts={podcasts} />
          </div>
        ) : null}
      </Container>
    </StickyBox>
  );
};

const Container = styled.div`
  .movers-box-wrapper {
    background-color: white !important;
  }
`;

export default DefaultSidebar;
