/* eslint-disable @typescript-eslint/member-ordering */
import { Theme } from '@benzinga/themetron';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import * as LightweightCharts from 'lightweight-charts';
import { HistogramData } from 'lightweight-charts';
import { DateTime } from 'luxon';
import Cookies from 'js-cookie';
import { LocalStorageKey } from '@benzinga/pro-ui';
import { CookiesKey } from '../../entities/storageEntity';
import { map, match, equals } from 'ramda';
import React, { Component, createRef } from 'react';
import ChartSocket, { Instrument } from './ChartSocket';
import * as protos from './lastdb';

export function getAuthKey(): string {
  return Cookies.get(CookiesKey.benzingaToken) || window.localStorage.getItem(LocalStorageKey.authKey) || '';
}
export declare type ChartType = 'line' | 'bar';

export declare type OnStateChange = (state: LoadState) => void;

export const SECRET = 'eb171f26ce963d6fe821468440083a93';

export interface LoadState {
  message: string | null;
}

export interface ChartProps {
  from: string;
  height: number;
  onLoadStateChange?: OnStateChange;
  resolution: string;
  showVolume?: boolean;
  symbol: string;
  theme?: Theme;
  type: ChartType;
  width: number;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface State {}

/**
 * Uses lightweight charts to implement a fairly simple real-time capable chart.
 * - No implementation of the Lightweight api is exposed.
 * - Few as possible dependencies on pro code, including themes. Treat this close to a 3rd party lib.
 *
 * https://github.com/tradingview/lightweight-charts/blob/master/docs/README.md
 */
export default class ProLightweightChart extends Component<ChartProps, State> {
  private chartRef = createRef<HTMLDivElement>();
  chart!: LightweightCharts.IChartApi;
  candleSeries: any;
  volumeSeries: any;
  chartSocket!: ChartSocket; // This is set in componentDidMount and thereafter will never be null.

  private series: any[] = [];

  private themes: Map<string, any> = new Map();
  private currentTheme: any;

  private currentInstrument: Instrument | null = null;
  private currentSubscriptionGuid: string | null = null;

  constructor(props: ChartProps) {
    super(props);

    // FIXME load theme programatically?
    //const bzGreyDarkest = '#222222';
    const bzGreyDark = '#373f49';
    const bzGreyDarker = '#28313C';
    const bzWhite = '#fff';
    const bzOrange = '#E3A542';
    // const bzOrangeDarker = '#a56300';
    // const bzOrangeLight = '#ff9c29';
    const bzGreyLight = '#C5CEDA';
    const upColor = '#50C773';
    const downColor = '#C75050';
    const darkDownColor = '#E5594E';
    const darkUpColor = '#30BFA3';

    this.themes.set('dark', {
      background: bzGreyDarker,
      borderColor: bzGreyLight,
      downColor: darkDownColor,
      gridColor: bzGreyDark,
      textColor: bzGreyLight,
      upColor: darkUpColor,
    });
    this.themes.set('light', {
      background: bzWhite,
      borderColor: bzGreyDark,
      downColor: downColor,
      gridColor: bzGreyLight,
      textColor: bzGreyDark,
      upColor: upColor,
    });
    this.themes.set('antique', {
      background: '#000',
      borderColor: bzOrange,
      downColor: downColor,
      gridColor: bzGreyDark,
      textColor: bzOrange,
      upColor: upColor,
    });
    this.themes.set('highContrast', {
      background: '#000',
      borderColor: bzGreyLight,
      downColor: downColor,
      gridColor: bzGreyDark,
      textColor: '#fff',
      upColor: upColor,
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const theme = this.themes.has(props.theme) ? props.theme : 'dark';
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    this.setTheme(theme);
  }

  setTheme(theme: string) {
    if (!this.themes.has(theme)) {
      throw `unknown theme ${theme}`;
    }
    this.currentTheme = this.themes.get(theme);
  }

  private buildChartConfig() {
    const c: LightweightCharts.DeepPartial<LightweightCharts.ChartOptions> = {
      crosshair: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        mode: LightweightCharts.CrosshairMode.Normal,
      },
      grid: {
        horzLines: {
          color: this.currentTheme.gridColor,
        },
        vertLines: {
          color: this.currentTheme.gridColor,
        },
      },
      height: this.props.height,
      layout: {
        background: {
          color: this.currentTheme.background,
        },
        textColor: this.currentTheme.textColor,
      },
      localization: {
        timeFormatter: function (businessDayOrTimestamp: any) {
          const time = businessDayOrTimestamp * 1000;

          const date = new Date(time);
          const result = DateTime.fromJSDate(date).setZone('utc').toFormat('dd, MMM yy HH:mm');

          return result;
        },
      },
      rightPriceScale: {
        borderColor: this.currentTheme.borderColor,
        scaleMargins: {
          bottom: 0.04,
          top: 0.04,
        },
      },
      timeScale: {
        borderColor: this.currentTheme.borderColor,
        rightBarStaysOnScroll: true,
        secondsVisible: false,
        timeVisible: true,
      },
      width: this.props.width,
    };
    return c;
  }

  componentDidMount() {
    this.chart = LightweightCharts.createChart(this.chartRef.current!, this.buildChartConfig());
    this.chartSocket = new ChartSocket({
      auth: {
        secret: SECRET,
      },
      ready: () => {
        this.load(this.props.symbol);
      },
    });
  }

  componentDidUpdate(prevProps: ChartProps) {
    if (
      prevProps.from != this.props.from ||
      prevProps.symbol != this.props.symbol ||
      prevProps.resolution != this.props.resolution ||
      prevProps.type != this.props.type
    ) {
      try {
        this.load(this.props.symbol);
      } catch (error: any) {
        console.warn(`Faled to load chart ${error}`);
        this.updateLoadState(`Failed to load chart; please try again.`);
      }
    }
    if (prevProps.width != this.props.width) {
      this.chart.resize(this.props.height, this.props.width);
    }
  }

  private updateLoadState(message: string | null) {
    if (this.props.onLoadStateChange) {
      this.props.onLoadStateChange({ message });
    }
  }

  /*
   * Loads a new symbol and chart.
   * - Guarantee that only one sub per chart instance is active.
   */
  private load(symbol: string) {
    this.updateLoadState(`Resolving ${symbol}`);
    // Clear the chart
    this.series.forEach(series => {
      this.chart.removeSeries(series);
    });
    this.series = [];

    this.chartSocket.resolveSymbol(
      symbol,
      instrument => {
        try {
          this.load2(instrument);
        } catch (error: any) {
          this.updateLoadState(`Faled to load chart for ${symbol}: ${error}`);
        }
      },
      error => {
        this.updateLoadState(error.message as string);
      },
    );
  }

  private load2(instrument: Instrument) {
    this.updateLoadState(`Loading...`);

    if (this.currentSubscriptionGuid) {
      this.chartSocket.unsubscribeBars(this.currentSubscriptionGuid);
    }
    if (this.currentInstrument) {
      // Any previous instrument cleanup here
    }
    this.currentInstrument = instrument;

    const symbol = `${instrument.exchange}:${instrument.symbol}`;

    const chartResolution = this.toResolution(this.props.resolution);
    const anySession = false;
    this.chartSocket.getBars(
      symbol,
      chartResolution,
      0,
      this.props.from,
      anySession,
      barsResult => {
        this.updateLoadState(null);

        this.loadCandles(barsResult);
        this.chart.timeScale().fitContent();

        const subGuid = `${symbol}_${chartResolution}`;
        this.currentSubscriptionGuid = subGuid;
        this.chartSocket.subscribeBars(symbol, chartResolution, this.onBarUpdate, subGuid);
      },
      error => {
        this.updateLoadState(`${error.message}`);
      },
    );
  }

  private toResolution(interval: string) {
    if (interval == '1d') {
      return 'D';
    } else if (!equals(match(/^[0-9]+m$/, interval), [])) {
      return String(interval.substring(0, interval.length - 1));
    } else if (!equals(match(/^[0-9]+h$/, interval), [])) {
      return String(parseInt(interval.substring(0, interval.length - 1)) * 60);
    } else if (!equals(match(/^[0-9]+w$/, interval), [])) {
      return interval;
    } else if (!equals(match(/^[0-9]+mo$/, interval), [])) {
      return 'MO';
    } else {
      throw new Error(`Unable to parse interval ${interval}`);
    }
  }

  onBarUpdate = (guid: string, bar: protos.quotestore.ICandle) => {
    if (guid != this.currentSubscriptionGuid) {
      console.warn(`Bar update recieved for unknown guid ${guid}`);
      return;
    }
    this.candleSeries.update(this.mapBar(bar));
  };

  private mapBar = (candle: protos.quotestore.ICandle) => {
    return {
      close: candle.close,
      high: candle.high,
      low: candle.low,
      open: candle.open,
      time: this.convertTime(candle.time as number),
    };
  };

  convertTime(time: number) {
    return time - 3600 * 4; // FIXME use moment, or another api to convert tz. N
  }

  private mapLine = (candle: protos.quotestore.ICandle) => {
    return {
      time: this.convertTime(candle.time as number),
      value: candle.close,
    };
  };

  addVolumeSeries(candlesResponse: protos.quotestore.IGetBarsResponse) {
    const data = candlesResponse.bars!.map(
      bar =>
        ({
          time: this.convertTime(bar.time as number),
          value: typeof bar.volume === 'number' ? bar.volume : 0,
        }) as HistogramData,
    );

    this.volumeSeries = this.chart.addSeries(LightweightCharts.HistogramSeries, {
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
    });
    this.volumeSeries.setData(data);
  }

  private loadCandles(candlesResponse: protos.quotestore.IGetBarsResponse) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const candles = map(this.mapBar, candlesResponse.bars);

    if (this.props.type == 'line') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const lines = map(this.mapLine, candlesResponse.bars);
      let clr = this.currentTheme.downColor;
      if ((lines[lines.length - 1].value ?? 0) > (lines[0].value ?? 0)) {
        clr = this.currentTheme.upColor;
      }

      const l = this.chart.addSeries(LightweightCharts.LineSeries, {
        color: clr,
        lineWidth: 1,
      });
      this.series.push(l);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      l.setData(lines);
    } else {
      this.candleSeries = this.chart.addSeries(LightweightCharts.CandlestickSeries, {
        borderDownColor: this.currentTheme.downColor,
        borderUpColor: this.currentTheme.upColor,
        downColor: this.currentTheme.downColor,
        upColor: this.currentTheme.upColor,
        wickDownColor: this.currentTheme.downColor,
        wickUpColor: this.currentTheme.upColor,
      });
      this.series.push(this.candleSeries);
      this.candleSeries.setData(candles);
    }
  }

  render() {
    return <div ref={this.chartRef} />;
  }
}
