import React from 'react';
import styled, { Theme } from '@benzinga/themetron';
import { equals } from 'ramda';
import deepEquals from 'fast-deep-equal';

import ProLightweightChart, { ChartProps, ChartType, LoadState } from './ProLightweightChart';
import { blueThemeColor } from '../../constants';
import { Candlestick, Line } from '@benzinga/icons';
import { leadingThrottle } from '@benzinga/utils';

const ChartContainer = styled.div`
  position: relative;
`;
const TradingViewLink = styled.a`
  bottom: 25px;
  height: 30px;
  left: 3px;
  position: absolute;
  width: auto;
  z-index: 99;
`;
const TradingViewLogo = styled.img`
  width: 32px;
`;
const TradingViewText = styled.span`
  color: rgb(44, 162, 209);
  cursor: pointer;
  display: inline-block;
  font-size: 15px;
  letter-spacing: 0.2px;
  line-height: 14px;
  margin-left: 6px;
  user-select: none;
  vertical-align: middle;
`;
const ButtonGroup = styled.div`
  position: relative;
  display: flex;
  height: 100%;
  align-items: center;
`;
const StyledButton = styled.button`
  height: 24px;
  padding: 0px 7px;
  color: ${({ theme }) => theme.colorPalette.blue500};
  border-radius: 4px;

  &.active {
    background: ${({ theme }) => theme.colorPalette.blue500};
    color: white;
  }

  &.chart-type-button {
    background: ${({ theme }) => theme.colorPalette.blue500};
    svg {
      fill: white;
      font-size: 1.3rem;
    }
  }
`;

interface Props {
  symbol: string;
  theme: Theme;
}

interface Dim {
  height: number;
  width: number;
}

interface State {
  chart: ChartProps;
  dim: Dim | null;
  loadText: string | null;
}

interface TimeFrame {
  from: string;
  label: string;
  resolution: string;
}

const timeFrames: TimeFrame[] = [
  { from: '1d', label: '1d', resolution: '2m' },
  { from: '2d', label: '2d', resolution: '5m' },
  { from: '5d', label: '5d', resolution: '10m' },
  { from: '1m', label: '1m', resolution: '30m' },
  { from: '90d', label: '3m', resolution: '4h' },
  { from: '180d', label: '6m', resolution: '1d' },
  { from: 'YTD', label: 'YTD', resolution: '1d' },
  { from: '1y', label: '1y', resolution: '1d' },
  { from: '2y', label: '2y', resolution: '1w' },
  { from: '5y', label: '5y', resolution: '2w' },
  { from: '10y', label: '10y', resolution: '1mo' },
  { from: '50y', label: 'MAX', resolution: '1mo' },
];

enum CSS {
  Active = 'active',
  Inactive = 'inactive',
}

class LightweightChart extends React.Component<Props, State> {
  timeoutId!: NodeJS.Timer;
  private updateSizeThrottled: any;
  private resizeObserver: ResizeObserver;
  private container: React.RefObject<HTMLDivElement | null> = React.createRef();

  constructor(props: any) {
    super(props);

    this.state = {
      chart: {
        from: '2d',
        height: 0,
        resolution: '5m',
        symbol: 'SPY',
        type: 'bar',
        width: 0,
      },
      dim: null,
      loadText: null,
    };

    this.updateSizeThrottled = leadingThrottle(this.updateSize, 500);
  }

  componentWillUnmount() {
    this.resizeObserver.disconnect();
  }

  componentDidMount() {
    this.resizeObserver = new ResizeObserver(entries => {
      if (!Array.isArray(entries) || !entries.length) {
        return;
      }
      this.updateSizeThrottled();
    });

    if (this.container.current) {
      this.resizeObserver.observe(this.container.current);
    }

    this.updateSize();
  }

  render() {
    // FIXME, find a better way, without it the resize it blocked initially
    clearTimeout(this.timeoutId);
    setTimeout(() => {
      this.updateSizeThrottled();
    }, 100);

    const { chart, loadText } = this.state;

    const timeFrameButtons = timeFrames.map(tf => {
      const activeInactive = equals(tf.from, chart.from) ? CSS.Active : CSS.Inactive;
      return (
        <StyledButton className={activeInactive} key={tf.from} onClick={() => this.onTimeFrameClick(tf.from)}>
          {tf.label}
        </StyledButton>
      );
    });

    const dim = this.state.dim;

    return (
      <div className="lightweight-chart">
        <div className="flex sm:flex-row sm:items-center mb-4">
          <ButtonGroup className="w-auto overflow-auto">{timeFrameButtons}</ButtonGroup>
          <ButtonGroup className="sm:ml-auto text-white ml-4 sm:mt-0">
            <StyledButton className="chart-type-button">
              {equals(chart.type, 'bar') ? (
                <Candlestick data-value="line" onClick={this.setChartType} />
              ) : (
                <Line data-value="bar" onClick={this.setChartType} />
              )}
            </StyledButton>
          </ButtonGroup>
        </div>
        <div className="lw-chart" ref={this.container}>
          <div className="left-0 top-0 flex flex-col w-full">
            <div className="status-ct">
              {loadText && <div className="status-msg">{loadText}</div>}
              {dim && (
                <ChartContainer>
                  <ProLightweightChart
                    from={chart.from}
                    height={dim.height}
                    onLoadStateChange={this.onLoadStateChange}
                    resolution={chart.resolution}
                    symbol={this.props.symbol}
                    theme={this.props.theme}
                    type={chart.type}
                    width={dim.width}
                  />
                  <TradingViewLink
                    href="https://www.tradingview.com/?utm_source=http://localhost:9000&utm_medium=library&utm_campaign=library"
                    target="_blank"
                  >
                    <TradingViewLogo src="/next-assets/images/tradingView.png" />
                    <TradingViewText>Chart by TradingView</TradingViewText>
                  </TradingViewLink>
                </ChartContainer>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  private updateSize = () => {
    const el = this.container.current;
    if (el && el.offsetWidth !== 0) {
      const newDim: Dim = {
        height: 240,
        width: el.offsetWidth,
      };
      if (!deepEquals(newDim, this.state.dim)) {
        this.setState({ dim: newDim });
      }
    }
  };

  private onTimeFrameClick = (timeFrameFrom: string) => {
    if (timeFrameFrom === null) {
      throw Error('value cannot be null');
    }

    const chart = this.state.chart;

    const tf = timeFrames.find(tf => tf.from === timeFrameFrom);
    if (!tf) {
      console.error(`Timeframe not found for ${timeFrameFrom}`);
      return;
    }

    chart.from = timeFrameFrom;
    chart.resolution = tf.resolution;

    this.setState({ chart });
  };

  private onLoadStateChange = (loastState: LoadState) => {
    this.setState({
      loadText: loastState.message,
    });
  };

  private setChartType = (ev: React.MouseEvent<SVGSVGElement, MouseEvent>) => {
    const type = ev.currentTarget.getAttribute('data-value') as ChartType;
    this.setState({
      chart: { ...this.state.chart, type },
    });
  };
}

export default LightweightChart;
