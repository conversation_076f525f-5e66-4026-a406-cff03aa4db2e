'use client';

import React, { useState, useEffect, useContext } from 'react';
import { TiInfoOutline } from 'react-icons/ti';
import { IoClose } from 'react-icons/io5';
import toast from 'react-hot-toast';

import { SessionContext } from '@benzinga/session-context';
import { WatchlistManager, Watchlist, WatchlistSymbol } from '@benzinga/watchlist-manager';
import { Spin<PERSON>, Tooltip, Modal } from '@benzinga/core-ui';

interface ToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  disabled?: boolean;
}

const Toggle: React.FC<ToggleProps> = ({ enabled, onChange, disabled = false }) => {
  return (
    <button
      onClick={() => !disabled && onChange(!enabled)}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        enabled ? 'bg-green-500' : 'bg-gray-300'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      disabled={disabled}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );
};

const WatchlistAlerts = () => {
  const [watchlistAlerts, setWatchlistAlerts] = useState<Watchlist[]>([]);
  const session = useContext(SessionContext);
  const [loading, setLoading] = useState<boolean>(true);
  const [deleteModal, setDeleteModal] = useState<{ show: boolean; watchlist: Watchlist | null; deleting: boolean }>({
    show: false,
    watchlist: null,
    deleting: false,
  });

  useEffect(() => {
    const fetchAlerts = async () => {
      const watchlistRes = await session.getManager(WatchlistManager).getWatchlists();
      setWatchlistAlerts(watchlistRes.ok ?? []);
      setLoading(false);
    };

    if (loading) {
      fetchAlerts();
    }
  }, [session, loading]);

  const calculateNewsAlertCount = (watchlist: Watchlist): string => {
    if (!watchlist.symbols || watchlist.symbols.length === 0) return 'All';

    const realtimeCount = watchlist.symbols.filter((symbol: WatchlistSymbol) => symbol.sendRealtime === 1).length;
    const totalSymbols = watchlist.symbols.length;

    if (realtimeCount === 0) return 'None';
    if (realtimeCount === totalSymbols) return 'All';
    return realtimeCount.toString();
  };

  const calculatePressReleaseCount = (watchlist: Watchlist): string => {
    if (!watchlist.symbols || watchlist.symbols.length === 0) return 'All';

    const prCount = watchlist.symbols.filter((symbol: WatchlistSymbol) => symbol.isPR === 1).length;
    const totalSymbols = watchlist.symbols.length;

    if (prCount === 0) return 'None';
    if (prCount === totalSymbols) return 'All';
    return prCount.toString();
  };

  const calculateSECCount = (watchlist: Watchlist): string => {
    if (!watchlist.symbols || watchlist.symbols.length === 0) return 'All';

    const secCount = watchlist.symbols.filter((symbol: WatchlistSymbol) => symbol.isSEC === 1).length;
    const totalSymbols = watchlist.symbols.length;

    if (secCount === 0) return 'None';
    if (secCount === totalSymbols) return 'All';
    return secCount.toString();
  };

  const handleToggleChange = async (watchlistId: string, field: string, newValue: boolean) => {
    setWatchlistAlerts(prev =>
      prev.map(watchlist => (watchlist.watchlistId === watchlistId ? { ...watchlist, [field]: newValue } : watchlist)),
    );

    try {
      const watchlistToUpdate = watchlistAlerts.find(w => w.watchlistId === watchlistId);
      if (!watchlistToUpdate) {
        console.error('Watchlist not found');
        return;
      }

      const updatedWatchlist = {
        ...watchlistToUpdate,
        [field]: newValue,
      };

      const result = await session.getManager(WatchlistManager).updateWatchlistSettings(updatedWatchlist);

      if (result.err) {
        console.error('Failed to update watchlist settings:', result.err);
        setWatchlistAlerts(prev =>
          prev.map(watchlist =>
            watchlist.watchlistId === watchlistId ? { ...watchlist, [field]: !newValue } : watchlist,
          ),
        );
      }
    } catch (error) {
      console.error('Error updating watchlist settings:', error);
      setWatchlistAlerts(prev =>
        prev.map(watchlist =>
          watchlist.watchlistId === watchlistId ? { ...watchlist, [field]: !newValue } : watchlist,
        ),
      );
    }
  };

  const handleDeleteWatchlist = (watchlist: Watchlist) => {
    setDeleteModal({ show: true, watchlist, deleting: false });
  };

  const confirmDeleteWatchlist = async () => {
    if (!deleteModal.watchlist) return;

    setDeleteModal(prev => ({ ...prev, deleting: true }));

    try {
      const result = await session.getManager(WatchlistManager).removeWatchlist(deleteModal.watchlist);

      if (result.err) {
        console.error('Failed to delete watchlist:', result.err);
        toast.error(`Unable to delete "${deleteModal.watchlist?.name}" watchlist. Please try again later.`);
      } else {
        setWatchlistAlerts(prev => prev.filter(w => w.watchlistId !== deleteModal.watchlist?.watchlistId));
        toast.success(`"${deleteModal.watchlist?.name}" watchlist deleted successfully`);
      }
    } catch (error) {
      console.error('Error deleting watchlist:', error);
      toast.error(`Unable to delete "${deleteModal.watchlist?.name}" watchlist. Please try again later.`);
    } finally {
      setDeleteModal({ show: false, watchlist: null, deleting: false });
    }
  };

  const cancelDeleteWatchlist = () => {
    if (!deleteModal.deleting) {
      setDeleteModal({ show: false, watchlist: null, deleting: false });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spinner />
      </div>
    );
  }

  if (watchlistAlerts.length === 0) {
    return <div className="text-center py-8 text-gray-500">No watchlists found</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Combined Watchlist View</h2>
        <p className="text-sm text-gray-600 mt-1">Manage how email notifications are sent to you for each watchlist</p>
      </div>

      <div className="hidden lg:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Watchlist
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="flex items-center gap-1">
                  Personalized Daily Briefing
                  <Tooltip content="Daily email summary of your watchlist activity" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="flex items-center gap-1">
                  News Alert
                  <Tooltip content="Real-time news alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="flex items-center gap-1">
                  Press Release
                  <Tooltip content="Press release alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="flex items-center gap-1">
                  SEC
                  <Tooltip content="SEC filing alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {watchlistAlerts.map((watchlist, index) => (
              <tr key={watchlist.watchlistId || index} className="hover:bg-gray-50 group">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium text-gray-900">
                      {watchlist.name || `Watchlist ${index + 1}`}
                    </div>
                    <button
                      onClick={() => handleDeleteWatchlist(watchlist)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-gray-400 hover:text-red-500 p-1"
                      title="Delete watchlist"
                    >
                      <IoClose size={16} />
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Toggle
                    enabled={watchlist.emailSummary || false}
                    onChange={enabled => handleToggleChange(watchlist.watchlistId, 'emailSummary', enabled)}
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-600">{calculateNewsAlertCount(watchlist)}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-600">{calculatePressReleaseCount(watchlist)}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-600">{calculateSECCount(watchlist)}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="lg:hidden divide-y divide-gray-200">
        {watchlistAlerts.map((watchlist, index) => (
          <div key={watchlist.watchlistId || index} className="p-4 sm:p-6 group">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900">{watchlist.name || `Watchlist ${index + 1}`}</h3>
              <button
                onClick={() => handleDeleteWatchlist(watchlist)}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-gray-400 hover:text-red-500 p-1"
                title="Delete watchlist"
              >
                <IoClose size={16} />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">Personalized Daily Briefing</span>
                  <Tooltip content="Daily email summary of your watchlist activity" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
                <Toggle
                  enabled={watchlist.emailSummary || false}
                  onChange={enabled => handleToggleChange(watchlist.watchlistId, 'emailSummary', enabled)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">News Alert</span>
                  <Tooltip content="Real-time news alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
                <span className="text-sm text-gray-600">{calculateNewsAlertCount(watchlist)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">Press Release</span>
                  <Tooltip content="Press release alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
                <span className="text-sm text-gray-600">{calculatePressReleaseCount(watchlist)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-700">SEC</span>
                  <Tooltip content="SEC filing alerts for watchlist symbols" size="sm">
                    <TiInfoOutline className="text-gray-400" />
                  </Tooltip>
                </div>
                <span className="text-sm text-gray-600">{calculateSECCount(watchlist)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <Modal
        visible={deleteModal.show}
        onClose={cancelDeleteWatchlist}
        modalTitleBarClassName="p-1"
        fullScreen={true}
        title="Delete Watchlist"
        size={{ alert: true }}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            Would you like to remove the <strong>{deleteModal.watchlist?.name}</strong> watchlist?
          </p>
          <div className="flex gap-3">
            <button
              onClick={cancelDeleteWatchlist}
              disabled={deleteModal.deleting}
              className={`flex-1 px-4 py-2 rounded-md font-medium transition-colors ${
                deleteModal.deleting
                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
              }`}
            >
              Cancel
            </button>
            <button
              onClick={confirmDeleteWatchlist}
              disabled={deleteModal.deleting}
              className={`flex-1 px-4 py-2 rounded-md font-medium transition-colors flex items-center justify-center gap-2 ${
                deleteModal.deleting
                  ? 'bg-blue-400 text-white cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {deleteModal.deleting && <Spinner />}
              {!deleteModal.deleting && 'Confirm'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default WatchlistAlerts;
