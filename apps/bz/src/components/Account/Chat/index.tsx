'use client';
import React from 'react';
import { Table } from 'antd';
import { DateTime } from 'luxon';
import { StreamChat } from 'stream-chat';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Spinner } from '@benzinga/core-ui';
import { UpdateAvatar } from '@benzinga/user-ui';
import { getTimeDisplayFormat } from '@benzinga/date-utils';
import { ChatIdentity, ChatManager } from '@benzinga/chat-manager';
import { useTime } from '@benzinga/time-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import Hooks from '@benzinga/hooks';
import BodyCard from '../BodyCard';

interface ChatTabProps {
  mutedUsers: any;
  setMutedUsers: (mutedUsers: any) => void;
  username: string | undefined;
  chatIdentity: ChatIdentity;
  setChatIdentity: (identity: ChatIdentity) => void;
  chatClient: StreamChat | null;
}

const ChatTab: React.FC<ChatTabProps> = props => {
  const { t } = useTranslation(['profile', 'common']);
  const { chatClient, chatIdentity, mutedUsers, setChatIdentity, setMutedUsers, username } = props;
  const [chatNickname, setChatNickname] = React.useState<string>(chatIdentity?.nickname ?? '');
  const [chatNicknameResponse, setChatNicknameResponse] = React.useState<{ message: string; type: string } | null>(
    null,
  );

  const session = React.useContext(SessionContext);
  const chatManager = session.getManager(ChatManager);
  const { timeFormat, timeOffset } = useTime();
  const { width } = Hooks.useWindowSize();

  const formatDateTime = (date: string) => {
    if (width < 768) {
      return DateTime.fromISO(date).toFormat(`MMM dd, yyyy`);
    }
    return DateTime.fromISO(date)
      .plus({ minutes: timeOffset })
      .toFormat(`MMMM dd, yyyy @ ${getTimeDisplayFormat({ timeFormat })}`);
  };

  const handleUnmute = async (id: string) => {
    if (!chatClient) return console.log('Stream chat not available');
    await chatClient?.unmuteUser(id);
    session.getManager(TrackingManager).trackChatEvent('unmute', { muted_user_id: id });
    const filteredMutes = mutedUsers.filter((mute: any) => mute.action !== id);
    setMutedUsers(filteredMutes);
  };

  const handleAvatarChange = async (avatar: string) => {
    const newChatIdentity = {
      ...chatIdentity,
      avatar: avatar,
    };
    setChatIdentity(newChatIdentity);
  };

  const updateChatNickname = async () => {
    if (chatNickname) {
      const response = await chatManager.updateIdentity({ nickname: chatNickname });
      if (response.ok) {
        setChatNicknameResponse({
          message: t('Chat.success', { ns: 'profile' }),
          type: 'success',
        });
        setChatIdentity({ ...chatIdentity, nickname: chatNickname });
      } else if (response?.err) {
        let resBody: any = null;
        if (response.err.data instanceof Response) {
          resBody = await response.err.data.json();
        }
        setChatNicknameResponse({
          message: resBody?.nickname?.[0]
            ? t(resBody.nickname[0], { ns: 'profile' })
            : t('Chat.error', { ns: 'profile' }),
          type: 'error',
        });
      }
    }
  };

  return (
    <BodyCard>
      <h3 className="section-heading">{t('Chat.chat-settings', { ns: 'profile' })}</h3>
      <div className="section-body-wrapper flex-col">
        <div className="flex flex-col gap-6">
          <UpdateAvatar identity={chatIdentity} setAvatar={handleAvatarChange} username={username} />
          <div className="setting-group">
            <div className="setting-label">{t('Chat.chat-username', { ns: 'profile' })}</div>
            <div className="flex w-full items-center gap-2 flex-wrap">
              <input
                className="setting-input flex"
                onChange={e => setChatNickname(e.target.value)}
                placeholder={t('Chat.name', { ns: 'profile' })}
                type="text"
                value={chatNickname}
              />
              <Button className="account-button" onClick={updateChatNickname}>
                <div className="button-text">{t('Chat.update', { ns: 'profile' })}</div>
              </Button>
            </div>
            {chatNicknameResponse && (
              <div className={`text-sm ${chatNicknameResponse.type === 'error' ? 'text-red-500' : 'text-green-500'}`}>
                {chatNicknameResponse.message}
              </div>
            )}
          </div>
        </div>
      </div>
      <h3 className="section-heading">{t('Chat.muted-users', { ns: 'profile' })}</h3>
      <div className="section-body-wrapper flex-col overflow-x-auto">
        {mutedUsers === null && <Spinner />}
        {mutedUsers?.length > 0 && (
          <Table dataSource={mutedUsers} pagination={false}>
            <Table.Column dataIndex="user" key="user" title="User"></Table.Column>
            <Table.Column
              dataIndex="added"
              key="added"
              render={added => formatDateTime(added)}
              title="Added"
            ></Table.Column>
            <Table.Column
              dataIndex="expires"
              key="expires"
              render={expires => (expires ? formatDateTime(expires) : 'Unmute Manually')}
              title="Expires"
            ></Table.Column>
            <Table.Column
              dataIndex="action"
              key="action"
              render={id => (
                <a onClick={() => handleUnmute(id)} style={{ cursor: 'pointer' }}>
                  Delete
                </a>
              )}
              title="Action"
            ></Table.Column>
          </Table>
        )}
        {mutedUsers?.length === 0 && <p className="m-0">{t('Chat.muted', { ns: 'profile' })}</p>}
      </div>
    </BodyCard>
  );
};

export default ChatTab;
