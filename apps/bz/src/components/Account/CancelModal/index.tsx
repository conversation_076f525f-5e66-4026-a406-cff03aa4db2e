import React, { useContext, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Close } from '@benzinga/themed-icons';
import { User } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { Card, ShopManager } from '@benzinga/shop-manager';
import { SubscriptionsManager, UserSubscription } from '@benzinga/subscription-manager';
import { PlusManager, PlusProduct } from '@benzinga/plus-manager';
import { TrackingManager } from '@benzinga/tracking-manager';

import { ContactIcon, ChangePlanIcon, PauseIcon } from './Icons';
import { AlternativeCard } from './components';
import { ContactFlow } from './ContactFlow';
import { ChangePlanFlow } from './ChangePlanFlow';
import { PauseFlow } from './PauseFlow';
import { CancelFlow } from './CancelFlow';

interface CancelSubscriptionModalProps {
  creditCards: Card[];
  subscription: UserSubscription;
  setSubToCancel: (sub: UserSubscription | null) => void;
  user: User | null;
}

export const CancelSubscriptionModal: React.FC<CancelSubscriptionModalProps> = ({
  creditCards,
  setSubToCancel,
  subscription,
  user,
}) => {
  const [showInitial, setShowInitial] = useState(true);
  const [showContact, setShowContact] = useState(false);
  const [showChangePlan, setShowChangePlan] = useState(false);
  const [showPauseFlow, setShowPauseFlow] = useState(false);
  const [showCancel, setShowCancel] = useState(false);

  const [alternativePlans, setAlternativePlans] = useState<any[]>([]);
  const [matchingProduct, setMatchingProduct] = useState<PlusProduct | null>(null);
  const [monthlyPlan, setMonthlyPlan] = useState<PlusProduct | null>(null);

  const session = useContext(SessionContext);
  const subscriptionManager = session.getManager(SubscriptionsManager);
  const shopManager = session.getManager(ShopManager);

  const handleClose = () => {
    setSubToCancel(null);
  };

  const handlePathSelect = setState => {
    setShowInitial(false);
    setState(true);
  };

  useEffect(() => {
    const setupProducts = async () => {
      // get similar plan slugs
      const checkout = await session.getManager(ShopManager).preCheckout({
        selected_plan: subscription.basePlan,
        subscription: '',
      });
      const samePackageSlugs = checkout?.ok?.package?.plans.map(plan => plan.slug);

      // get alternative plans
      const researchProducts = await session.getManager(PlusManager).getProducts();
      const proProduct = {
        purchaseSlugs: ['bzpro-basic-monthly', 'bzpro-essential-monthly'],
        slug: '/benzinga-pro',
        title: 'Benzinga Pro',
        uuid: 'benzinga-pro',
      };
      const alternativePlans = [...(researchProducts?.ok ?? []), proProduct];

      // for cancel flow, last chance to upsell different tier with discount
      const matchedProduct = alternativePlans.find(product =>
        product.purchaseSlugs?.some(slug => samePackageSlugs?.includes(slug)),
      );
      if (matchedProduct) {
        matchedProduct.purchaseSlugs = matchedProduct?.purchaseSlugs?.filter(slug => !samePackageSlugs?.includes(slug));
        setMatchingProduct(matchedProduct);
      }

      // switch to monthly plan from annual
      if (checkout?.ok?.interval === 'year') {
        const monthlyEquivalent = checkout.ok?.package?.plans?.find(
          plan => plan.interval === 'month' && (plan as any).interval_count === 1,
        );
        if (monthlyEquivalent) {
          const monthlyPlan = {
            change: 'monthly',
            purchaseSlugs: [monthlyEquivalent.slug],
            slug: monthlyEquivalent.slug,
            title: 'Switch to Monthly Subscription',
            uuid: monthlyEquivalent.uuid,
          };
          setMonthlyPlan(monthlyPlan);
          alternativePlans.push(monthlyPlan);
        }
      }

      setAlternativePlans(alternativePlans.filter(prod => prod.purchaseSlugs && prod.purchaseSlugs.length > 0));
    };
    setupProducts();
  }, [session, subscription]);

  // handle all plan checkouts (upgrade/downgrade/switch)
  const handleSubscriptionCheckout = async (checkoutDetails, coupon = '', oldSub, paymentCard) => {
    try {
      const newSubReq = await shopManager.checkout([], checkoutDetails?.plan?.slug, '', coupon, paymentCard.uuid);
      if (!newSubReq.ok) {
        const errMessage = await (newSubReq.err?.data as Response)?.json();
        throw 'Checkout: ' + errMessage;
      }
      await session.getManager(TrackingManager).trackSubscriptionEvent('added', {
        interval: newSubReq.ok.interval,
        package_id: newSubReq.ok.uuid,
        price: newSubReq.ok.amount,
        product_id: newSubReq.ok.product,
      });

      const deleteSub = await subscriptionManager.deleteSubscription(oldSub.uuid);
      if (!deleteSub.ok) {
        const errMessage = await (deleteSub.err?.data as Response)?.json();
        throw 'Delete: ' + errMessage;
      }
      await session.getManager(TrackingManager).trackSubscriptionEvent('removed', {
        interval: oldSub.interval,
        package_id: oldSub.uuid,
        price: oldSub.amount,
        product_id: oldSub.product,
      });

      await subscriptionManager.getSubscriptions(true);
      return true;
    } catch (err: any) {
      if (err.response?.data?.requires_action) {
        console.log('NEED TO SET UP 3D SECURE CHECKOUT');
      } else {
        console.log('Error Changing Plans - ', err);
      }
      return false;
    }
  };

  // handle switch to monthly/keep sub with discount/pro upgrade or downgrade
  const handleSubscriptionUpgrade = async (checkoutDetails, coupon = '', paymentCard) => {
    try {
      const newSubReq = await subscriptionManager.upgradeSubscription(
        subscription,
        checkoutDetails.plan.slug,
        paymentCard.uuid,
        coupon,
        false,
      );
      if (!newSubReq.ok) {
        const errMessage = await (newSubReq.err?.data as Response)?.json();
        throw errMessage;
      }

      await session.getManager(TrackingManager).trackSubscriptionEvent('updated', {
        interval: subscription.interval,
        package_id: subscription.uuid,
        price: subscription.amount,
        product_id: subscription.product,
      });

      await subscriptionManager.getSubscriptions(true);
      return true;
    } catch (err) {
      console.log('Error upgrading subscription: ', err);
      return false;
    }
  };

  return (
    <div className="z-[100] fixed top-0 left-0 w-full h-full flex justify-center items-center bg-slate-400/50">
      <div className="w-96 p-6 border bg-white relative max-h-[600px] rounded-sm h-full shadow-lg mt-20">
        {showInitial && (
          <div className=" h-full relative">
            <h2>Cancel Subscription</h2>
            <p className="text-sm">
              <strong>Are you sure you want to cancel your subscription?</strong> You will no longer have access to{' '}
              {subscription.planName} features after{' '}
              {subscription.currentPeriodEnd ? dayjs(subscription.currentPeriodEnd).format('MMM DD, YYYY') : 'today'}.
            </p>
            <div className="flex flex-col gap-2">
              <AlternativeCard
                body="Our support team can help you with any problems you might be facing"
                handleClick={() => handlePathSelect(setShowContact)}
                icon={<ContactIcon />}
                label="Contact Us"
                rbgColor="22, 101, 52"
                title="Have Issues or Questions?"
              />
              <AlternativeCard
                body="Unsatisfied with your plan? Try another"
                handleClick={() => handlePathSelect(setShowChangePlan)}
                icon={<ChangePlanIcon />}
                label="Change Plan"
                rbgColor="30, 64, 175"
                title="Try a Different Plan?"
              />
              {subscription.interval === 'month' && subscription.intervalCount === 1 && (
                <AlternativeCard
                  body="Pause your account without being charged for up to 2 months"
                  handleClick={() => handlePathSelect(setShowPauseFlow)}
                  icon={<PauseIcon />}
                  label="Pause"
                  rbgColor="234, 179, 8"
                  title="Pause for 2 Months"
                />
              )}
            </div>
            <div className="text-sm flex gap-2 absolute bottom-0 right-0">
              <button className="border border-bzgrey-400 px-2 py-1 text-bzgrey-400 rounded-md" onClick={handleClose}>
                Close
              </button>
              <button
                className="border border-red-500 bg-red-500 px-2 py-1 text-white rounded-md"
                onClick={() => handlePathSelect(setShowCancel)}
              >
                Continue to Cancellation
              </button>
            </div>
          </div>
        )}
        {showContact && (
          <ContactFlow
            handleClose={handleClose}
            setShowContact={setShowContact}
            setShowInitial={setShowInitial}
            subscription={subscription}
            user={user}
          />
        )}
        {showChangePlan && (
          <ChangePlanFlow
            alternativePlans={alternativePlans}
            creditCards={creditCards}
            handleClose={handleClose}
            handleSubscriptionCheckout={handleSubscriptionCheckout}
            handleSubscriptionUpgrade={handleSubscriptionUpgrade}
            setShowChangePlan={setShowChangePlan}
            setShowInitial={setShowInitial}
            subscription={subscription}
          />
        )}
        {showCancel && (
          <CancelFlow
            creditCards={creditCards}
            handleClose={handleClose}
            handleSubscriptionCheckout={handleSubscriptionCheckout}
            handleSubscriptionUpgrade={handleSubscriptionUpgrade}
            matchingProduct={matchingProduct}
            monthlyPlan={monthlyPlan}
            setShowCancel={setShowCancel}
            setShowInitial={setShowInitial}
            subscription={subscription}
            user={user}
          />
        )}
        {showPauseFlow && (
          <PauseFlow
            creditCards={creditCards}
            handleClose={handleClose}
            setShowInitial={setShowInitial}
            setShowPauseFlow={setShowPauseFlow}
            subscription={subscription}
          />
        )}
        <button className="absolute top-2 right-2" onClick={handleClose}>
          <Close />
        </button>
      </div>
    </div>
  );
};

export default CancelSubscriptionModal;
