'use client';
import React, { useCallback, useEffect } from 'react';

import { AuthenticationManager, User } from '@benzinga/session';
import { useTranslation } from 'react-i18next';
import BodyCard from '../BodyCard';
import { TrackingManager } from '@benzinga/tracking-manager';

import { faClose, faSave } from '@fortawesome/pro-regular-svg-icons';
import { faPencil } from '@fortawesome/pro-solid-svg-icons/faPencil';
import { Button, Icon } from '@benzinga/core-ui';
import { SessionContext } from '@benzinga/session-context';
import { UserManager } from '@benzinga/user-manager';
import { SMSVerify } from '@benzinga/auth-ui';

interface ProfileTabProps {
  user: User | null;
}

const EmailVerifyButton = ({ verifyEmailRequest, verifySent }) => {
  const { t } = useTranslation(['profile', 'common']);
  return verifySent ? (
    <div className="text-sm text-green-700">
      <span>{t('Profile.verification-email-sent', { ns: 'profile' })}</span>
    </div>
  ) : (
    <div className="text-sm cursor-pointer" onClick={verifyEmailRequest}>
      <span className="text-red-500 cursor-pointer mr-1">{t('Profile.unverified', { ns: 'profile' })}</span>
      <button className="text-white bg-red-500 px-2 rounded-sm">{t('Profile.verified', { ns: 'profile' })}</button>
    </div>
  );
};

const ProfileTab: React.FC<ProfileTabProps> = props => {
  const { t } = useTranslation(['profile', 'common']);
  const { user } = props;
  const session = React.useContext(SessionContext);
  const userManager = session.getManager(UserManager);
  const authenticationManager = session.getManager(AuthenticationManager);

  const [profileEditMode, setProfileEditMode] = React.useState<boolean>(false);
  const [profileEditState, setProfileEditState] = React.useState<{ [key: string]: any }>({
    benzingaUid: user?.benzingaUid ?? '',
    displayName: user?.displayName ?? '',
    email: user?.email ?? '',
    firstName: user?.firstName ?? '',
    lastName: user?.lastName ?? '',
    phoneNumber: user?.phoneNumber ?? '',
  });
  const [saveError, setSaveError] = React.useState<string>('');
  const [verifySent, setVerifySent] = React.useState<boolean>(false);
  const [emailVerified, setEmailVerified] = React.useState<boolean>(user?.emailVerified ?? true);
  const [showSMSVerify, setShowSMSVerify] = React.useState<boolean>(false);
  const [showNewEmailRequest, setShowNewEmailRequest] = React.useState<boolean>(false);
  const [newPhoneVerified, setNewPhoneVerified] = React.useState<boolean>(false);

  const handleResetPasswordClick = useCallback(() => {
    const uuidHash = Buffer.from(`${profileEditState?.benzingaUid}`).toString('base64');
    const resetPasswordLink = `https://accounts.benzinga.com/password/change/${uuidHash}/`;
    session.getManager(TrackingManager).trackProfileEvent('reset_password');
    window.open(resetPasswordLink, '_blank');
  }, [session, profileEditState]);

  const onAccountInfoSave = async () => {
    setSaveError('');

    const nameRegex = /^[a-zA-Z\s'-]+$/;
    if (profileEditState.firstName && !nameRegex.test(profileEditState.firstName)) {
      setSaveError('First name cannot include special characters.');
      return;
    }
    if (profileEditState.lastName && !nameRegex.test(profileEditState.lastName)) {
      setSaveError('Last name cannot include special characters.');
      return;
    }

    if (user?.phoneNumber !== profileEditState.phoneNumber && !newPhoneVerified) {
      setSaveError('Please verify your phone number before saving.');
      return;
    }

    if (profileEditState.firstName !== user?.firstName || profileEditState.lastName !== user?.lastName) {
      const response = await userManager.editUser({
        first_name: profileEditState.firstName,
        last_name: profileEditState.lastName,
        // ...(user?.phoneNumber !== profileEditState.phoneNumber && { phone_number: profileEditState.phoneNumber }),
      });

      if (response?.err) {
        const errorMessage = await (response?.err?.data as Response)?.json();
        if (errorMessage?.phone_number) {
          if (errorMessage.phone_number?.[0] === 'This field may not be blank.') {
            setSaveError('Phone number is required.');
          } else if (errorMessage.phone_number?.includes('already in use')) {
            setSaveError(
              'Phone number already in use. Please call support at **************** <NAME_EMAIL>.',
            );
          } else {
            setSaveError(
              'There is an error with the Phone number. Please call support at **************** <NAME_EMAIL>.',
            );
          }
        } else {
          setSaveError(response.err.message);
        }
      } else if (response.ok) {
        setProfileEditMode(false);
        session.getManager(TrackingManager).trackProfileEvent('update');
      }
    }

    if (showNewEmailRequest) {
      window.location.reload();
    }

    return;
  };

  const handleCancel = () => {
    setSaveError('');
    setProfileEditMode(false);
    setProfileEditState({
      email: user?.email,
      firstName: user?.firstName,
      lastName: user?.lastName,
      phoneNumber:
        user?.phoneNumber !== profileEditState.phoneNumber && newPhoneVerified
          ? profileEditState.phoneNumber
          : user?.phoneNumber,
    });
  };

  const verifyEmailRequest = async () => {
    await authenticationManager.emailRequest();
    setVerifySent(true);
  };

  const handleVerifiedSMS = async () => {
    setNewPhoneVerified(true);
    setShowSMSVerify(false);
  };

  const confirmNewEmail = async () => {
    setSaveError('');
    if (profileEditState.email === user?.email) {
      setSaveError('Please enter a new email address.');
      return;
    }
    const emailRegex = /([a-zA-Z0-9._!#$%+^&*()[\]<>-]+)@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+/gi;
    if (profileEditState.email && !emailRegex.test(profileEditState.email)) {
      setSaveError('Email address is not valid.');
      return;
    }
    const response = await authenticationManager.verifyNewEmail(profileEditState.email);
    if (response?.err) {
      const errorMessage = await (response?.err?.data as Response)?.json();
      setSaveError(errorMessage?.new_email || 'There was an error verifying the new email address.');
    } else {
      setShowNewEmailRequest(true);
    }
  };

  const handleShowSMSVerify = () => {
    setSaveError('');
    const numberRegex = /^\+?[0-9()-\s]*$/;
    if (profileEditState.phoneNumber && !numberRegex.test(profileEditState.phoneNumber)) {
      setSaveError('Phone number is not valid.');
      return;
    } else {
      setShowSMSVerify(true);
    }
  };

  const AccountButton = () => (
    <div className="button-holder">
      {!profileEditMode ? (
        <Button className="account-button" onClick={() => setProfileEditMode(!profileEditMode)}>
          <div className="button-text">{t('Profile.edit', { ns: 'profile' })}</div>
          <Icon icon={faPencil} />
        </Button>
      ) : (
        <div className="button-holder">
          <Button className="account-button" onClick={handleCancel}>
            <div className="button-text">{t('Profile.cancel', { ns: 'profile' })}</div>
            <Icon icon={faClose} />
          </Button>
          <Button className="account-button" onClick={onAccountInfoSave}>
            <div className="button-text">{t('Profile.save', { ns: 'profile' })}</div>
            <Icon icon={faSave} />
          </Button>
        </div>
      )}
    </div>
  );

  useEffect(() => {
    if (user) {
      setProfileEditState({
        benzingaUid: user.benzingaUid,
        displayName: user.displayName,
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phoneNumber: user.phoneNumber || '',
      });
      setEmailVerified(user.emailVerified);
    }
  }, [user]);

  return (
    <BodyCard>
      <h3 className="section-heading">{t('Profile.profile-information', { ns: 'profile' })}</h3>
      <div className="section-body-wrapper justify-between flex-col relative">
        <div className="setting-row top">
          <div className="setting-group">
            <div className="setting-label">{t('Profile.first-name', { ns: 'profile' })}</div>
            {!profileEditMode && (
              <div className={`setting-value ${!profileEditState?.firstName ? 'placeholder' : ''}`}>
                {profileEditState?.firstName || t('Profile.add-first-name', { ns: 'profile' })}
              </div>
            )}
            {profileEditMode && (
              <input
                className="setting-input condensed"
                onChange={e => {
                  setProfileEditState({
                    ...profileEditState,
                    firstName: e.target.value,
                  });
                }}
                placeholder={t('Profile.add-first-name', {
                  ns: 'profile',
                })}
                value={profileEditState.firstName}
              />
            )}
          </div>
          <div className="setting-group">
            <div className="setting-label">{t('Profile.last-name', { ns: 'profile' })}</div>
            {!profileEditMode && (
              <div className={`setting-value ${!profileEditState?.lastName ? 'placeholder' : ''}`}>
                {profileEditState?.lastName || t('Profile.add-last-name', { ns: 'profile' })}
              </div>
            )}
            {profileEditMode && (
              <input
                className="setting-input condensed"
                onChange={e => {
                  setProfileEditState({
                    ...profileEditState,
                    lastName: e.target.value,
                  });
                }}
                placeholder={t('Profile.add-last-name', {
                  ns: 'profile',
                })}
                value={profileEditState.lastName}
              />
            )}
          </div>
          <AccountButton />
        </div>
        <div className="setting-row">
          <div className="setting-group">
            <div className="setting-label">{t('Profile.email-address', { ns: 'profile' })}</div>
            {!profileEditMode && (
              <div className={`setting-value ${!profileEditState?.email ? 'placeholder' : ''}`}>
                {profileEditState?.email || t('Profile.add-email-address', { ns: 'profile' })}
              </div>
            )}
            {!profileEditMode && !emailVerified && (
              <EmailVerifyButton verifyEmailRequest={verifyEmailRequest} verifySent={verifySent} />
            )}
            {profileEditMode && (
              <input
                className={'setting-input condensed ' + (emailVerified ? '' : 'cursor-not-allowed')}
                disabled={!emailVerified}
                onChange={e => {
                  setProfileEditState({
                    ...profileEditState,
                    email: e.target.value,
                  });
                }}
                placeholder={t('Profile.add-email-address', {
                  ns: 'profile',
                })}
                value={profileEditState.email}
              />
            )}
          </div>
          {profileEditMode && emailVerified && (
            <>
              {showNewEmailRequest && (
                <div className="col-span-2 text-sm text-red-700">
                  Please confirm email change with the account verification emails to your old and new email addresses.
                  Saving after confirming the emails will reload this page.
                </div>
              )}
              {profileEditState.email !== user?.email && (
                <div className="flex flex-col items-start justify-end">
                  <button className="bg-bzblue-800 text-white rounded-sm px-2 text-sm" onClick={confirmNewEmail}>
                    Verify New Email
                  </button>
                </div>
              )}
            </>
          )}
        </div>
        <div className="setting-row">
          <div className="setting-group">
            <div className="setting-label">{t('Profile.phone-no', { ns: 'profile' })}</div>
            {!profileEditMode && (
              <div className={`setting-value ${!profileEditState?.phoneNumber ? 'placeholder' : ''}`}>
                {profileEditState?.phoneNumber || t('Profile.add-phone-number', { ns: 'profile' })}
              </div>
            )}
            {profileEditMode && (
              <input
                className="setting-input condensed"
                disabled={newPhoneVerified}
                onChange={e => {
                  setProfileEditState({
                    ...profileEditState,
                    phoneNumber: e.target.value,
                  });
                }}
                placeholder={t('Profile.add-phone-number', {
                  ns: 'profile',
                })}
                value={profileEditState.phoneNumber}
              />
            )}
          </div>
          {profileEditMode && (
            <div className="flex flex-col items-start justify-end">
              {newPhoneVerified && (
                <div className="col-span-2 text-sm text-green-700">
                  <span>{t('Profile.phone-number-verified', { ns: 'profile' })}</span>
                </div>
              )}
              {profileEditState.phoneNumber !== user?.phoneNumber && (
                <button className="bg-bzblue-800 text-white rounded-sm px-2 text-sm" onClick={handleShowSMSVerify}>
                  Verify Phone with SMS
                </button>
              )}
            </div>
          )}
        </div>
        <div className="setting-row bottom">
          <div className="setting-group">
            <div className="setting-label">{t('Profile.password', { ns: 'profile' })}</div>
            <div className="setting-clickable" onClick={handleResetPasswordClick}>
              {t('Profile.change-your-password', { ns: 'profile' })}
            </div>
          </div>
          {saveError ? <div className="account-save-error">{saveError}</div> : <div></div>}
          <AccountButton />
        </div>
        {showSMSVerify && (
          <div className="absolute top-0 left-0 right-0 w-full h-full bg-white/50 z-50 flex items-center justify-center">
            <div className="h-fit max-w-80 bg-slate-400 rounded-lg shadow-lg p-4 mx-auto">
              <Icon className="float-right" icon={faClose} onClick={() => setShowSMSVerify(false)} />
              <SMSVerify
                isProfileVerification={true}
                onProfileVerified={handleVerifiedSMS}
                phoneNumber={profileEditState.phoneNumber}
              />
            </div>
          </div>
        )}
      </div>
    </BodyCard>
  );
};

export default ProfileTab;
