import { Ref, useContext, useEffect, useRef, useState } from 'react';
import { Provider, CardComponent } from '@chargebee/chargebee-js-react-wrapper';

import { SessionContext } from '@benzinga/session-context';
import { ShopManager } from '@benzinga/shop-manager';
import styled from '@benzinga/themetron';
import { Button } from '@benzinga/core-ui';
import toast from 'react-hot-toast';
import { CreditCardsIcons } from './CreditCardForm';

import { CHARGEBEE_PUBLISHABLE_KEY } from '../../../env';

export const ChargebeeForm = () => {
  const [cbInstance, setCbInstance] = useState<any>(null);
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errMsg, setErrMsg] = useState<string>('');

  const session = useContext(SessionContext);
  const cardRef: Ref<any> = useRef({ initialValue: '' });

  useEffect(() => {
    if (typeof window === 'undefined' || !window.Chargebee) return;
    window.Chargebee.init({
      domain: window.location.origin,
      publishableKey: CHARGEBEE_PUBLISHABLE_KEY,
      site: 'benzinga-main',
    });

    const instance = window.Chargebee.getInstance();
    setCbInstance(instance);
  }, []);

  const handleSubmit = async () => {
    setIsLoading(true);
    setErrMsg('');

    try {
      const tokenized = await cardRef.current.tokenize({});
      const token = tokenized?.token;
      const result = await session.getManager(ShopManager).addCreditCard(token);

      if (result.err) {
        const errorMessage = await (result?.err?.data as Response).json();
        throw errorMessage?.[0] ?? errorMessage?.token?.[0] ?? result;
      } else {
        toast.success('Successfully added card!');
        cardRef.current.clear();
      }
    } catch (err: any) {
      console.error('Error Tokenizing: ', err);
      const tokenErr = err?.token?.[0];
      const saveCardErr = typeof err === 'string' ? err : err?.message;
      setErrMsg(tokenErr ?? saveCardErr ?? 'Error adding card. Please try again.');
    }

    setIsLoading(false);
  };

  return (
    <div>
      <CreditCardsIcons />
      <ChargebeeWrapper>
        <Provider cbInstance={cbInstance}>
          <CardComponent onReady={() => setIsReady(true)} ref={cardRef} />
        </Provider>
        {!isReady && <div className="absolute top-0 bg-slate-300 rounded-full w-full h-full animate-pulse"></div>}
      </ChargebeeWrapper>
      <Button
        className={`w-full ${isLoading ? 'cursor-wait' : ''}`}
        disabled={isLoading || !isReady}
        onClick={handleSubmit}
      >
        {isLoading ? 'Adding Card...' : 'Add Card'}
      </Button>
      {errMsg && <div className="text-sm text-red-800 mt-2">{errMsg}</div>}
    </div>
  );
};

const ChargebeeWrapper = styled.div`
  margin: 1rem 0;
  height: 20px;
  position: relative;
`;
