import React from 'react';
import dayjs from 'dayjs';
import { CiSquareChevDown } from 'react-icons/ci';

import styled from '@benzinga/themetron';
import { SubscriptionsManager } from '@benzinga/subscription-manager';
import { BillingUserSubscription } from '.';
import { getNewEndPeriodDate } from '../CancelModal/CancelFlow';
import { Switch } from 'antd';
import toast from 'react-hot-toast';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

interface SubscriptionRowProps {
  subscription: BillingUserSubscription;
  subscriptionManager: SubscriptionsManager;
  setSubToCancel: (sub: BillingUserSubscription | null) => void;
}

export const SubscriptionRow: React.FC<SubscriptionRowProps> = ({
  setSubToCancel,
  subscription,
  subscriptionManager,
}) => {
  const [showParentSub, setShowParentSub] = React.useState(false);
  const [updating, setUpdating] = React.useState(false);
  const session = React.useContext(SessionContext);

  const handleRestart = async () => {
    try {
      const res = await subscriptionManager.restartSubscription(subscription.uuid);
      if (res.err) {
        const errMessage = await (res.err?.data as Response)?.json();
        throw errMessage;
      } else {
        session.getManager(TrackingManager).trackSubscriptionEvent('renewed', {
          interval: subscription.interval,
          package_id: subscription.uuid,
          price: subscription.amount,
          product_id: subscription.product,
        });
      }
    } catch (error) {
      console.error('Error restarting subscription', error);
      toast.error('Unable to update your subscription. Please contact our support team.');
    }
  };

  const handleEndTrial = async () => {
    try {
      const res = await subscriptionManager.deleteSubscription(subscription.uuid);
      if (res.err) {
        const errMessage = await (res.err?.data as Response)?.json();
        throw errMessage;
      } else {
        session.getManager(TrackingManager).trackSubscriptionEvent('cancelled', {
          interval: subscription.interval,
          package_id: subscription.uuid,
          price: subscription.amount,
          product_id: subscription.product,
        });
      }
    } catch (error) {
      console.error('Error ending trial subscription', error);
      toast.error('Unable to cancel your trial subscription. Please contact our support team.');
    }
  };

  const handleToggle = async () => {
    setUpdating(true);
    if (subscription.cancelAtPeriodEnd === false) {
      setSubToCancel(subscription);
    } else {
      await handleRestart();
    }
    setUpdating(false);
  };

  const getDateColumn = () => {
    let dateText = '';
    let dateLabel = '';
    let type = '';

    if (subscription.pendingPause) {
      dateLabel = 'Pause on';
      dateText = dayjs(subscription.pauseDate).format('MMM DD YYYY');
    } else if (subscription.reactivate) {
      dateLabel = 'Upgrade on';
      dateText = dayjs(subscription.currentPeriodEnd).format('MMM DD YYYY');
      type = 'upgrade';
    } else if (subscription.cancelAtPeriodEnd && subscription.currentPeriodEnd) {
      dateLabel = 'Cancel on';
      dateText = dayjs(subscription.currentPeriodEnd).format('MMM DD YYYY');
      type = 'cancel';
    } else if (subscription.status === 'trialing') {
      dateLabel = 'Trial ends on';
      dateText = dayjs(subscription.trialEnd).format('MMM DD YYYY');
    } else if (subscription.currentPeriodEnd === null) {
      dateLabel = 'No end date';
      dateText = 'Exclusive Access';
    } else {
      dateLabel = 'Auto-renew on';
      dateText = dayjs(subscription.currentPeriodEnd).format('MMM DD YYYY');
    }

    return (
      <div className="flex flex-col justify-between max-w-64 p-2">
        <span className={`text-xs ${type == 'cancel' ? 'text-red-500' : 'text-gray-500'}`}>{dateLabel}</span>
        <div className="flex flex-row items-center gap-1">
          {dateText}
          {type === 'upgrade' && (
            <CiSquareChevDown className="cursor-pointer" onClick={() => setShowParentSub(!showParentSub)} size={24} />
          )}
        </div>
      </div>
    );
  };

  return (
    <SubscriptionRowWrapper>
      <div>
        <SubscriptionPricing {...subscription} />
        {showParentSub && subscription.parentData && (
          <div className="flex flex-col my-2 text-sm">
            <div className="font-bold">Upgrade from</div>
            <SubscriptionPricing {...subscription.parentData} />
          </div>
        )}
      </div>

      {getDateColumn()}

      {subscription.pendingPause && (
        <div className="flex flex-col justify-between max-w-64 bg-yellow-100 p-2 rounded-sm">
          <div className="text-xs text-yellow-600 leading-tight">Subscription resumes on</div>
          <div className="font-bold text-yellow-950">{dayjs(subscription.willActivateOn).format('MMM DD YYYY')}</div>
        </div>
      )}

      {/* Use case: keep with discount, upgrade/downgrade with discount, switch to monthly with discount */}
      {subscription.reactivate &&
        subscription.willActivateOn &&
        ['cancel-save-monthly', 'cancel-save-annual-2'].includes(subscription?.coupon?.code ?? '') && (
          <div className="flex flex-col justify-between max-w-64 bg-blue-100 p-2 rounded-sm">
            <div className="text-xs text-blue-600 leading-tight">Base price resumes on</div>
            <div className="font-bold text-blue-950">{getNewEndPeriodDate(subscription, subscription.interval)}</div>
          </div>
        )}

      {subscription.status === 'trialing' && (
        <div className="flex flex-col gap-2 items-end py-2">
          {subscription.cancelAtPeriodEnd ? (
            <div className="font-bold text-sm bg-red-500 text-white p-2 rounded-md">Cancelled</div>
          ) : (
            <button className="font-bold text-sm bg-red-500 text-white p-2 rounded-md" onClick={handleEndTrial}>
              Cancel Trial
            </button>
          )}
        </div>
      )}
      {subscription.status === 'active' &&
        !subscription.pendingPause &&
        !subscription.pendingUpgrade &&
        subscription.currentPeriodEnd && (
          <div className="flex flex-col gap-2 items-end py-2">
            <div className="font-bold text-sm">Auto-Renew</div>
            <Switch
              checked={subscription.cancelAtPeriodEnd === false}
              disabled={updating}
              id={subscription?.uuid}
              onChange={handleToggle}
            />
          </div>
        )}
    </SubscriptionRowWrapper>
  );
};

const SubscriptionPricing: React.FC<BillingUserSubscription> = sub => {
  return (
    <div>
      <div className="mb-1">{sub?.planName}</div>
      <div>
        <div className="text-xs text-gray-500 flex flex-row justify-between max-w-64">
          <div>Base Price</div>
          <div className="font-bold text-gray-600">{sub?.totalPrice}</div>
        </div>
        {sub?.coupon && (
          <div className="text-xs text-gray-500 flex flex-row justify-between max-w-64">
            <div>
              Coupon: <strong className="text-gray-600">{sub?.coupon?.code}</strong>
            </div>
            {typeof sub?.coupon?.percentOff === 'number' && sub.coupon.percentOff !== 0 && (
              <div className="font-bold text-gray-600">{sub.coupon.percentOff}% off</div>
            )}
            {typeof sub?.coupon?.amountOff === 'number' && sub.coupon.amountOff !== 0 && (
              <div className="font-bold text-gray-600">{sub.coupon.amountOff} off</div>
            )}
          </div>
        )}
        <div className="text-xs text-gray-500 flex flex-row justify-between max-w-64">
          <div>Total Price</div>
          <div className="font-bold text-gray-600">{sub?.finalPrice}</div>
        </div>
      </div>
    </div>
  );
};

const SubscriptionRowWrapper = styled.div`
  align-items: flex-start;
  padding: 8px 12px;

  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1rem;
  border-bottom: 1px solid #e4e7e9;
`;

export default SubscriptionRow;
