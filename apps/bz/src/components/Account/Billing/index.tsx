'use client';
import React from 'react';
import dayjs from 'dayjs';

import { useUser } from '@benzinga/user-context';
import { Pagination } from '@benzinga/core-ui';
import { CreditCard } from '@benzinga/user-ui';
import { Card } from '@benzinga/shop-manager';
import { SubscriptionsManager, UserSubscription } from '@benzinga/subscription-manager';
import { useTranslation } from 'react-i18next';
import BodyCard from '../BodyCard';
import SubscriptionRow from './SubscriptionRow';
import { ChargebeeForm } from './ChargebeeForm';

import CancelSubscriptionModal from '../CancelModal';

export type BillingUserSubscription = UserSubscription & {
  pauseDate?: string;
  parentData?: UserSubscription;
  pendingPause?: boolean;
  pendingUpgrade?: boolean;
};

interface BillingTabProps {
  creditCards: Card[];
  subscriptionManager: SubscriptionsManager;
  subscriptions: BillingUserSubscription[];
}

const BillingTab: React.FC<BillingTabProps> = props => {
  const { t } = useTranslation(['profile', 'common']);
  const { creditCards, subscriptionManager, subscriptions } = props;
  const creditCardLimit = 2;
  const [page, setPage] = React.useState(1);
  const [cards, setCards] = React.useState<Card[]>(creditCards?.slice(0, creditCardLimit) ?? []);
  const user = useUser();

  const [subsList, setSubsList] = React.useState<BillingUserSubscription[]>([]);
  const [subToCancel, setSubToCancel] = React.useState<UserSubscription | null>(null);

  const handlePageChange = ({ nextPage, pageSize }) => {
    setPage(nextPage);
    const startIdx = (nextPage - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    const cards = creditCards.slice(startIdx, endIdx);
    setCards(cards);
  };

  React.useEffect(() => {
    const startIdx = (page - 1) * creditCardLimit;
    const endIdx = startIdx + creditCardLimit;
    const cardsPage = creditCards.slice(startIdx, endIdx);
    if (cardsPage.length === 0 && page > 1) {
      setPage(page - 1);
    } else {
      setCards(cardsPage);
    }
  }, [creditCards, page]);

  React.useEffect(() => {
    const pending = subscriptions.filter(sub => sub.status === 'pending');
    const activeSubs = subscriptions.filter(sub => sub.status === 'active');
    const trialSubs = subscriptions.filter(sub => sub.status === 'trialing');
    if (pending.length > 0) {
      const updatedSubs = pending.map(sub => {
        // no ref to original sub, only matching in basePlan. Hard coded logic to get Pause date, default only 2 months.
        if (sub.willActivateOn && !sub.reactivate) {
          sub.pendingPause = true;
          sub.pauseDate = dayjs(sub?.willActivateOn).subtract(2, 'month').format('MMM DD YYYY');
          const parentSub = activeSubs.find(aSub => aSub.basePlan === sub.basePlan);
          sub.parent = parentSub?.uuid;
        }

        // upgrades have parent uuid ref
        const pendingUpgrade = subscriptions.find(aSub => aSub.uuid === sub.parent);
        if (pendingUpgrade && sub.reactivate) {
          sub.pendingUpgrade = true;
          sub.willActivateOn = pendingUpgrade.currentPeriodEnd;
          sub.parentData = pendingUpgrade;
        }
        return sub;
      });

      // filters out parent upgrade subs
      const filterActiveUuid = updatedSubs.map(sub => sub.parent);
      const activeSubsFiltered = activeSubs.filter(sub => !filterActiveUuid.includes(sub.uuid));
      setSubsList([...trialSubs, ...updatedSubs, ...activeSubsFiltered]);
    } else {
      setSubsList([...trialSubs, ...activeSubs]);
    }
  }, [subscriptions]);

  return (
    <BodyCard>
      <h3 className="section-heading">{t('Billing.billing-info', { ns: 'profile' })}</h3>
      <div className="section-body-wrapper grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-8">
        <ChargebeeForm />
        <div className="flex flex-col gap-2 credit-card-element">
          {cards &&
            cards.length > 0 &&
            cards.map(card => (
              <CreditCard
                buttonMode="popconfirm"
                card={card}
                key={card.uuid}
                locked={creditCards.length === 1}
                reloadSession={() => {
                  return;
                }}
              />
            ))}

          {creditCards.length > 2 && (
            <Pagination
              buttonClassName="pagination-button"
              defaultPage={page}
              onPageChanged={handlePageChange}
              pageSize={creditCardLimit}
              totalItems={creditCards.length}
            />
          )}
        </div>
      </div>
      <h3 className="subscriptions-heading">{t('Billing.subscription-info', { ns: 'profile' })}</h3>
      {subsList.length > 0 &&
        subsList.map(subscription => (
          <SubscriptionRow
            key={subscription.uuid}
            setSubToCancel={setSubToCancel}
            subscription={subscription}
            subscriptionManager={subscriptionManager}
          />
        ))}
      {subsList.length === 0 && <div className="pt-2">{t('Billing.noactive-subscription', { ns: 'profile' })}</div>}
      {subsList.length > 0 && (
        <div className="text-sm pt-4">
          To cancel a subscription, toggle the Auto-Renew off. The subscription will be canceled at the end of the
          billing period.
        </div>
      )}
      {subToCancel && (
        <CancelSubscriptionModal
          creditCards={creditCards}
          setSubToCancel={setSubToCancel}
          subscription={subToCancel}
          user={user}
        />
      )}
    </BodyCard>
  );
};

export default BillingTab;
