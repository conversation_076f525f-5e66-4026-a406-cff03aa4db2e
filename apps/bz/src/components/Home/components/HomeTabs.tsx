'use client';

import React, { ReactNode } from 'react';
import styles from './HomeTabs.module.scss';
import { Tabs } from '@benzinga/core-ui';
import { HomePageTabKey, homePageTabs, PageTabsT } from '../interface';
import { isMobile } from '@benzinga/device-utils';
import Hooks from '@benzinga/hooks';
import { handleMenuItemsClickEvent } from '@benzinga/navigation-ui';

type TabIdentification = string;

interface Tab {
  title: string;
  slug: TabIdentification;
  url: string;
}

interface Props {
  tabs?: Tab[];
  activeTab: HomePageTabKey;
  tabContent: React.ReactNode;
}

export const HOME_PAGE_TABS: homePageTabs = {
  BRIEFS: 'briefs',
  EXCLUSIVES: 'exclusives',
  OFFER: 'offer',
  RECENT: 'recent',
  STOCKIDEAS: 'stockideas',
  TOOLSIDEA: 'toolsidea',
  TRENDING: 'trending',
  WATCH: 'watch',
};

export const pageTabs: PageTabsT = {
  [HOME_PAGE_TABS.TRENDING]: {
    clientSide: false,
    loaded: false,
    title: 'Trending',
    url: '/',
  },
  [HOME_PAGE_TABS.RECENT]: {
    clientSide: true,
    loaded: false,
    title: 'Latest News',
    url: '/recent',
  },
  [HOME_PAGE_TABS.STOCKIDEAS]: {
    clientSide: true,
    loaded: false,
    title: 'Stock Ideas',
    url: '/stock-ideas',
  },
  [HOME_PAGE_TABS.BRIEFS]: {
    clientSide: true,
    loaded: false,
    title: 'Briefs',
    url: '/briefs',
  },
  [HOME_PAGE_TABS.EXCLUSIVES]: {
    clientSide: true,
    loaded: false,
    title: 'Exclusives',
    url: '/exclusives',
  },
  [HOME_PAGE_TABS.WATCH]: {
    clientSide: true,
    loaded: false,
    title: 'Benzinga TV',
    url: '/watch',
  },
  // [HOME_PAGE_TABS.TOOLSIDEA]: {
  //   title: 'Premium Tools & Ideas',
  //   shouldReload: true,
  //   clientSide: true,
  //   loaded: false,
  //   url: 'https://www.benzinga.com/premium-products?utm_source=benzigna-homepage&utm_medium=benzinga-homepage'
  // },
};

export const HomeTabs: React.FC<Props> = ({ activeTab, tabContent }) => {
  const isMobileHydrated = Hooks.useHydrate(isMobile(), false);

  const handleTabRouting = React.useCallback((tabKey: string) => {
    const tab = pageTabs[tabKey];
    handleMenuItemsClickEvent('menu_click', 'tab_list', tab.title);
    if (tab) {
      if (tab.openInNewTab) {
        window.open(tab.url, '_blank');
      } else {
        window.location.href = tab.url;
      }
    }
  }, []);

  const renderTabs = React.useCallback(() => {
    const tabs: { component?: React.ReactElement; key: HomePageTabKey; name: string }[] = [];

    for (const tabKey in pageTabs) {
      tabs.push({
        component: activeTab === tabKey ? (tabContent as React.ReactElement) : undefined,
        key: tabKey as HomePageTabKey,
        name: pageTabs[tabKey].title,
      });
    }
    return tabs;
  }, [activeTab, tabContent]);

  return (
    <div className={styles.root}>
      <Tabs
        activeTab={activeTab}
        isShowMoreButtonEnabled={!isMobileHydrated}
        onChange={handleTabRouting}
        tabs={renderTabs()}
        variant="lifted"
      />
    </div>
  );
};

// styles moved to CSS Modules in ./HomeTabs.module.scss
