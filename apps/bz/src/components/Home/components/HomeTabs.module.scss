.root {
  @media screen and (max-width: 800px) {
    margin-top: 0rem;
  }

  .tabs-container.lifted-variant {
    .tab-list-wrapper {
      border-bottom: 1px solid var(--bz-gray600, #818b97);
      margin-bottom: 1rem;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .tab-list-wrapper {
      .tab-list {
        margin-bottom: -1px;

        button.tab {
          color: var(--bz-blue500, #92a7c5);
          font-size: 1.25rem; /* theme.fontSize.xl */
          font-weight: 700; /* theme.fontWeight.bold */
          padding: 4px 20px;
          text-align: center;

          &.active {
            background-color: transparent;
            border: 1px solid var(--bz-gray600, #818b97);
            border-bottom-color: var(--bz-white, #ffffff);
            color: var(--bz-foreground-active, #192940);

            &:hover {
              border-bottom-color: var(--bz-white, #ffffff);
            }
          }

          @media screen and (max-width: 800px) {
            padding: 10px 12px;
            border: none !important;
            min-width: auto;
            &.active {
              border-bottom: solid 3px #3f82f8;
            }
          }
        }

        button.tab:first-child {
          min-width: 120px;
        }
        button.tab:nth-child(2) {
          min-width: 147px;
        }
        button.tab:nth-child(3) {
          min-width: 141px;
        }
        button.tab:nth-child(4) {
          min-width: 91px;
        }
        button.tab:nth-child(5) {
          min-width: 132px;
        }
        button.tab:nth-child(6) {
          min-width: 147px;
        }
      }
    }
  }
}


