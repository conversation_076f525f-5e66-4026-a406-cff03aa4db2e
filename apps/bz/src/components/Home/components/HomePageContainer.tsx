import styled from '@benzinga/themetron';

/**
 * @deprecated This styled component has been replaced with Tailwind CSS implementation.
 * Use the HomePageWrapper component from app/_components/HomePageWrapper.tsx instead,
 * or the new HomePageContainer from app/_components/HomePageContainer.tsx for app router pages.
 * This component will be removed in a future version.
 */
export const HomePageContainer = styled.div`
  padding: 12px;
  margin: auto;
  .home-page-wrapper {
    display: flex;
    /* display: grid; */
    /* gap: 16px;
    grid-template-columns: repeat(1, minmax(0, 1fr)); */
  }
  .main {
    /* grid-column: span 3; */
    flex: 1;
  }
  .sidebar {
    /* grid-column: span 1; */
    flex: 0;
    min-width: 300px;
    margin-left: 16px;
  }
  @media (min-width: 1400px) {
    max-width: 1400px;
    padding: 16px 0;
    .home-page-wrapper {
      /* grid-template-columns: repeat(4, minmax(0, 1fr)); */
    }
  }
  @media (max-width: 800px) {
    .sidebar {
      margin-left: 0;
    }
    .home-page-wrapper {
      display: block;
    }
  }
`;
