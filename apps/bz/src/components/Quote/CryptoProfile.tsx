'use client';
import React from 'react';
import ErrorBoundry from '../ErrorBoundry';
import { QuoteProfile } from '../../entities/quoteEntity';
import { NoFirstRender } from '@benzinga/hooks';
import LazyLoad from 'react-lazyload';
import { TaboolaPlacement } from '@benzinga/ads';
import { ContentFeed } from '@benzinga/news';
import { TradingViewWidget } from '@benzinga/charts-ui';

export interface Props {
  profile: QuoteProfile;
}

export const CryptoProfile = ({ profile, symbol }) => {
  // const [renderAd, toggleRenderAd] = useState(false);
  return (
    <div className="quote-news-menu col-span-12">
      <div className="trading-view-widget-wrapper">
        <ErrorBoundry>
          <React.Suspense>
            <NoFirstRender>
              <TradingViewWidget
                widgetProps={{ height: 600, symbol: `${symbol?.replace('/USD', 'USDT')}`, width: '100%' }}
              />
            </NoFirstRender>
          </React.Suspense>
        </ErrorBoundry>
      </div>
      <div className="my-5">
        <ErrorBoundry>
          <React.Suspense>
            <LazyLoad offset={100} once>
              <TaboolaPlacement
                container="taboola-mid-page-thumbnails---quotes"
                mode="thumbnails-mq"
                placement="Mid-Page Thumbnails - Quotes"
                settings={{
                  other: 'auto',
                }}
                url=""
                vizSensor={false}
              />
            </LazyLoad>
          </React.Suspense>
        </ErrorBoundry>
      </div>
      <ErrorBoundry>
        <ContentFeed
          contentId="Crypto Recent News"
          isInfinite
          limit={10}
          loadMore
          nodes={profile.cryptoNews}
          noResultsElement={<span>There doesn&apos;t seem to be any stories for {symbol}</span>}
          query={{
            tickers: profile.newsSymbol,
          }}
          title="Recent News"
        />
      </ErrorBoundry>
    </div>
  );
};
