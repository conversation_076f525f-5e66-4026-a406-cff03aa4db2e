import { CallsAndPuts } from '@benzinga/data-option-chain';
import { format } from 'date-fns';
import { useCallback } from 'react';
import styled from 'styled-components';

interface TableRowProps {
  className?: string;
  values?: CallsAndPuts;
}

const TableRow = ({ className, values }: TableRowProps) => {
  const formatNumber = useCallback((value: number, isPercentage = false): number | string => {
    if (typeof value === 'number') {
      return isPercentage ? `${Math.abs(value)}%` : Math.abs(value);
    }
    return '-';
  }, []);
  if (!values) return null;
  return (
    <TableRowContainer className={className}>
      <div className="flex flex-col">
        <span className="block--label">Last Price</span>
        <span>{values?.lastTradePrice ?? '-'}</span>
        {values?.lastTradePrice && (
          <span className="last-price-date">
            {values?.lastTradeTime && format(values.lastTradeTime, 'MMM dd, yyyy')}
          </span>
        )}
      </div>
      <div className="flex flex-col">
        <span className="block--label">Change</span>
        <div className="flex flex-col gap-1">
          <span>{values?.change ? formatNumber(values.change) : '-'}</span>
          {typeof values?.change == 'number' && (
            <span className={`price-in-percentage ${values.change < 0 ? 'price-down' : 'price-up'}`}>
              {formatNumber(values.changePercent, true)}
            </span>
          )}
        </div>
      </div>
      <div className="last-columns">
        <div>
          <span className="block--label">Bid</span>
          <span>{values?.bidPrice ?? '-'}</span>
        </div>
        <div>
          <span className="block--label">Ask</span>
          <span>{values?.askPrice ?? '-'}</span>
        </div>

        <div>
          <span className="block--label">volume</span>
          <span>{values?.volume ?? '-'}</span>
        </div>

        <div>
          <span className="block--label">Intrest</span>
          <span>{values?.openInterest ?? '-'}</span>
        </div>
      </div>
    </TableRowContainer>
  );
};

export default TableRow;

const TableRowContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  align-items: center;
  border: 1px solid ${({ theme }) => theme.colorPalette.gray100};
  padding: 11px;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  color: ${({ theme }) => theme.colorPalette.gray700};
  gap: 8px;
  width: 100%;
  margin-bottom: 4px;

  .last-price-date {
    color: ${({ theme }) => theme.colorPalette.gray400};
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
  }
  &.border-right,
  &.border-left {
    background: var(--Blue-05, rgba(63, 131, 248, 0.05));
  }
  &.border-right {
    border-right: 3px solid ${({ theme }) => theme.colorPalette.blue500};
    border-radius: 0px 3px 3px 0px;
  }
  &.border-left {
    border-left: 3px solid ${({ theme }) => theme.colorPalette.blue500};
    border-radius: 3px 0px 0px 3px;
  }

  .price-in-percentage {
    display: inline-flex;
    height: 16px;
    padding: 2px 4px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: fit-content;
    flex-shrink: 0;
    border-radius: 4px;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
  }

  .price-up {
    background: var(--Green-10, rgba(48, 191, 96, 0.1));
    color: ${({ theme }) => theme.colorPalette.bzGreen100};
  }

  .price-down {
    background: var(--Red-10, rgba(255, 64, 80, 0.1));
    color: ${({ theme }) => theme.colorPalette.bzRed100};
  }

  .block--label {
    display: none;
    color: ${({ theme }) => theme.colorPalette.gray400};
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
  }

  .block--price {
    color: ${({ theme }) => theme.colorPalette.gray700};
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px; /* 114.286% */
  }
  .block--date {
    color: ${({ theme }) => theme.colorPalette.gray500};
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px; /* 116.667% */
  }

  .last-columns {
    display: grid;
    width: 100%;
    grid-template-columns: repeat(4, 1fr);
    grid-column: span 4;
    gap: 8px;
    div {
      display: flex;
      flex-direction: column;
    }
  }
  @media screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    border: none !important;
    background: transparent;
    margin-bottom: 0px !important;

    &.border-right {
      border-right: 3px solid ${({ theme }) => theme.colorPalette.blue500} !important;
    }
    &.border-left {
      border-left: 3px solid ${({ theme }) => theme.colorPalette.blue500} !important;
    }

    border-left: 1px solid ${({ theme }) => theme.colorPalette.gray100};
    & > div:not(:last-child):after {
      content: '';
      width: 100%;
      height: 1px;
      background: ${({ theme }) => theme.colorPalette.gray100};
      margin: 6px 0;
    }

    .block--label {
      display: block;
    }

    .last-columns {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column: span 1;
      gap: 8px;

      div {
        display: flex;
        flex-direction: column;
      }
    }
  }
`;
