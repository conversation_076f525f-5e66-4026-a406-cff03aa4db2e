import React from 'react';
import styled from '@benzinga/themetron';
import { Ratings } from '@benzinga/calendar-manager';
import { RatingsByMonthWithCounts, generateMockRatingsByMonthWithCounts } from '../../../quoteV2Utils';
import RadialGauge from './RadialGuage';
import AnalystRatingBar from './AnalystRatingBar';
import { DateTime, Interval } from 'luxon';
import { Icon, Tooltip } from '@benzinga/core-ui';
import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { useTranslation } from 'react-i18next';
import i18n, { translate } from '@benzinga/translate';

const AnalystRatingsCharts: React.FC<{
  ratingsByMonthWithCounts: RatingsByMonthWithCounts[];
  averageRating: number | null;
  ratings?: Ratings[];
}> = ({ averageRating, ratings, ratingsByMonthWithCounts }) => {
  return (
    <div className="flex flex-col md:flex-row gap-8 md:gap-4 w-full">
      <AnalystTrend ratingsByMonthWithCounts={ratingsByMonthWithCounts} />
      <div className="max-w-[460px] w-full">
        <AnalystRatingGauge averageRating={averageRating} ratings={ratings || []} />
      </div>
    </div>
  );
};

export default AnalystRatingsCharts;

const AnalystTrend: React.FC<{ ratingsByMonthWithCounts: RatingsByMonthWithCounts[] }> = ({
  ratingsByMonthWithCounts,
}) => {
  const { t } = useTranslation('quote', { i18n });
  if (ratingsByMonthWithCounts.length === 0) return null;

  const strippedRatings =
    ratingsByMonthWithCounts.length >= 6 ? ratingsByMonthWithCounts.slice(2, 7) : ratingsByMonthWithCounts;

  const mockDataRatingsByMonthWithCounts = generateMockRatingsByMonthWithCounts(5);

  return (
    <div className="analyst-rating-bar-chart card flex flex-col flex-1">
      <h6 className="font-bold mb-4">{t('Quote.Financials.analyst-trend')}</h6>
      <div className="flex gap-5 md:gap-8 mt-auto mx-auto w-full justify-center">
        {ratingsByMonthWithCounts.length === 0 &&
          mockDataRatingsByMonthWithCounts.map(rating => (
            <div className="flex-1 min-w-0 max-w-[50px] h-[200px]" key={rating.date}>
              <AnalystRatingBar placeholder={true} type="vertical" {...rating} />
            </div>
          ))}
        {strippedRatings.map(rating => (
          <div className="flex-1 min-w-0 max-w-[50px] h-[200px]" key={rating.date}>
            <AnalystRatingBar type="vertical" {...rating} />
          </div>
        ))}
      </div>
      <div className="mt-2 lg:ml-0">
        <ColorKey isBar={true} />
      </div>
    </div>
  );
};

const AnalystRatingGauge: React.FC<{ averageRating: number | null; ratings: Ratings[] }> = ({
  averageRating,
  ratings,
}) => {
  const { t } = useTranslation('quote', { i18n });
  if (ratings.length === 0 || !averageRating) return null;

  const radialGaugeValue = averageRating ? averageRating / 20 : 0;
  const ratingValue = getRatingValue(radialGaugeValue);

  const calculateTimeBetween = () => {
    const lastRating = ratings[ratings.length - 1];

    if (!lastRating) return null;

    const now = DateTime.now();

    const lastRatingDate = DateTime.fromISO(lastRating?.date);
    const interval = Interval.fromDateTimes(lastRatingDate, now);

    const hoursBetween = interval.toDuration('hours').hours;

    if (hoursBetween < 24) {
      return `${hoursBetween} hours`;
    } else if (hoursBetween < 24 * 7) {
      return `${Math.floor(interval.toDuration('days').days)} days`;
    } else if (hoursBetween < 24 * 30) {
      return `${Math.floor(interval.toDuration('weeks').weeks)} weeks`;
    } else if (hoursBetween < 24 * 365) {
      return `${Math.floor(interval.toDuration('months').months)} months`;
    } else if (hoursBetween < 24 * 365 * 2) {
      return `${Math.floor(interval.toDuration('years').years)} years`;
    }

    return '';
  };

  const timeBetween = calculateTimeBetween();

  return (
    <AnalystRatingGaugeContainer className="analyst-rating-semi-circle-chart card flex-1">
      <div className="flex items-center gap-2 mb-4">
        <h6 className="font-bold">{t('Quote.Financials.analyst-rating')}</h6>
        {ratings.length > 0 && (
          <Tooltip
            content={t('Quote.Financials.analyst-rating-score', {
              ratingsLength: ratings.length,
              s: ratings.length > 1 ? 's' : '',
              timeBetween,
            })}
            width={250}
          >
            <Icon className="info-circle-icon text-sm flex items-center justify-center" icon={faInfoCircle} />
          </Tooltip>
        )}
      </div>
      <div className="w-full mx-auto">
        <div className="box gauge--3 relative">
          <RadialGauge max={5} min={1} value={radialGaugeValue} />
          <div
            className={`analyst-rating-score ${
              averageRating && ratingValue ? ratingValue?.className : 'no-rating'
            } flex flex-col items-center justify-center absolute bottom-[-30px] left-1/2 -translate-y-2/4 -translate-x-2/4 z-[2]`}
          >
            <div className="analyst-rating-score-circle text-white font-bold rounded-full w-12 h-12 text-center flex items-center justify-center">
              {averageRating ? radialGaugeValue.toFixed(1) : '-'}
            </div>
            <span className="analyst-rating-label rounded-full font-bold text-sm z-[2] -mt-2 py-0.5 px-2 min-w-[60px] text-center">
              {averageRating && ratingValue ? ratingValue.label() : t('Quote.Financials.no-rating')}
            </span>
          </div>
        </div>
      </div>
      {/* {ratings.length > 0 && (
        <div className="text-sm text-center text-gray-500 mt-2 mb-1">
          This score is based on {ratings.length} analyst{ratings.length > 1 ? 's' : ''} from the past {timeBetween}{' '}
        </div>
      )} */}
      <div className="mt-2 lg:ml-0">
        <ColorKey />
      </div>
    </AnalystRatingGaugeContainer>
  );
};

const ratingsValuesMap = {
  0: {
    className: 'strong-sell',
    label: () => translate('Market.Actions.strong-sell'),
    primary: '#EF4444',
    secondary: '#F8D8D8',
  },
  1: { className: 'sell', label: () => translate('Market.Actions.sell'), primary: '#F97316', secondary: '#FAE0D1' },
  2: { className: 'hold', label: () => translate('Market.Actions.hold'), primary: '#FFD644', secondary: '#FAF3D8' },
  3: {
    className: 'buy',
    label: () => i18n.t('Market.Actions.buy'),
    primary: '#84CC16',
    secondary: '#E1F1D2',
  },
  4: {
    className: 'strong-buy',
    label: () => translate('Market.Actions.strong-buy'),
    primary: '#30BF60',
    secondary: '#D9F2DF',
  },
};

const getRatingValue = (averageRating: number) => {
  if (averageRating > 4.5) {
    return ratingsValuesMap[4];
  } else if (averageRating > 3.5) {
    return ratingsValuesMap[3];
  } else if (averageRating > 2.5) {
    return ratingsValuesMap[2];
  } else if (averageRating > 1.5) {
    return ratingsValuesMap[1];
  } else if (averageRating > 0) {
    return ratingsValuesMap[0];
  }

  return 0;
};

const AnalystRatingGaugeContainer = styled.div`
  .analyst-rating-score-circle {
    color: #ffffff;
    background-color: #d0d4da;
  }
  .analyst-rating-label {
    background: #e1eaf9;
    color: #000000;
  }
  .strong-buy {
    .analyst-rating-score-circle {
      background: ${ratingsValuesMap[4].primary};
    }
    .analyst-rating-label {
      background: ${ratingsValuesMap[4].secondary};
      color: ${ratingsValuesMap[4].primary};
    }
  }

  .buy {
    .analyst-rating-score-circle {
      background: ${ratingsValuesMap[3].primary};
    }
    .analyst-rating-label {
      background: ${ratingsValuesMap[3].secondary};
      color: ${ratingsValuesMap[3].primary};
    }
  }

  .hold {
    .analyst-rating-score-circle {
      background: ${ratingsValuesMap[2].primary};
      color: #7e6a22;
    }
    .analyst-rating-label {
      background: ${ratingsValuesMap[2].secondary};
      color: #ac9339;
    }
  }

  .sell {
    .analyst-rating-score-circle {
      background: ${ratingsValuesMap[1].primary};
      font-weight: 600;
    }
    .analyst-rating-label {
      background: ${ratingsValuesMap[1].secondary};
      color: ${ratingsValuesMap[1].primary};
    }
  }

  .strong-sell {
    .analyst-rating-score-circle {
      background: ${ratingsValuesMap[0].primary};
    }
    .analyst-rating-label {
      background: ${ratingsValuesMap[0].secondary};
      color: ${ratingsValuesMap[0].primary};
    }
  }
`;

export const ColorKey = ({ isBar }: { isBar?: boolean }) => {
  const { t } = useTranslation('common', { i18n });

  const allColors: { color: string; label: string }[] = [
    !isBar && {
      color: '#30BF60',
      label: t('Market.Actions.strong-buy'),
    },
    {
      color: isBar ? '#30BF60' : '#91DBB0',
      label: t('Market.Actions.buy'),
    },
    {
      color: '#FFD644',
      label: t('Market.Actions.hold'),
    },
    {
      color: '#FF9244',
      label: t('Market.Actions.sell'),
    },
    {
      color: '#FF4050',
      label: t('Market.Actions.strong-sell'),
    },
  ].filter(Boolean) as { color: string; label: string }[];

  return (
    <ColorKeyWrapper className="flex items-center justify-center gap-4 lg:gap-2 p-1 flex-wrap">
      {allColors.map(color => (
        <div className="flex items-center gap-2" key={color.label}>
          <div className="color-key-box" style={{ background: color.color }}></div>
          <div className="text-sm">{color.label}</div>
        </div>
      ))}
    </ColorKeyWrapper>
  );
};

const ColorKeyWrapper = styled.div`
  .color-key-box {
    height: 6px;
    width: 6px;
  }
`;
