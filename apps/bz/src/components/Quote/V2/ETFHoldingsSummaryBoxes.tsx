import { useTranslation } from 'react-i18next';
import { StatBoxesContainer } from '@benzinga/core-ui';
import i18n from '@benzinga/translate';
import { Fund } from '@benzinga/etf-manager';
import StatBox from './StatBox';
import { DateTime } from 'luxon';

export const ETFHoldingsSummaryBoxes: React.FC<{ etfFund: Fund }> = ({ etfFund }) => {
  const { t } = useTranslation('quote', { i18n });

  if (!etfFund) {
    return null;
  }

  return (
    <>
      {etfFund?.top_holder_snapshotdate && etfFund?.top_holder_snapshotdate != '' && (
        <p className="my-2 section-description">
          {t('Quote.Holdings.snapshot-date', {
            date: DateTime.fromISO(etfFund.top_holder_snapshotdate).toFormat('LLL dd, yyyy'),
          })}
        </p>
      )}

      <StatBoxesContainer className="etf-holdings-summary-boxes">
        <StatBox
          title={`${t('Quote.Holdings.number-of-stock-holding-net')}`}
          value={etfFund.number_of_stock_holdingnet || '-'}
        />
        <StatBox
          title={`${t('Quote.Holdings.number-of-bond-holding-net')}`}
          value={etfFund.number_of_bond_holdingnet || '-'}
        />
        <StatBox
          className="consensus-price-target"
          title={`${t('Quote.Holdings.total-number-of-holdings')}`}
          value={etfFund.total_number_holding || '-'}
        />
        <StatBox title={`${t('Quote.Holdings.number-of-etfs')}`} value={etfFund?.fund_etf_count || '-'} />
      </StatBoxesContainer>
    </>
  );
};

export default ETFHoldingsSummaryBoxes;
