
.getAccessWrapper {
  background-color: #f2f8ff;
  background-image: linear-gradient(90deg, rgba(63, 131, 248, 0) 0%, rgba(63, 131, 248, 0.16) 100%);
  background-repeat: no-repeat;
  background-size: 25% 100%;
  background-position: right;
  border: 1px solid #ceddf2;
  border-radius: 4px;
  padding: 1rem;
  max-height: 80px;
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;

  .accessHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    min-width: 80px;

    .iconWrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #e0edff;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      border: 2px solid #f2f8ff;
      &:first-child {
        z-index: 2;
      }
      &:last-child {
        position: absolute;
        right: 10%;
      }
    }
  }

  .accessTitle {
    color: #192940;
    font-size: 14px;
    font-weight: 800;
    line-height: 20px;
    text-transform: capitalize;
    margin-bottom: 0;
    flex-grow: 1;
  }

  a {
    background-color: #3f83f8;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    white-space: nowrap;
    &:hover {
      background-color: #1c64f2;
    }

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  @media (max-width: 800px) {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    max-height: 100%;
    background-image: none;

    .accessHeader {
      flex-direction: row-reverse;
      justify-content: space-between;
      width: 100%;
    }

    button {
      font-size: 12px;
      width: 100%;
      padding: 4px 12px;
      text-transform: uppercase;
    }
  }
}

// Overview Component
.rowWrapper {
  position: relative;
  display: flex;
  align-items: center;

  .divider, .mobileContent {
    display: none;
  }

  @media (max-width: 800px) {
    display: block;
    background-color: #f2f8ff;
    border-radius: 4px;
    padding: 12px 16px;
    border: 1px solid #ceddf2;

    .divider {
      display: block;
    }

    .mobileContent {
      display: flex;
    }

    .card:hover .descriptionTooltip {
      display: none;
    }

    .mainContent {
      gap: 0;
    }
  }
}

.divider {
  width: 2px;
  min-height: 80px;
  height: 100%;
  background-color: #e1ebfa;
  margin: 0 1rem;
}

.mainContent {
  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.mobileContent {
  display: flex;
  flex-direction: column;

  .descriptionCard {
    margin-top: 16px;

    .cardHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        text-decoration: underline;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      button {
        background-color: #3f83f8;
        color: #fff;
        border-radius: 2px;
        padding: 4px 8px;
        height: fit-content;
        font-size: 12px;
      }
    }
  }
}

.card {
  background-color: #f2f8ff;
  border-radius: 4px;
  padding: 12px 16px;
  border: 1px solid #ceddf2;
  max-height: 105px;
  width: 100%;

  &:hover {
    border: 1px solid #3f83f8;
    cursor: pointer;
    .descriptionTooltip {
      display: block;
    }
  }

  @media (max-width: 1293px) {
    justify-content: center;
    height: fit-content;
    flex-wrap: wrap;
    max-width: 245px;
  }

  @media (max-width: 800px) {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
    max-width: 100%;
    border: none;
    padding: 0;
    &:hover {
      border: none;
    }
  }
}

.title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    color: #192940;
  }

  @media (max-width: 800px) {
    justify-content: flex-end;
    flex-direction: row-reverse;
    h3 {
      margin-left: 0.5rem;
    }
  }
}

.preview {
  font-size: 12px;
  color: #395173;
  text-overflow: ellipsis;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: 768px) {
    display: -webkit-box;
  }
}

.descriptionTooltip {
  display: none;
  position: absolute;
  top: 120%;
  left: 0;
  background: #f2f8ff;
  border-radius: 4px;
  padding: 8px;
  width: 100%;
  z-index: 100;
  font-size: 14px;
  color: #192940;

  &::after {
    content: '';
    position: absolute;
    bottom: 100%;
    margin-left: -10px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent #ceddf2 transparent;
  }

  &.bear::after{
      left: 25%;
  }
  &.bull::after {
    left: 75%;
  }
}

// Full Display Component
.fullDisplayWrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 16px 0;

  @media (max-width: 1000px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  > div {
    display: flex;
    flex-direction: column;
  }

  h3 {
    font-size: 18px;
    font-weight: 700;
    color: #192940;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }

  p {
    font-size: 14px;
    color: black;
    margin-bottom: 0;
    padding: 1rem;
    background-color: #e1effe;
    border-radius: 4px;
  }
}
