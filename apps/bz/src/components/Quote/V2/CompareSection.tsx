'use client';
import React, { Suspense } from 'react';

import { useTranslation } from 'react-i18next';
import { faChevronDown, faChevronUp } from '@fortawesome/pro-regular-svg-icons';

import styled from '@benzinga/themetron';
import { Icon, SectionTitle } from '@benzinga/core-ui';
import Hooks from '@benzinga/hooks';
import i18n from '@benzinga/translate';

import { Thumb } from './OverviewRow';
import QuoteSlider from '../../Quote/QuoteSlider';
import { EP<PERSON><PERSON>, PriceBook<PERSON>hart, PriceEarningsChart, Ro<PERSON><PERSON>, <PERSON><PERSON>hart } from './CompareCharts/CompareCharts';

interface CompareData {
  de: number;
  eps: number;
  pb: number;
  pe: number;
  roe: number;
  rsi: number;
}

interface CompareSectionProps {
  compareData: CompareData;
  initialSymbols: string[];
  currentSymbol: string;
}

const CompareSection = ({ compareData, currentSymbol, initialSymbols }: CompareSectionProps) => {
  const { t } = useTranslation('quote', { i18n });
  const [symbols, setSymbols] = React.useState<string[]>(initialSymbols);

  const { width } = Hooks.useWindowSize();
  const isMobile = width < 768;

  const handleRemoveSymbol = React.useCallback((symbol: string) => {
    setSymbols(previousSymbols => previousSymbols.filter(previousSymbol => previousSymbol !== symbol));
  }, []);

  const handleAddSymbol = React.useCallback(
    (symbol: string) => {
      if (!symbols.includes(symbol)) {
        setSymbols(previousSymbols => [...previousSymbols, symbol]);
      }
    },
    [symbols],
  );

  const tabsData = React.useMemo(() => {
    return [
      {
        acceptedAverage: 0.5,
        description: t('Quote.Compare.earnings-per-share-description'),
        formatter: (value: number) => (value ? `$${value?.toFixed(2)}` : ''),
        inverse: true,
        label: 'EPS',
        renderChart: ({ symbols }) => (
          <EPSChart
            addSymbol={handleAddSymbol}
            currentSymbol={currentSymbol}
            removeSymbol={handleRemoveSymbol}
            symbols={symbols}
          />
        ),
        title: t('Quote.Compare.earnings-per-share'),
        value: compareData?.eps,
      },
      {
        acceptedAverage: 17,
        description: t('Quote.Compare.price-to-earnings-description'),
        formatter: (value: number) => (value ? `${value?.toFixed(1)}x` : ''),
        label: 'P/E',
        renderChart: ({ symbols }) => (
          <PriceEarningsChart
            addSymbol={handleAddSymbol}
            currentSymbol={currentSymbol}
            removeSymbol={handleRemoveSymbol}
            symbols={symbols}
          />
        ),
        title: t('Quote.Compare.price-to-earnings'),
        value: compareData?.pe,
      },
      {
        acceptedAverage: 1,
        description: t('Quote.Compare.price-to-book-description'),
        formatter: (value: number) => (value ? `${value?.toFixed(1)}x` : ''),
        label: 'P/B',
        renderChart: ({ symbols }) => (
          <PriceBookChart
            addSymbol={handleAddSymbol}
            currentSymbol={currentSymbol}
            removeSymbol={handleRemoveSymbol}
            symbols={symbols}
          />
        ),
        title: t('Quote.Compare.price-to-book'),
        value: compareData?.pb, // This is so that the lower the value, the better
      },
      {
        acceptedAverage: 0.15,
        description: t('Quote.Compare.return-on-equity-description'),
        formatter: (value: number) => (value ? `${value?.toFixed(1)}%` : ''),
        inverse: true,
        label: 'ROE',
        renderChart: ({ symbols }) => (
          <RoEChart
            addSymbol={handleAddSymbol}
            currentSymbol={currentSymbol}
            removeSymbol={handleRemoveSymbol}
            symbols={symbols}
          />
        ),
        title: t('Quote.Compare.return-on-equity'),
        value: compareData?.roe * 100,
      },
      {
        acceptedAverage: 2,
        description: t('Quote.Compare.debt-to-equity-description'),
        formatter: (value: number) => (value ? `${value?.toFixed(1)}x` : ''),
        label: 'D/E',
        renderChart: ({ symbols }) => (
          <DEChart
            addSymbol={handleAddSymbol}
            currentSymbol={currentSymbol}
            removeSymbol={handleRemoveSymbol}
            symbols={symbols}
          />
        ),
        title: t('Quote.Compare.debt-to-equity'),
        value: compareData?.de,
      },
      // {
      //   acceptedAverage: 50,
      //   description: `The relative strength index (RSI) is a momentum indicator used in technical analysis that measures the magnitude of recent price changes to evaluate overbought or oversold conditions in the price of a stock or other asset.`,
      //   formatter: (value: number) => `${value.toFixed(0)}`,
      //   label: 'RSI',
      //   title: 'Relative Strength Index',
      //   value: compareData?.rsi,
      // },
    ];
  }, [
    t,
    compareData?.eps,
    compareData?.pe,
    compareData?.pb,
    compareData?.roe,
    compareData?.de,
    handleAddSymbol,
    currentSymbol,
    handleRemoveSymbol,
  ]);

  const [activeTab, setActiveTab] = React.useState(0);
  const [activeTabItem, setActiveTabItem] = React.useState(tabsData[0]);

  React.useEffect(() => {
    if (isMobile) return;
    if (activeTabItem) return;
    setActiveTab(0);
    setActiveTabItem(tabsData[0]);
  }, [isMobile, activeTabItem, tabsData]);

  const handleTabClick = (index: number) => {
    if (isMobile && activeTab === index) {
      setActiveTab(0);
      setActiveTabItem(tabsData[0]);
      return;
    }
    setActiveTab(index);
    setActiveTabItem(tabsData[index]);
  };

  const showGreenChip = activeTabItem?.inverse
    ? activeTabItem?.acceptedAverage < activeTabItem?.value
    : activeTabItem?.acceptedAverage > activeTabItem?.value;

  return (
    <>
      <div className="flex gap-2 items-center mb-4">
        <SectionTitle level={3}>{t('Quote.Compare.compare')}</SectionTitle>
      </div>
      <CompareSectionWrapper>
        <div className="flex flex-col gap-8 md:gap-4 w-full md:hidden">
          {tabsData.map((tab, index) => (
            <div key={index}>
              <button
                className={`compare-tab ${activeTab === index ? 'active' : ''}`}
                onClick={() => handleTabClick(index)}
              >
                <div className="flex items-center w-full justify-between">
                  <div className="flex items-center gap-1">
                    <Thumb
                      down={tab.inverse ? tab.acceptedAverage > tab.value : tab.acceptedAverage < tab.value}
                      up={tab.inverse ? tab.acceptedAverage < tab.value : tab.acceptedAverage > tab.value}
                    />
                    <div className="compare-tab__label font-bold">{tab.label}</div>
                    <div className={`compare-tab__value ${tab.inverse ? 'inverse' : ''}`}>
                      {tab.formatter(tab.value)}
                    </div>
                  </div>
                  {activeTab === index ? <Icon icon={faChevronUp} /> : <Icon icon={faChevronDown} />}
                </div>
                <QuoteSlider
                  currentPrice={tab.acceptedAverage}
                  high={tab.acceptedAverage * 2}
                  inverse={tab.inverse}
                  low={tab.acceptedAverage / 2}
                  open={tab.value}
                />
              </button>
              {tab.label === activeTabItem.label && (
                <div className="tab-content w-full">
                  <div className="flex flex-col items-start w-full gap-2">
                    <div className="flex items-center gap-2">
                      <div className={`rounded-lg p-2 ${showGreenChip ? 'green-chip' : 'red-chip'}`}>
                        {/* Pass a green / red text color here dependenting. */}
                        <div className="text-xs whitespace-nowrap label-text">
                          {activeTabItem?.label} {showGreenChip ? t('Quote.Compare.better') : t('Quote.Compare.worse')}{' '}
                          {t('Quote.Compare.than-industry')}
                        </div>
                      </div>
                      <Thumb
                        down={
                          activeTabItem?.inverse
                            ? activeTabItem?.acceptedAverage > activeTabItem?.value
                            : activeTabItem?.acceptedAverage < activeTabItem?.value
                        }
                        up={
                          activeTabItem?.inverse
                            ? activeTabItem?.acceptedAverage < activeTabItem?.value
                            : activeTabItem?.acceptedAverage > activeTabItem?.value
                        }
                      />
                    </div>
                    <div>
                      <div className="font-bold text-lg text-gray-600 mb-2 tab-title">{activeTabItem?.title}</div>
                      <div className="text-gray-600	text-md tab-description">{activeTabItem?.description}</div>
                    </div>
                  </div>
                  {/* If the activeTabItems has a chart, let's display it */}
                  <Suspense fallback={<div />}>
                    {activeTabItem?.renderChart && activeTabItem.renderChart({ symbols })}
                  </Suspense>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="hidden md:block">
          <div className="tabs-row flex items-center gap-4 wrap">
            {tabsData.map((tab, index) => (
              <button
                className={`compare-tab ${activeTab === index ? 'active' : ''}`}
                key={index}
                onClick={() => handleTabClick(index)}
              >
                <div className="flex items-center w-full justify-between">
                  <div className="flex items-center gap-1">
                    <div className="compare-tab__label font-bold">{tab.label}</div>
                    <div className={`compare-tab__value ${tab.inverse ? 'inverse' : ''}`}>
                      {tab.formatter(tab.value)}
                    </div>
                  </div>
                  <Thumb
                    down={tab.inverse ? tab.acceptedAverage > tab.value : tab.acceptedAverage < tab.value}
                    up={tab.inverse ? tab.acceptedAverage < tab.value : tab.acceptedAverage > tab.value}
                  />
                </div>
                <QuoteSlider
                  currentPrice={tab.acceptedAverage}
                  high={tab.acceptedAverage * 2}
                  inverse={tab.inverse}
                  low={tab.acceptedAverage / 2}
                  open={tab.value}
                />
              </button>
            ))}
          </div>
          <div className="tab-content w-full">
            <div className="flex items-center justify-between w-full">
              <div>
                <div className="font-bold text-lg text-gray-600 mb-2 tab-title">{activeTabItem?.title}</div>
                <div className="text-gray-600	text-md tab-description">{activeTabItem?.description}</div>
              </div>
              {/* We'll show a 'chip' of a sort with a label that says 'x higher / lower than industry' */}
              <div className="flex items-center gap-2">
                <div className={`rounded p-2 ${showGreenChip ? 'green-chip' : 'red-chip'}`}>
                  {/* Pass a green / red text color here dependenting. */}
                  <div className="text-xs whitespace-nowrap label-text">
                    {activeTabItem?.label} {showGreenChip ? t('Quote.Compare.better') : t('Quote.Compare.worse')}{' '}
                    {t('Quote.Compare.than-industry')}
                  </div>
                </div>
                <Thumb
                  down={
                    activeTabItem?.inverse
                      ? activeTabItem?.acceptedAverage > activeTabItem?.value
                      : activeTabItem?.acceptedAverage < activeTabItem?.value
                  }
                  up={
                    activeTabItem?.inverse
                      ? activeTabItem?.acceptedAverage < activeTabItem?.value
                      : activeTabItem?.acceptedAverage > activeTabItem?.value
                  }
                />
              </div>
            </div>
            {/* If the activeTabItems has a chart, let's display it */}
            <Suspense fallback={<div />}>
              {activeTabItem?.renderChart && activeTabItem.renderChart({ symbols })}
            </Suspense>
          </div>
        </div>
      </CompareSectionWrapper>
    </>
  );
};

const CompareSectionWrapper = styled.div`
  .chart-body {
    border: 1px solid ${({ theme }) => theme.colors.border};
    background: ${({ theme }) => theme.colorPalette.white};
  }

  .symbol-menu {
    background: ${({ theme }) => theme.colorPalette.white};

    .searchbar {
      border-bottom: 1px solid ${({ theme }) => theme.colors.border};
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;

      .inner-searchbar {
        border: none;
        background: transparent;

        &:focus {
          outline: none;
        }
      }
    }

    .symbol-entry {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-left: 0.25rem;
      padding: 0.5rem 1rem;

      .symbol-pill-dot {
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
      }

      .symbol-pill {
        display: flex;
        padding: 2px 4px;
        align-items: flex-start;
        gap: 8px;
        border-radius: 4px;
        background: ${({ theme }) => theme.colorPalette.gray50};
        border: 1px solid ${({ theme }) => theme.colorPalette.gray200};
      }
    }
  }

  .green-chip {
    background: #d3efe5;

    .label-text {
      color: #30bf60;
    }
  }

  .red-chip {
    background: #f4dae3;

    .label-text {
      color: #eb5757;
    }
  }

  .compare-tab {
    cursor: pointer;
    @media (min-width: 768px) {
      max-width: 160px;
    }
    width: 100%;
    background: ${({ theme }) => theme.colorPalette.gray50};
    display: flex;
    height: 72px;
    padding: 12px 16px;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;

    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid ${({ theme }) => theme.colorPalette.gray200};

    &.active {
      background: ${({ theme }) => theme.colorPalette.gray50};
      border-bottom: none;
    }

    .compare-tab__label {
      font-size: 16px;
      font-weight: 700;
      color: #4f4f4f;
      line-height: normal;
    }

    .compare-tab__value {
      font-size: 12px;
      color: #828282;
      font-weight: 700;
      line-height: normal;
    }
  }

  .tab-content {
    background: linear-gradient(
      180deg,
      ${({ theme }) => theme.colorPalette.gray50} 100%,
      ${({ theme }) => theme.colorPalette.gray50} 0%
    );
    border: 1px solid ${({ theme }) => theme.colorPalette.gray200};
    padding: 16px;

    margin-top: -1px;
  }
`;

export default CompareSection;
