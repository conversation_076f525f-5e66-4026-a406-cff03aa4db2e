'use client';
import React, { Suspense } from 'react';
import { DateTime } from 'luxon';

import styled from '@benzinga/themetron';
import { numberShorthand } from '@benzinga/utils';
import { AsOf, SecuritiesManager } from '@benzinga/securities-manager';
import { SessionContext } from '@benzinga/session-context';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';
import { EChartsOption } from 'echarts';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

interface FinancialsSectionProps {
  symbol: string;
}

type MappedPerformanceData = {
  date: string;
  earnings: number;
  ebit: number;
  ebitda: number;
  grossProfit: number;
  totalRevenue: number;
};

export const FinancialsSection: React.FC<FinancialsSectionProps> = ({ symbol }) => {
  const { t } = useTranslation('quote', { i18n });
  const [dataPeriod, setDataPeriod] = React.useState<'quarterly' | 'annual'>('quarterly');
  const [dataLoaded, setDataLoaded] = React.useState(false);
  const [epsData, setEpsData] = React.useState<MappedPerformanceData[]>([]);

  const session = React.useContext(SessionContext);
  const securitiesManager = session.getManager(SecuritiesManager);

  const handleDataPeriodChange = (period: 'quarterly' | 'annual') => {
    setDataPeriod(period);
    setDataLoaded(false);
  };

  React.useEffect(() => {
    let fourPeriodsAgo: AsOf = DateTime.local().minus({ years: 4 }).toFormat('yyyy-MM-dd') as AsOf;
    if (dataPeriod === 'quarterly') {
      fourPeriodsAgo = DateTime.local().minus({ months: 13 }).toFormat('yyyy-MM-dd') as AsOf;
    }
    // TODO: Fetch this server-side
    async function getOperationRatios() {
      const operationRatios = await securitiesManager.getFinancials({
        asOf: fourPeriodsAgo,
        period: dataPeriod === 'quarterly' ? '3M' : '12M',
        symbols: symbol,
      });

      if (operationRatios.ok) {
        setDataLoaded(true);
        const ratios = operationRatios?.ok?.[0]?.financials;

        // Make a list of the data, but do not add it to the chartData if it cannot get a date or value.
        let data: {
          date: string;
          earnings: number;
          ebit: number;
          ebitda: number;
          grossProfit: number;
          totalRevenue: number;
        }[] = [];

        ratios?.forEach(ratio => {
          if (ratio.asOf && ratio?.incomeStatement?.netIncome) {
            const date = ratio.asOf;
            const value = ratio.incomeStatement;
            data.push({
              date,
              earnings: value?.netIncome,
              ebit: value?.ebit,
              ebitda: value?.ebitda,
              grossProfit: value?.grossProfit,
              totalRevenue: value?.totalRevenue,
            });
          }
        });

        // Sort data by date
        data = data.sort((a, b) => {
          const aDate = DateTime.fromISO(a.date);
          const bDate = DateTime.fromISO(b.date);
          return aDate > bDate ? 1 : -1;
        });
        setEpsData(data);
      }
    }
    if (!dataLoaded) {
      getOperationRatios();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [securitiesManager, dataPeriod]);

  const topChartOptions = React.useMemo(() => {
    return {
      legend: {
        bottom: 'left',
        data: [
          t('Quote.Financials.revenue'),
          t('Quote.Financials.earnings'),
          `${t('Quote.Financials.profit-margin')} %`,
        ],
        left: 50,
      },
      series: [
        {
          data: epsData.map(data => {
            return data.totalRevenue;
          }),
          itemStyle: {
            color: '#30BF60',
          },
          name: t('Quote.Financials.revenue'),
          tooltip: {
            valueFormatter: function (value) {
              return `$${numberShorthand(value)}`;
            },
          },
          type: 'bar',
        },
        {
          data: epsData.map(data => {
            return data.earnings;
          }),
          itemStyle: {
            color: '#2f80ed',
          },
          name: t('Quote.Financials.earnings'),
          tooltip: {
            valueFormatter: function (value) {
              return `$${numberShorthand(value)}`;
            },
          },
          type: 'bar',
        },
        {
          // We will calculate the earnings / totalRevenue * 100 to get the profit margin %
          data: epsData.map(data => {
            return Math.round(((data.earnings / data.totalRevenue) * 100 + Number.EPSILON) * 100) / 100;
          }),
          itemStyle: {
            color: '#f2994a',
          },
          name: `${t('Quote.Financials.profit-margin')} %`,
          tooltip: {
            valueFormatter: function (value) {
              return value + '%';
            },
          },
          type: 'line',
          yAxisIndex: 1,
        },
      ],
      tooltip: {
        axisPointer: {
          crossStyle: {
            color: '#999',
          },
          type: 'cross',
        },
        trigger: 'axis',
      },
      xAxis: [
        {
          axisPointer: {
            type: 'shadow',
          },
          data: epsData.map(data =>
            DateTime.fromFormat(data.date, 'yyyy-MM-dd').setLocale(i18n.language).toFormat('LLL yy'),
          ),
          type: 'category',
        },
      ],
      yAxis: [
        {
          axisLabel: {
            formatter: function (value) {
              return `$${numberShorthand(value, 0)}`;
            },
          },
          // interval: 50,
          // max: 250,
          // min: 0,
          type: 'value',
        },
        {
          axisLabel: {
            interval: '0',
            textStyle: {
              color: '#fff',
            },
          },
          max: 100,
          min: 0,
          name: 'right_yaxis',
          nameTextStyle: {
            color: '#fff',
          },
          splitLine: {
            show: false,
          },
          splitNumber: 5,
          type: 'value',
        },
      ],
    };
  }, [epsData, t]);

  // Let's sort the profile?.ratings by month.
  // We want a dict like 'Jan': [ratings], 'Feb': [ratings], etc.

  const calculatePercentChange = (current: number, previous: number) => {
    return `${previous ? Math.round((((current - previous) / previous) * 100 + Number.EPSILON) * 100) / 100 : '--'}%`;
  };

  return (
    <FinancialsSectionWrapper>
      {/* This will be the chart + table */}
      <div className="card">
        <div className="flex justify-between items-center">
          <div className="simple-tab-wrapper" />
          <div className="simple-tab-wrapper">
            <div
              className={`simple-tab ${dataPeriod === 'quarterly' ? 'active' : ''}`}
              onClick={() => handleDataPeriodChange('quarterly')}
            >
              <div className="simple-tab-text">{t('Date.Periods.quarterly')}</div>
            </div>
            <div
              className={`simple-tab ${dataPeriod === 'annual' ? 'active' : ''}`}
              onClick={() => handleDataPeriodChange('annual')}
            >
              <div className="simple-tab-text">{t('Date.Periods.annual')}</div>
            </div>
          </div>
        </div>
        {/* Chart goes here */}
        <Suspense fallback={<div />}>
          <ReactECharts
            className="chart-body mt-4 z-[1]"
            key="performance"
            notMerge={true}
            option={topChartOptions as EChartsOption}
            style={{ height: '325px', overflow: 'hidden', width: '100%' }}
          />
        </Suspense>
        <div className="divider my-4"></div>
        <table className="w-full">
          <thead>
            <tr className="table-row">
              <th className="table-title">{t('Table.name')}</th>
              {epsData.map(data => {
                const date = DateTime.fromISO(data.date);
                const year = date.toFormat('yyyy');
                const month = date.setLocale(i18n.language).toFormat('LLL');
                return (
                  <th className="table-title" key={data.date}>
                    {dataPeriod === 'annual' ? year : `${month} '${year.substring(2)}`}
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody className="mt-2">
            <tr>
              <td>
                <div className="mt-2 divider"></div>
              </td>
            </tr>
            <tr className="table-row body-row">
              <td className="table-cell">
                <div className="table-cell-title">{t('Quote.Financials.revenue')}</div>
                <div className="table-cell-subtitle">{t('Quote.Financials.change')}</div>
              </td>

              {epsData.map(data => {
                return (
                  <td className="table-cell" key={data.date}>
                    <div className="table-cell-title">{numberShorthand(data.totalRevenue, 2)}</div>
                    <div className="table-cell-subtitle">
                      {calculatePercentChange(data.totalRevenue, epsData[epsData?.indexOf(data) - 1]?.totalRevenue)}
                    </div>
                  </td>
                );
              })}
            </tr>
            <tr className="table-row body-row">
              <td className="table-cell">
                <div className="table-cell-title">{t('Quote.Financials.gross')}</div>
                <div className="table-cell-subtitle">{t('Quote.Financials.change')}</div>
              </td>
              {epsData.map(data => {
                return (
                  <td className="table-cell" key={`gross-profit-table-cell-${data.date}`}>
                    <div className="table-cell-title">{numberShorthand(data.grossProfit, 2)}</div>
                    <div className="table-cell-subtitle">
                      {calculatePercentChange(data.grossProfit, epsData[epsData?.indexOf(data) - 1]?.grossProfit)}
                    </div>
                  </td>
                );
              })}
            </tr>
            <tr className="table-row body-row">
              <td className="table-cell">
                <div className="table-cell-title">EBITDA</div>
                <div className="table-cell-subtitle">{t('Quote.Financials.change')}</div>
              </td>
              {epsData.map(data => {
                return (
                  <td className="table-cell" key={`ebitda-table-cell-${data.date}`}>
                    <div className="table-cell-title">{numberShorthand(data.ebitda, 2)}</div>
                    <div className="table-cell-subtitle">
                      {calculatePercentChange(data.ebitda, epsData[epsData?.indexOf(data) - 1]?.ebitda)}
                    </div>
                  </td>
                );
              })}
            </tr>
            <tr className="table-row body-row">
              <td className="table-cell">
                <div className="table-cell-title">EBIT</div>
                <div className="table-cell-subtitle">{t('Quote.Financials.change')}</div>
              </td>
              {epsData.map(data => {
                return (
                  <td className="table-cell" key={`ebit-table-cell-${data.date}`}>
                    <div className="table-cell-title">{numberShorthand(data.ebit, 2)}</div>
                    <div className="table-cell-subtitle">
                      {calculatePercentChange(data.ebit, epsData[epsData?.indexOf(data) - 1]?.ebit)}
                    </div>
                  </td>
                );
              })}
            </tr>
            <tr className="table-row body-row">
              <td className="table-cell">
                <div className="table-cell-title">{t('Quote.Financials.earnings')}</div>
                <div className="table-cell-subtitle">{t('Quote.Financials.change')}</div>
              </td>
              {epsData.map(data => {
                return (
                  <td className="table-cell" key={`earnings-table-cell-${data.date}`}>
                    <div className="table-cell-title">{numberShorthand(data.earnings, 2)}</div>
                    <div className="table-cell-subtitle">
                      {calculatePercentChange(data.earnings, epsData[epsData?.indexOf(data) - 1]?.earnings)}
                    </div>
                  </td>
                );
              })}
            </tr>
          </tbody>
        </table>
      </div>
    </FinancialsSectionWrapper>
  );
};

const FinancialsSectionWrapper = styled.div`
  .small-gray {
    font-size: 12px;
    color: #828282;
    font-weight: 400;
  }

  .title-gray {
  }

  .financials-right-spacer {
    margin-top: 56px;
    position: relative;
  }

  .divider {
    height: 1px;
    width: 100%;
    background: #f1f1f1;
    padding: 0px 16px;
  }

  .body-row {
    border-bottom: 1px solid #f1f1f1;
    padding: 8px 0px;

    &:last-child {
      border-bottom: none;
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 1.5fr repeat(4, 1fr);

    .table-title {
      font-size: 12px;
      color: #828282;
      line-height: 15px;
      font-weight: 400;
      text-align: left;
    }

    .table-cell {
      display: flex;
      flex-direction: column;
      padding: 2px 4px;

      .table-cell-title {
        font-size: 14px;
        font-weight: 700;
        line-height: 17px;

        @media (max-width: 768px) {
          font-size: 12px;
          line-height: 14px;
          font-weight: 600;
        }
      }

      .table-cell-subtitle {
        font-size: 12px;
        line-height: 14px;
        color: #333333;

        @media (max-width: 768px) {
          font-size: 10px;
          line-height: 12px;
        }
      }
    }
  }
`;

export default FinancialsSection;
