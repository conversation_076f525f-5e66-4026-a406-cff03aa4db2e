import React from 'react';
import styled from '@benzinga/themetron';
import { Button } from '@benzinga/core-ui';
import { News, StoryObject } from '@benzinga/basic-news-manager';
import { SimpleNewsQueryAndOptions } from '@benzinga/internal-news-manager';
import { ContentFeed, NewsListItemElement } from '@benzinga/news';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';

export interface QuoteNewsFeedProps {
  nodes: (StoryObject | News)[];
  adUnit?: React.ReactNode;
  query?: SimpleNewsQueryAndOptions;
  seeMoreEnabled?: boolean;
  seeMoreLabel?: string;
  dynamicReachContent?: boolean;
}

export const QuoteNewsFeed: React.FC<QuoteNewsFeedProps> = ({
  adUnit,
  nodes,
  query = {},
  seeMoreEnabled = false,
  seeMoreLabel = 'See More',
}) => {
  const { t } = useTranslation('quote', { i18n });

  return (
    <QuoteNewsFeedWrapper className="flex flex-col">
      <div className="quote-news-list">
        <ContentFeed
          adPlacements={[
            {
              index: 2,
              placement_id: 'quote-news-feed-1',
            },
          ]}
          adUnit={adUnit}
          isInfinite={false}
          limit={5}
          loadMore={seeMoreEnabled}
          loadMoreElement={(isLoading, handleLoadMore) => {
            if (!seeMoreEnabled) return <></>;
            if (!nodes.length) return <></>;
            return (
              <div className="see-more-button-wrapper flex justify-center items-center relative mt-8">
                <Button
                  className="show-more-button w-full md:w-auto uppercase z-[2] absolute"
                  isLoading={isLoading}
                  onClick={handleLoadMore}
                  variant="flat-light-blue-outlined"
                >
                  {seeMoreLabel}
                </Button>
                <div className="hidden md:flex relative items-center justify-center w-full">
                  <hr className="absolute h-px w-full bg-[#ceddf2] border-0 p-0" />
                  <span className="relative bg-white px-2" />
                </div>
              </div>
            );
          }}
          newsItemElement={(node, index) => {
            return (
              <div
                className="quote-news-item bg-[#F2F8FF66] border border-bzblue-400 rounded p-4"
                key={`quote-news-item-${node.id}-${index}`}
              >
                <NewsListItemElement
                  dateType="med"
                  locale={i18n.language}
                  node={node as StoryObject}
                  showAuthor={true}
                  showTeaser={true}
                  showTicker={false}
                  showTime={false}
                />
              </div>
            );
          }}
          nodes={nodes as News[]}
          noResultsElement={<div>{t('Quote.News.no-news-found')}</div>}
          query={query}
          showSponsoredContent={false}
        />
      </div>
    </QuoteNewsFeedWrapper>
  );
};

const QuoteNewsFeedWrapper = styled.div`
  .content-feed-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .see-more-button-wrapper {
    .show-more-button {
      padding: 0.5rem 2rem;
    }
  }

  .content-feed-no-results {
    height: 200px;
  }

  .quote-news-item {
    display: flex;

    .article-title {
      -webkit-line-clamp: 3;
    }

    .article-teaser {
      color: ${({ theme }) => theme.colorPalette.gray600};
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
    }

    .article-author {
      color: ${({ theme }) => theme.colorPalette.gray600};
    }

    .article-datetime {
      color: ${({ theme }) => theme.colorPalette.gray400};
    }

    .news-block {
      all: unset;
    }
  }
`;
