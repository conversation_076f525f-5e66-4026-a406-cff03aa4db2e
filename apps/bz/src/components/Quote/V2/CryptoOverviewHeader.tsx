import React from 'react';
import { QuoteProfile } from '../../../entities/quoteEntity';
import { EdgeRanking } from '@benzinga/ticker-ui';
import { RankingDetail } from '@benzinga/quotes-manager';
import { Tooltip, Icon } from '@benzinga/core-ui';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';

interface CryptoOverviewHeaderProps {
  profile: QuoteProfile;
  rankingData?: RankingDetail | null;
}

export const CryptoOverviewHeader = ({ profile, rankingData }: CryptoOverviewHeaderProps) => {
  return (
    <div className="flex gap-4 flex-col lg:flex-row">
      {rankingData && (
        <div className="flex flex-1 flex-col">
          <div className="flex flex-row gap-2 pb-2">
            <h6 className="uppercase pb-0">Edge Rankings</h6>
            <Tooltip
              content={
                'Benzinga Edge stock rankings give you four critical scores to help you identify the strongest and weakest stocks to buy and sell.'
              }
              width={250}
            >
              <Icon
                className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
                icon={faInfoCircle}
              />
            </Tooltip>
          </div>
          <EdgeRanking
            className="w-full"
            {...rankingData}
            adType="quotepage"
            layout="crypto_etf"
            symbol={profile?.symbol}
            variant="dark"
          />
        </div>
      )}
    </div>
  );
};

export default CryptoOverviewHeader;
