'use client';
import React from 'react';
import { <PERSON>ton, <PERSON>Title, Tabs, TabsInterface } from '@benzinga/core-ui';
import { QuoteNewsFeed } from './QuoteNewsFeed';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { SimpleNewsQueryAndOptions } from '@benzinga/internal-news-manager';
import { QuoteProfile } from '../../../../src/entities/quoteEntity';
import styled from '@benzinga/themetron';
import { News } from '@benzinga/basic-news-manager';
import LazyLoad from 'react-lazyload';
import { TaboolaPlacement } from '@benzinga/ads';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';

export interface QuoteNewsSectionProps {
  belowNewsSection?: React.ReactNode;
  profile: QuoteProfile;
  isETF?: boolean;
}

const TaboolaQuotePlacement = () => (
  <LazyLoad offset={100} once>
    <TaboolaPlacement
      container="taboola-mid-page-thumbnails---quotes"
      mode="thumbnails-mq"
      placement="Mid-Page Thumbnails - Quotes"
      settings={{
        other: 'auto',
      }}
      url=""
      vizSensor={false}
    />
  </LazyLoad>
);

const QuoteNewsSection = ({ belowNewsSection, profile }: QuoteNewsSectionProps) => {
  const { t } = useTranslation('quote', { i18n });
  const [newsTab, setNewsTab] = React.useState('news');

  const handleNewsTabChange = (tab: string) => {
    setNewsTab(tab);
  };

  const createTabComponent = (
    nodes: (StoryObject | News)[],
    query?: SimpleNewsQueryAndOptions,
    seeMoreEnabled?: boolean,
    adUnit?: React.ReactNode,
  ) => (
    <QuoteNewsFeed
      adUnit={adUnit}
      nodes={nodes}
      query={query}
      seeMoreEnabled={seeMoreEnabled}
      seeMoreLabel={t('Buttons.see-more')}
    />
  );

  const tabs: TabsInterface[] = [
    {
      component: createTabComponent(
        profile?.initialRecentNews,
        profile.recentNewsQuery,
        profile?.initialRecentNews?.length === 3,
        <TaboolaQuotePlacement />,
      ),
      key: 'news',
      name: t('Quote.News.news'),
    },
    // {
    //   component: createTabComponent(profile?.partnersRelease?.sortedContent ?? []),
    //   key: 'partners',
    //   name: t('Quote.News.partners'),
    // },
    {
      component: (
        <>
          {createTabComponent(profile?.pressRelease?.sortedContent ?? [])}
          <Button
            as="a"
            className="font-bold inline-block w-full mt-4"
            href="https://pro.benzinga.com/?utm_source=benzinga.com&utm_campaign=quote-press-release"
            target="_blank"
            variant="flat-blue"
          >
            {t('Quote.News.press-release-full-archive')}
          </Button>
        </>
      ),
      key: 'press_release',
      name: t('Quote.News.press-release'),
    },
  ];

  return (
    <Container className="news-tab-and-ads-wrapper w-full flex flex-col lg:flex-row gap-8 md:gap-4">
      <StyledTabsWrapper className="w-full">
        <Tabs
          activeTab={newsTab}
          enableLazyLoad={true}
          isShowMoreButtonEnabled={false}
          leftSideElement={
            <div className="flex items-center whitespace-nowrap mr-4 recent-news-title">
              <SectionTitle className="leading-none" level={3}>
                {t('Quote.News.recent-news')}
              </SectionTitle>
            </div>
          }
          onChange={handleNewsTabChange}
          tabs={tabs}
          variant="pill"
        />
        {belowNewsSection}
      </StyledTabsWrapper>
    </Container>
  );
};

export default QuoteNewsSection;

const Container = styled.div``;

const StyledTabsWrapper = styled.div`
  .tabs-container {
    .tab-list-wrapper {
      display: flex;
      flex-direction: column;
      position: relative;
      width: 100%;

      .recent-news-title {
        margin-bottom: 1rem;
      }
    }

    @media screen and (min-width: 550px) {
      .tab-list-wrapper {
        flex-direction: row;

        .recent-news-title {
          margin-bottom: unset;
        }
      }
    }

    .tab-list {
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
      color: ${({ theme }) => theme.colorPalette.blue500};
      max-width: 540px;
      width: 100%;
      margin-left: auto;

      .tab {
        flex: 1;
        //max-width: 200px;
      }
    }

    .tab-content {
      padding: 1rem 0;
    }
  }
`;
