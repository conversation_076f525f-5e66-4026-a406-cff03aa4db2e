'use client';
import { QuoteProfile } from '../../../entities/quoteEntity';
import React from 'react';
import styled from '@benzinga/themetron';
import { truncate } from '@benzinga/utils';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';

interface AboutQuote {
  profile: QuoteProfile;
}

const AboutQuote = ({ profile }: AboutQuote) => {
  const { t } = useTranslation('quote', { i18n });
  const [shortenedDescription, setShortenedDescription] = React.useState(true);

  const handleToggleShowMore = () => setShortenedDescription(!shortenedDescription);

  const companyDescriptionToUse = React.useMemo(() => {
    return profile?.tickerDetails?.company?.longDescription || '';
  }, [profile?.tickerDetails?.company?.longDescription]);

  return (
    <AboutQuoteWrapper className="w-full">
      <div className="card financials-right-spacer overflow-hidden">
        <div className="card-title font-bold">
          {t('Quote.About.about', { company: profile?.richQuoteData?.issuerName })}
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="flex flex-col">
            <div className="title-gray">{t('Quote.About.sector')}</div>
            <div className="value-black font-bold">
              {profile?.richQuoteData.sector?.length
                ? profile?.richQuoteData?.sector
                : profile.fundamentals?.assetClassification?.msSectorName || t('Fields.n-a')}
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="flex flex-col">
            <div className="title-gray">{t('Quote.About.industry')}</div>
            <div className="value-black font-bold">{profile?.richQuoteData?.industry || t('Fields.n-a')}</div>
          </div>
        </div>

        {companyDescriptionToUse?.length > 0 && (
          <div className="title-gray mt-2 mb-8">
            {shortenedDescription ? `${truncate(companyDescriptionToUse, 100)}` : companyDescriptionToUse}
          </div>
        )}

        {companyDescriptionToUse?.length > 100 && (
          <div className="show-more-about font-bold" onClick={handleToggleShowMore}>
            {shortenedDescription ? t('Buttons.show-more') : t('Buttons.show-less')}
          </div>
        )}
      </div>

      {/* Team Card */}
      {/* {isAdvertiser && (
        <div className="team-card mt-4 border border-bzblue-400 rounded overflow-hidden">
          <div className="team-header flex items-center">
            <div className="team-header-title">{t('Quote.About.team')}</div>
            <div className="negative-tab-wrapper team-header-tabs">
              <div
                className={`negative-tab ${teamMode === 'executives' ? 'active' : ''}`}
                onClick={() => handleTeamModeChange('executives')}
              >
                <div className="simple-tab-text">{t('Quote.About.executives')}</div>
              </div>
              <div
                className={`negative-tab ${teamMode === 'board' ? 'active' : ''}`}
                onClick={() => handleTeamModeChange('board')}
              >
                <div className="simple-tab-text">{t('Quote.About.board')}</div>
              </div>
            </div>
          </div>
          {teamMode === 'executives' && (
            <div className="team-body">
              {advertiserProfile?.info?.[0]?.value?.slice(0, 5)?.map((member, index) => (
                <div className="team-member" key={index}>
                  <div className="team-member-image">
                    <Icon icon={faUser} />
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <div className="team-member-name">{member?.name}</div>
                    <div className="team-member-title">{member?.title}</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {teamMode === 'board' && (
            <div className="team-body">
              {advertiserProfile?.info?.[1]?.value?.slice(0, 5)?.map((member, index) => (
                <div className="team-member" key={index}>
                  <div className="team-member-image">
                    <Icon icon={faUser} />
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <div className="team-member-name">{member?.name}</div>
                    <div className="team-member-title">{member?.title}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
          <div className="flex items-center justify-between team-footer">
            <div className="font-bold">{t('Quote.About.other-members')}</div>
            <div className="font-bold">
              {teamMode === 'executives'
                ? advertiserProfile?.info?.[0]?.value?.length - 5
                : advertiserProfile?.info?.[1]?.value?.length - 5}
              +
            </div>
          </div>
        </div>
      )} */}

      {/* Contact Card */}
    </AboutQuoteWrapper>
  );
};

export default AboutQuote;

const AboutQuoteWrapper = styled.div`
  &.top-spacer {
    margin-top: 56px;
  }

  .financials-right-spacer {
    position: relative;
  }

  .show-more-about {
    cursor: pointer;
    background: #ecf3ff;
    position: absolute;
    bottom: 0px;
    display: flex;
    font-weight: 600;
    margin-left: -16px;
    padding: 8px 0px;
    width: 100%;
    justify-content: center;
    align-items: center;
    color: #3f83f8;
    text-transform: uppercase;
  }

  .team-body {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    padding: 12px;
  }

  .team-member {
    display: flex;
    align-items: center;

    .team-member-image {
      border: 1px solid #f2f2f2;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .team-member-name {
      font-size: 14px;
      font-weight: 600;
      line-height: 17px;
      color: #333333;
    }

    .team-member-title {
      font-size: 12px;
      line-height: 14px;
      font-weight: 400;
      color: #5b7292;
    }
  }

  .team-footer {
    background: ${({ theme }) => theme.colorPalette.blue50};
    padding: 8px 16px;

    div {
      font-size: 12px;
      line-height: 14px;
      color: #5b7292;
    }
  }

  .team-header {
    background: ${({ theme }) => theme.colorPalette.blue50};
    padding: 8px 16px;

    .team-header-title {
      font-size: 14px;
      font-weight: 500;
      line-height: 17px;
      color: #5b7292;
      margin-right: 32px;
    }
  }

  .negative-tab-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 4px;
    padding: 2px;
    width: 100%;

    .negative-tab {
      flex: 1;
      text-align: center;
      cursor: pointer;
      padding: 8px 16px;
      border-radius: 4px;
      &.active {
        background: #ffffff;
      }

      .simple-tab-text {
        font-weight: 600;
      }
    }
  }
`;
