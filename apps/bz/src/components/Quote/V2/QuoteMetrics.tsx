import classNames from 'classnames';
import { numberShorthand } from '@benzinga/utils';
import { sortByRecentDate } from '../../../../pages/api/quote/[ticker]/short-interest';
import { QuoteProfile } from '../../../entities/quoteEntity';
import { Thumb } from './OverviewRow';
import i18n from '@benzinga/translate';
import { useTranslation } from 'react-i18next';

export interface QuoteMetricRowProps {
  label: string;
  value: string | number | JSX.Element;
  className?: string;
}

export const QuoteMetricRow = ({ className, label, value }: QuoteMetricRowProps) => {
  return (
    <tr
      className={classNames('flex items-center justify-between w-full py-[0.4rem]', {
        [`${className}`]: !!className,
      })}
    >
      <td className="label text-gray-500">{label}</td>
      <td className="value font-bold">{value}</td>
    </tr>
  );
};

export const QuoteMetrics = ({ profile }: { profile: QuoteProfile }) => {
  const { t } = useTranslation('quote', { i18n });
  const daysToCover = Array.isArray(profile?.shortInterest)
    ? sortByRecentDate(profile?.shortInterest)?.[0]?.daysToCover
    : '-';

  const quoteMetrics = [
    {
      label: t('Quote.Metrics.open'),
      value: profile?.richQuoteData.open ? `$${numberShorthand(profile?.richQuoteData.open, 2)}` : '-',
    },
    {
      label: t('Quote.Metrics.close'),
      value: profile?.richQuoteData.close ? `$${numberShorthand(profile?.richQuoteData.close, 2)}` : '-',
    },
    {
      label: t('Quote.Metrics.volume-avg'),
      value: `${numberShorthand(profile?.richQuoteData.volume, 2) || '-'} / ${
        numberShorthand(profile?.richQuoteData.averageVolume, 2) || '-'
      }`,
    },
    {
      label: t('Quote.Metrics.day-range'),
      value: `${profile?.richQuoteData.low ? `$${numberShorthand(profile?.richQuoteData.low, 2)}` : '-'} - ${
        profile?.richQuoteData.high ? `$${numberShorthand(profile?.richQuoteData.high, 2)}` : '-'
      }`,
    },
    {
      label: t('Quote.Metrics.52wkrange'),
      value: `${numberShorthand(profile?.richQuoteData.fiftyTwoWeekLow, 2) || '-'} - ${
        numberShorthand(profile?.richQuoteData.fiftyTwoWeekHigh, 2) || '-'
      }`,
    },
    {
      label: t('Quote.Metrics.market-cap'),
      value: profile?.richQuoteData.marketCap ? `$${numberShorthand(profile?.richQuoteData.marketCap, 2)}` : '-',
    },
    {
      label: t('Quote.Metrics.pe-ratio'),
      value: profile?.fundamentals?.valuationRatios?.[0]?.peRatio
        ? `${numberShorthand(profile?.fundamentals?.valuationRatios?.[0]?.peRatio, 2)}`
        : '-',
    },
    {
      label: t('Quote.Metrics.dividend-yield'),
      value: profile?.fundamentals?.valuationRatios?.[0]?.forwardDividendYield
        ? `${numberShorthand(profile?.fundamentals?.valuationRatios?.[0]?.forwardDividendYield * 100, 2)}%`
        : '-',
    },
    {
      label: t('Quote.Metrics.exchange'),
      value: profile?.richQuoteData?.bzExchange,
    },
    {
      label: t('Quote.Metrics.rsi'),
      value: profile?.technicals?.rsi ? (
        <div className="flex items-center gap-4">
          {Math.round(profile?.technicals?.rsi)}
          {profile?.technicals?.rsi > 70 || profile?.technicals?.rsi < 30 ? <Thumb down /> : <Thumb up />}
        </div>
      ) : (
        '-'
      ),
    },
    {
      label: t('Quote.Metrics.short-interest'),
      value: profile?.tickerDetails?.sharesShortPercentOfFloat ? (
        <div className="flex items-center gap-4">
          {profile?.tickerDetails?.sharesShortPercentOfFloat}%{' '}
          {profile?.tickerDetails?.sharesShortPercentOfFloat > 10 ? <Thumb down /> : <Thumb up />}
        </div>
      ) : (
        '-'
      ),
    },
    {
      label: t('Quote.Metrics.days-to-cover'),
      value: daysToCover,
    },
  ];

  return (
    <table className="w-full h-full">
      <tbody className="flex flex-col h-full divide-y divide-bzblue-300 px-4 py-2 rounded border border-bzblue-400">
        {quoteMetrics.map(metric => (
          <QuoteMetricRow key={metric.label} label={metric.label} value={metric.value} />
        ))}
      </tbody>
    </table>
  );
};

export default QuoteMetrics;
