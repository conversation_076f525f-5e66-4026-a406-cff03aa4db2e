import React from 'react';
import AboutQuote from './AboutQuote';
import CryptoQuoteMetrics from './CryptoQuoteMetrics';
import ETFQuoteMetrics from './ETFQuoteMetrics';
import QuoteMetrics from './QuoteMetrics';
import { Profile } from '@benzinga/content-manager';
import { QuoteProfile } from '../../../../src/entities/quoteEntity';

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

interface MetricsSidebarProps {
  isETF: boolean;
  isCrypto?: boolean;
  profile: QuoteProfile;
}

export const MetricsSidebar: React.FC<MetricsSidebarProps> = ({ isCrypto, isETF, profile }) => {
  return (
    <>
      {isCrypto ? (
        <CryptoQuoteMetrics profile={profile} />
      ) : isETF ? (
        <ETFQuoteMetrics profile={profile} />
      ) : (
        <QuoteMetrics profile={profile} />
      )}
    </>
  );
};
