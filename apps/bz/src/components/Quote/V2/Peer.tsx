'use client';
import React from 'react';
import classnames from 'classnames';
import styled from '@benzinga/themetron';
import { ChartDataResponse } from '@benzinga/chart-manager';
import { formatRoundFixed } from '@benzinga/utils';
import { AFTER_MARKET, DelayedQuote, PRE_MARKET, REGULAR } from '@benzinga/quotes-manager';
import { Up } from '@benzinga/themed-icons';

const PeerChart = React.lazy(() =>
  import('./PeerChart').then(module => {
    return { default: module.PeerChart };
  }),
);

interface PeerProps {
  chart: ChartDataResponse;
  currentSymbol: string;
  quote: DelayedQuote;
  type: string;
}

const Peer: React.FC<PeerProps> = ({ chart, quote, type }) => {
  const symbol = quote?.symbol;
  // const session = React.useContext(SessionContext);

  // const chartManager = React.useMemo(() => session.getManager(ChartManager), [session]);
  // onst [chartData, setChartData] = React.useState<ChartDataResponse>(chart[currentSymbol]);

  // React.useEffect(() => {
  //   if (!symbol) return;

  //   async function getChartData() {
  //     const res = await chartManager.getChart(symbol, '1d', '20min');
  //     setChartData(res.ok);
  //   }

  //   getChartData();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [symbol]);

  let chartColors: [string, string, string] = ['#1060FF4D', '#1060FF00', '#1060FF'];

  // quote has loaded
  if (quote && quote.lastTradePrice) {
    let color = 'text-blue-600';
    let price = quote.lastTradePrice;
    let changePercent = quote.changePercent;

    if (type === PRE_MARKET) {
      color = 'text-black';
    } else if (quote.change < 0) {
      color = 'text-red-600';
      chartColors = ['#FF40504D', '#FF405000', '#FF4050'];
    } else if (quote.change > 0) {
      color = 'text-green-600';
      chartColors = ['#0F993D4D', '#0F993D00', '#0F993D'];
    }

    if ((type === PRE_MARKET || type === AFTER_MARKET) && quote.ethPrice !== undefined) {
      price = quote.ethPrice;
      const change = price - quote.lastTradePrice || quote.change;
      changePercent = (change / quote.lastTradePrice) * 100 || quote.changePercent;

      if (change > 0) {
        color = 'text-green-600';
        chartColors = ['#0F993D4D', '#0F993D00', '#0F993D'];
      } else if (change < 0) {
        color = 'text-red-600';
        chartColors = ['#FF40504D', '#FF405000', '#FF4050'];
      }
    }

    let data: number[] = [];

    // chart has loaded
    if (Array.isArray(chart?.candles)) {
      data = chart.candles.map(candle => candle.open);
      const min = Math.min(...data);
      data = data.map(point => point - min);
    }

    if (type === REGULAR) {
      while (data.length < 19) {
        data.push(0);
      }
    }

    const sumOfChartData = data.reduce((dataPointA, dataPointB) => dataPointA + dataPointB, 0);

    return (
      <PeerWrapper className="rounded-xl mb-2 block">
        <div className="w-full h-full block">
          <a className="w-full h-full" href={`/quote/${symbol}`}>
            <div className="flex items-start justify-between">
              <div className="flex flex-col">
                <div className="font-bold symbol">{quote?.companyStandardName}</div>
                <div className="text-xs name">
                  {quote?.bzExchange}:{quote?.symbol}
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="font-bold text-black">${formatRoundFixed(price, 2)}</div>
                <div className="flex">
                  <span className="flex items-center justify-center h-[15px] w-[21px]">
                    {changePercent !== 0 && color === 'text-red-600' && <DownArrow />}
                    {changePercent !== 0 && color === 'text-green-600' && <UpArrow />}
                  </span>
                  <span className={classnames(color)}>
                    {changePercent !== 0 ? formatRoundFixed(changePercent, 2) : '-'}%
                  </span>
                </div>
              </div>
            </div>
            <div>
              {/* <Sparkline className="block" data={data} height={23} width={250} /> */}
              {sumOfChartData === 0 ? (
                <div className="text-black text-sm">No data available</div>
              ) : (
                <React.Suspense fallback={<div className="h-[50px]" />}>
                  <PeerChart colors={chartColors} seriesData={data} />
                </React.Suspense>
              )}
            </div>
          </a>
        </div>
      </PeerWrapper>
    );
  }

  // Return an empty row with just the symbol.
  return (
    <div className="border-b">
      <TableData>{symbol}</TableData>
      <TableData />
      <TableData />
      <TableData />
      <TableData />
      <TableData />
    </div>
  );
};

const TableData = styled.div`
  &.quote-table-data {
    padding: 5px;
    .custom-cell-width {
      width: 133px;
    }
  }
`;

export default Peer;

const PeerWrapper = styled.div`
  background: #fff;

  :hover {
    background: #f8f8f8;
  }

  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #ceddf2;

  .symbol {
    color: #283d59;
  }

  .name {
    color: #5b7292;
  }
`;

const Arrow = styled(Up)`
  font-size: 11px;
  margin-bottom: -6px;
`;

const UpArrow = styled(Arrow)`
  fill: ${props => props.theme.colorPalette.green500};
`;

const DownArrow = styled(Arrow)`
  transform: rotate(180deg);
  fill: ${props => props.theme.colorPalette.red500};
`;
