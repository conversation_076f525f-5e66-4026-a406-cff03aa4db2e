import React from 'react';
import styled from '@benzinga/themetron';
import LazyLoad from 'react-lazyload';
import { QuoteHeader } from './QuoteHeader';
import { QuoteTabs } from './QuoteTabs';
import { QuoteProfile as QuoteProfileProps } from '../../entities/quoteEntity';
import { TaboolaPlacement } from '@benzinga/ads';

interface QuoteLayoutProps {
  activeTab: string;
  profile: QuoteProfileProps;
  symbol: string;
  metaInfoData: any;
  tabKey: string;
  // tabContent: JSX.Element;
  // tabContents?: {
  //   [key: string]: JSX.Element
  // };
}

export const QuoteLayout: React.FC<React.PropsWithChildren<QuoteLayoutProps>> = ({
  activeTab,
  children,
  metaInfoData,
  profile,
  symbol,
}) => {
  return (
    <QuoteLayoutWrapper className={`quote-page-container ${activeTab ?? 'profile'}`}>
      <QuoteHeader key={symbol} profile={profile} symbol={symbol} />
      <React.Suspense fallback={<div />}>
        <QuoteTabs
          activeTab={activeTab}
          description={metaInfoData.description}
          profile={profile}
          symbol={symbol}
          title={metaInfoData.title}
        >
          {children}
        </QuoteTabs>
      </React.Suspense>
      <React.Suspense>
        <LazyLoad offset={100} once>
          <div className="w-full px-3.5 lg:px-0 wrapper mb-5">
            <div className="md:w-2/4 m-auto xs:w-full">
              <TaboolaPlacement
                container="taboola-feed-logged-in"
                mode="thumbs-feed-01"
                placement="Feed-Logged In"
                settings={{
                  other: 'auto',
                }}
                url=""
                vizSensor={false}
              />
            </div>
          </div>
        </LazyLoad>
      </React.Suspense>
    </QuoteLayoutWrapper>
  );
};

const QuoteLayoutWrapper = styled.div`
  &.quote-page-container {
    position: relative;

    .layout-container {
      width: 100%;
    }
    @media screen and (max-width: 768px) {
      .quote-header {
        .stock-fundamentals,
        .crypto-fundamentals {
          display: none;
        }
      }
    }
    @media screen and (min-width: 768px) {
      .quote-profile {
        .stock-fundamentals,
        .crypto-fundamentals {
          display: none;
        }
        .chart-and-ad-container {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }
      }
    }

    .trade-idea-portal-container {
      margin-top: 0;
    }

    .buttons-container {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .buttons-container a,
    .compare-brokers-button,
    .perks-button {
      width: 100%;
      white-space: nowrap;

      button {
        width: 100%;
        display: flex;
      }
    }

    .quote-profile-buttons-list {
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      width: 100%;

      @media screen and (min-width: 768px) {
        flex-direction: row;
      }

      /* @media screen and (min-width: 1024px) {
        display: none;
      } */
    }

    .tab-content-heading {
      margin: 10px 0;
      border-bottom: 2px solid ${({ theme }) => theme.colors.border};

      h1 {
        font-weight: ${({ theme }) => theme.fontWeight.semibold};
        font-size: ${({ theme }) => theme.fontSize['2xl']};
      }

      p {
        font-size: ${({ theme }) => theme.fontSize.base};
      }

      @media screen and (max-width: 768px) {
        padding: 0 10px;
      }
    }

    .quote-listing-analyst-ratings-tab,
    .quote-listing-earnings-tab {
      display: flex;
      padding-top: 0.5rem;
      /* padding: 10px; */

      /* > div {
        width: auto;
      } */

      .layout-container {
        margin-top: 0;
        margin-bottom: 0;
      }

      .quote-calendar {
        display: flex;
      }

      .quote-listing-analyst-ratings-tab-sidebar,
      .quote-listing-earnings-tab-sidebar {
        width: 100%;
      }
    }

    @media screen and (min-width: 768px) {
      flex-direction: row;

      .quote-listing-analyst-ratings-tab-sidebar,
      .quote-listing-earnings-tab-sidebar {
        max-width: 300px;
      }
    }

    .desktop-tab {
      display: none;
    }

    @media screen and (min-width: 1024px) {
      .desktop-tab {
        display: block;
      }

      .mobile-tab {
        display: none;
      }
    }
  }
`;
