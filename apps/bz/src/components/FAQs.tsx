import React, { ReactElement } from 'react';
import ReactDOMServer from 'react-dom/server';
import { FAQ } from '@benzinga/ui';
import { Schema } from '@benzinga/seo';

interface FAQInterface {
  answer: React.ReactNode | string;
  question: string;
}

interface FAQSchemaAnswer {
  '@type': string;
  text: string;
}

interface FAQSchemaQuestion {
  '@type': string;
  acceptedAnswer: FAQSchemaAnswer;
  name: string;
}

interface FAQSchema {
  '@context': 'https://schema.org';
  '@type': 'FAQPage';
  mainEntity: FAQSchemaQuestion[];
}

interface FAQProps {
  FAQsDataSet: FAQInterface[];
  title?: string;
}

export const FAQs: React.FC<FAQProps> = ({ FAQsDataSet, title }) => {
  const formatSchemaData = (FAQData: FAQInterface[]): FAQSchema => {
    const mappedQuestions = FAQData.map(item => {
      return {
        '@type': 'Question',
        acceptedAnswer: {
          '@type': 'Answer',
          text: ReactDOMServer.renderToStaticMarkup(<>{item.answer}</>),
        },
        name: item.question,
      };
    });

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: mappedQuestions,
    };
  };

  return (
    <div className="mt-6">
      <Schema data={formatSchemaData(FAQsDataSet)} name="faq" />
      <FAQ faqData={FAQsDataSet} title={title} />
    </div>
  );
};

export default FAQs;
