import React from 'react';

interface ClickableValue {
  isClickable: boolean;
  url: string;
  content: React.ReactNode;
}

interface StockInfoRowProps {
  label: string;
  values: (string | React.ReactNode | ClickableValue)[];
  shouldBlur?: boolean;
}

const isClickableValue = (value: any): value is ClickableValue => {
  return typeof value === 'object' && value !== null && 'isClickable' in value && value.isClickable;
};

const StockInfoRow: React.FC<StockInfoRowProps> = ({ label, values, shouldBlur = false }) => {
  return (
    <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
      <div
        style={{ borderLeftColor: '#b2c7e6', backgroundImage: 'linear-gradient(to right, #e1ebfa 0%, #e1ebfa00 100%)' }}
        className="flex items-center font-bold min-w-[120px] px-3 py-2 text-sm w-1/2 sm:w-40 border-l-2 shrink-0"
      >
        {label}
      </div>
      <div className="flex-1 overflow-hidden">
        <div className="flex gap-0.5 min-[400px]:gap-1 sm:gap-4 pb-2">
          {values.map((value, idx) => {
            if (isClickableValue(value)) {
              return (
                <a
                  key={idx}
                  href={value.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-2 sm:px-3 py-2 text-xs sm:text-sm text-gray-700 border rounded-lg flex-1 flex items-center justify-center hover:bg-gray-50 transition-colors text-center"
                  aria-label="Upgrade to Benzinga Edge to access this premium data"
                  title="Upgrade to Benzinga Edge to access this premium data"
                >
                  <div
                    className={`text-center font-bold ${label.toLowerCase() === 'sector' ? 'text-[10px] min-[500px]:text-sm' : 'text-xs sm:text-sm'} ${shouldBlur ? 'blur-md' : ''}`}
                  >
                    {value.content}
                  </div>
                </a>
              );
            }

            return (
              <div
                key={idx}
                className="px-2 sm:px-3 py-2 font-bold text-xs sm:text-sm text-gray-700 border rounded-lg flex-1 flex items-center justify-center text-center"
              >
                <div
                  className={`text-center font-bold ${label.toLowerCase() === 'sector' ? 'text-[10px] min-[500px]:text-sm' : 'text-xs sm:text-sm'} ${shouldBlur ? 'blur-md' : ''}`}
                >
                  {value}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StockInfoRow;
