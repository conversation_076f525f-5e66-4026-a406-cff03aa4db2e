import React, { useEffect, useState, useRef, Suspense } from 'react';
import StockBox from '../StockBox';
import StockInfoRow from '../StockInfoRow';
import StockSection from '../StockSection';
import EdgeSignUpBanner from '../EdgeSignUpBanner';
import { ScreenerHeaderLayout } from '../../ScreenerHeaderLayout';
import { getGlobalSession } from '../../../../pages/api/session';
import { AuthenticationManager } from '@benzinga/session';
import { usePermission } from '@benzinga/user-context';
import { ScannerManager, ScannerProtos } from '@benzinga/scanner-manager';
import { faLock } from '@fortawesome/pro-regular-svg-icons';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { Icon } from '@benzinga/core-ui';
import { usePathname } from 'next/navigation';
import '../../../../pages/screener/screener-styles.css';

const PREMIUM_URL_BASE = 'https://www.benzinga.com/premium/ideas/benzinga-edge-2';
const BLURRED_FIELDS_FOR_NON_LOGGED_IN = ['Dividend Yield', 'EPS (TTM)', 'P/E Ratio', 'Analyst Rating'] as const;
type FormatType = 'percentage' | 'dollar' | 'number' | 'ratio';

export const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.RaptiveAdPlaceholder,
  })),
);

interface Stock {
  symbol: string;
  company: string;
  price: number;
  change: number;
}

interface ClickableValue {
  isClickable: boolean;
  url: string;
  content: React.ReactNode;
}

interface SectionData {
  icon: IconDefinition;
  title: string;
  subtitle?: string;
  data: Record<string, (string | React.ReactNode | ClickableValue)[]>;
}

interface TabConfig {
  key: string;
  label: string;
  href: string;
  sortField: string;
  isActive: boolean;
}

interface ScreenerConfig {
  scannerFields: string[];
  filtersString: string;
  limit: number;
  sortField: string;
  sortDir: ScannerProtos.SortDir;
  utmPrefix: string;
}

interface SectionConfig {
  icon: IconDefinition;
  title: string;
  subtitle?: string;
  fields: Array<{
    label: string;
    fieldKey: string;
    format?: FormatType;
    formatter?: (value: any) => string;
    isSecure?: boolean;
  }>;
}

interface ScreenerPageConfig {
  headerConfig: {
    title: string;
    subtitle: string;
    description: string;
    is_ranking_screener: boolean;
  };
  pageContent: {
    title: string;
    subtitle: string;
    description: string;
  };
  screenerConfig: ScreenerConfig;
  tabs: TabConfig[];
  sections: SectionConfig[];
  stockInfoRows: Array<{
    label: string;
    fieldKey: string;
    formatter?: (value: any) => string;
  }>;
}

interface ScreenerTemplateProps {
  config: ScreenerPageConfig;
}

const ScreenerTemplate: React.FC<ScreenerTemplateProps> = ({ config }) => {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [stockData, setStockData] = useState<Record<string, string[]>>({});
  const [stockSectionsConfig, setStockSectionsConfig] = useState<SectionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasNavAd, setHasNavAd] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const stickyContainerRef = useRef<HTMLDivElement>(null);
  const isCollapsedRef = useRef(false);
  const pathname = usePathname();

  const session = getGlobalSession();
  const authManager = session.getManager(AuthenticationManager);
  const isLoggedIn = authManager.isLoggedIn();
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');

  useEffect(() => {
    setIsLoading(true);
  }, [pathname]);

  useEffect(() => {
    const checkAdBanner = () => {
      const adBanner = document.querySelector('.rotatingBanners_banner__PN0fs');
      setHasNavAd(!!adBanner);
    };

    checkAdBanner();

    const timer = setTimeout(checkAdBanner, 500);

    const observer = new MutationObserver(checkAdBanner);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    const checkIfMobile = () => window.innerWidth < 800;

    const updateMobileState = () => {
      const mobile = checkIfMobile();
      setIsMobile(mobile);

      if (!mobile) {
        return;
      }

      const hideRotatingBanner = localStorage.getItem('hideRotatingBanner') === 'true';
      const adBanner = document.querySelector('.rotatingBanners_banner__PN0fs');
      const hasAd = !!adBanner && !hideRotatingBanner;

      const MAIN_BLOCK_HEIGHT = 49;
      const QUOTE_BAR_HEIGHT = 52;
      const SEARCH_BAR_HEIGHT = 61;
      const ROTATING_BANNER_HEIGHT = 80;

      const collapsedHeight = 60;
      const fullHeightWithAd = MAIN_BLOCK_HEIGHT + QUOTE_BAR_HEIGHT + SEARCH_BAR_HEIGHT + ROTATING_BANNER_HEIGHT;
      const fullHeightWithoutAd = MAIN_BLOCK_HEIGHT + QUOTE_BAR_HEIGHT + SEARCH_BAR_HEIGHT;
    };

    updateMobileState();

    const handleResize = () => {
      updateMobileState();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isCollapsed]);

  useEffect(() => {
    if (!isMobile) return;

    let lastPos = 0;
    let ticking = false;

    const updateMobileNavHeight = (collapsed: boolean) => {
      const hideRotatingBanner = localStorage.getItem('hideRotatingBanner') === 'true';
      const adBanner = document.querySelector('.rotatingBanners_banner__PN0fs');
      const hasAd = !!adBanner && !hideRotatingBanner;

      let height: string;
      if (collapsed) {
        height = '60px';
      } else if (hasAd) {
        height = '242px';
      } else {
        height = '162px';
      }

      if (stickyContainerRef.current) {
        stickyContainerRef.current.style.setProperty('--mobile-nav-height', height);
      }
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const hideQuoteBar = false;
          const MAIN_BLOCK_HEIGHT = 49;
          const QUOTE_BAR_HEIGHT = 52;
          const SEARCH_BAR_HEIGHT = 61;
          const ROTATING_BANNER_HEIGHT = 80;
          const hideRotatingBanner = localStorage.getItem('hideRotatingBanner') === 'true';

          const offset =
            MAIN_BLOCK_HEIGHT +
            QUOTE_BAR_HEIGHT +
            SEARCH_BAR_HEIGHT +
            (hideRotatingBanner ? 0 : ROTATING_BANNER_HEIGHT);

          if (!hideQuoteBar) {
            const currentScrollY = window.scrollY;
            const scrollDirection = lastPos > currentScrollY ? 0 : 1;

            const scrollDiff = Math.abs(currentScrollY - lastPos);

            if (scrollDiff > 20) {
              const shouldCollapse = currentScrollY > offset + 30 && scrollDirection === 1;
              const shouldExpand = currentScrollY <= offset - 30 || scrollDirection === 0;

              if (shouldCollapse && !isCollapsedRef.current) {
                isCollapsedRef.current = true;
                setIsCollapsed(true);
                updateMobileNavHeight(true);
              } else if (shouldExpand && isCollapsedRef.current) {
                isCollapsedRef.current = false;
                setIsCollapsed(false);
                updateMobileNavHeight(false);
              }
              lastPos = currentScrollY;
            }
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    updateMobileNavHeight(isCollapsedRef.current);

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile]);

  useEffect(() => {
    const withLock = (
      value?: string | number | null,
      fieldName?: string,
      format?: FormatType,
    ): string | ClickableValue => {
      if (hasBzEdge) {
        if (value === null || value === undefined || value === '' || value === 0) {
          return 'NA';
        }

        const numValue = Number(value);

        switch (format) {
          case 'percentage':
            return `${numValue.toFixed(2)}%`;
          case 'dollar':
            return `$${numValue.toFixed(2)}`;
          case 'number':
            return numValue.toFixed(2);
          case 'ratio':
            return numValue.toFixed(2);
          default:
            return `${value}`;
        }
      }

      if (fieldName) {
        const formattedFieldName = fieldName
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-');

        const utmAdType = `${config.screenerConfig.utmPrefix}_${formattedFieldName}`;
        const url = `${PREMIUM_URL_BASE}?utm_adType=${encodeURIComponent(utmAdType)}`;

        return {
          isClickable: true,
          url,
          content: (
            <Icon className="flex items-center justify-center h-5 w-5 fill-current text-blue-500" icon={faLock} />
          ),
        };
      }

      return 'NA';
    };

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const session = getGlobalSession();
        const scannerManager = session.getManager(ScannerManager);

        const query: ScannerProtos.IQuery = {
          fields: config.screenerConfig.scannerFields,
          filtersAsString: config.screenerConfig.filtersString,
          limit: config.screenerConfig.limit,
          sortDir: config.screenerConfig.sortDir,
          sortField: config.screenerConfig.sortField,
        };

        const response = await scannerManager.getInstrumentsWithIQuery(query);

        if (response.ok) {
          const instruments = response.ok.instruments || [];

          const stockCards: Stock[] = instruments.map(item => ({
            symbol: item.symbol || '',
            company: item.name || '',
            price: Number(item.price || 0),
            change: Number(item.changePercent || 0),
          }));
          setStocks(stockCards);

          const stockInfoData: Record<string, string[]> = {};
          config.stockInfoRows.forEach(row => {
            if (row.formatter) {
              stockInfoData[row.label] = instruments.map(i => row.formatter!(i[row.fieldKey as keyof typeof i]));
            } else {
              stockInfoData[row.label] = instruments.map(i => i[row.fieldKey as keyof typeof i] || 'NA');
            }
          });
          setStockData(stockInfoData);

          const sections: SectionData[] = config.sections.map(section => {
            const sectionData: Record<string, (string | React.ReactNode | ClickableValue)[]> = {};

            section.fields.forEach(field => {
              sectionData[field.label] = instruments.map(i => {
                const value = i[field.fieldKey as keyof typeof i];
                if (field.isSecure) {
                  return withLock(value, field.fieldKey, field.format);
                } else {
                  if (value === null || value === undefined || value === '' || value === 0) {
                    return 'NA';
                  }

                  if (field.formatter) {
                    return field.formatter(value);
                  }

                  if (field.format) {
                    const numValue = Number(value);
                    switch (field.format) {
                      case 'percentage':
                        return `${numValue.toFixed(2)}%`;
                      case 'dollar':
                        return `$${numValue.toFixed(2)}`;
                      case 'number':
                        return numValue.toFixed(2);
                      case 'ratio':
                        return numValue.toFixed(2);
                      default:
                        return `${value}`;
                    }
                  }

                  return `${value}`;
                }
              });
            });

            return {
              icon: section.icon,
              title: section.title,
              subtitle: section.subtitle,
              data: sectionData,
            };
          });

          setStockSectionsConfig(sections);
        } else {
          console.error('Scanner API Error:', response.err);
        }
      } catch (error) {
        console.error('Error fetching stock data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [hasBzEdge, config, pathname]);

  const hasNoSidebar = isLoggedIn && hasBzEdge;

  const getStickyContainerClasses = (
    isLoggedIn: boolean,
    hasBzEdge: boolean,
    isMobile: boolean,
    hasNavAd: boolean,
  ): string => {
    const baseClass = 'screener-stock-boxes-container';

    if (!isLoggedIn || !hasBzEdge) {
      return baseClass;
    }

    if (isMobile) {
      return `${baseClass} sticky-position-mobile`;
    }

    return hasNavAd ? `${baseClass} sticky-position-with-ad` : `${baseClass} sticky-position`;
  };

  const getStickyContainerStyles = (
    isLoggedIn: boolean,
    hasBzEdge: boolean,
    isMobile: boolean,
    hasNavAd: boolean,
  ): React.CSSProperties | undefined => {
    if (!isLoggedIn || !hasBzEdge || !isMobile) {
      return undefined;
    }

    return {
      '--mobile-nav-height': hasNavAd ? '242px' : '162px',
    } as React.CSSProperties;
  };

  const headerConfig = {
    ...config.headerConfig,
    hasNoSidebar,
    tabs: config.tabs.map(tab => ({
      key: tab.key,
      label: tab.label,
      href: tab.href,
      isActive: tab.isActive,
    })),
  };

  const LoadingSkeleton = () => (
    <div className="animate-pulse">
      <div className="screener-stock-boxes-container">
        <div className="hidden min-[1200px]:flex flex-col gap-2 w-40" />
        <div className="flex flex-row gap-1 sm:gap-4 items-start mb-6 min-[1200px]:mb-8 w-full bg-white pt-[5px] min-[1080px]:max-[1199px]:justify-end min-[1080px]:max-[1199px]:mr-[125px]">
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className="flex-1 min-w-0 max-w-[110px] sm:max-w-none">
              <div className="relative w-full cursor-pointer">
                <div className="flex flex-col justify-between rounded-xl shadow-sm border px-1 sm:px-3 py-2 sm:py-3 space-y-1 h-[110px] sm:h-[150px] lg:h-[130px] cursor-pointer transition-shadow duration-200 bg-white">
                  <div className="flex flex-col items-center lg:items-start">
                    <div className="bg-gray-300 rounded h-3 sm:h-6 w-6 sm:w-12 mb-1" />
                    <div className="bg-gray-200 rounded h-2 sm:h-3 w-12 sm:w-16" />
                  </div>
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-1 lg:gap-2 items-center">
                    <div className="bg-gray-300 rounded h-3 sm:h-5 w-8 sm:w-12" />
                    <div className="bg-gray-300 rounded-full h-4 sm:h-5 w-10 sm:w-14 px-1 sm:px-1.5 py-0.5" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-2 mb-6">
        {config.stockInfoRows.map((_, i) => (
          <div key={i} className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <div
              style={{
                borderLeftColor: '#b2c7e6',
                backgroundImage: 'linear-gradient(to right, #e1ebfa 0%, #e1ebfa00 100%)',
              }}
              className="flex items-center font-bold min-w-[120px] px-3 py-2 text-sm w-1/2 sm:w-40 border-l-2 shrink-0"
            >
              <div className="bg-gray-300 rounded h-4 w-16" />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="flex gap-0.5 min-[400px]:gap-1 sm:gap-4 pb-2 text-[9px] min-[400px]:text-sm">
                {[1, 2, 3, 4, 5].map(j => (
                  <div
                    key={j}
                    className="px-2 sm:px-3 py-2 font-bold text-xs sm:text-sm text-gray-700 border rounded-lg flex-1 flex items-center justify-center text-center"
                  >
                    <div className="bg-gray-200 rounded h-3 w-6" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-4 min-[1200px]:space-y-8">
        {config.sections.map((section, i) => (
          <div key={i}>
            <div className="flex items-center gap-3 mb-4 mt-8">
              <div className="bg-blue-50 p-2 rounded-md">
                <div className="bg-gray-300 rounded h-5 w-5" />
              </div>
              <div>
                <div className="bg-gray-300 rounded h-4 w-24 mb-1" />
                {section.subtitle && <div className="bg-gray-200 rounded h-3 w-16" />}
              </div>
            </div>

            <div className="space-y-2 overflow-x-auto">
              {section.fields.map((_, j) => (
                <div key={j} className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                  <div
                    style={{
                      borderLeftColor: '#b2c7e6',
                      backgroundImage: 'linear-gradient(to right, #e1ebfa 0%, #e1ebfa00 100%)',
                    }}
                    className="flex items-center font-bold min-w-[120px] px-3 py-2 text-sm w-1/2 sm:w-40 border-l-2 shrink-0"
                  >
                    <div className="bg-gray-300 rounded h-4 w-16" />
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <div className="flex gap-0.5 min-[400px]:gap-1 sm:gap-4 pb-2 text-[9px] min-[400px]:text-sm">
                      {[1, 2, 3, 4, 5].map(k => (
                        <div
                          key={k}
                          className="px-2 sm:px-3 py-2 font-bold text-xs sm:text-sm text-gray-700 border rounded-lg flex-1 flex items-center justify-center text-center"
                        >
                          <div className="bg-gray-200 rounded h-3 w-6" />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <>
      <ScreenerHeaderLayout {...headerConfig} />
      <div className={`screener-container ${hasNoSidebar ? 'has-no-sidebar' : ''}`}>
        <div className={`screener-main-content ${hasNoSidebar ? 'has-no-sidebar' : ''}`}>
          <div className="mb-8 min-[1050px]:mb-12 mt-4 min-[1200px]:mt-8">
            <p className={`text-[#1a202c] screener-extra-bold text-2xl min-[1200px]:text-3xl mb-4`}>
              {config.pageContent.title}
            </p>
            <p className={`text-[#1a202c] screener-extra-bold text-lg min-[1200px]:text-2xl`}>
              {config.pageContent.subtitle}
            </p>
            <p className="text-[#5B7292] font-normal text-sm min-[1200px]:text-[16px]">
              {config.pageContent.description}
            </p>
          </div>

          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <>
              <div
                ref={stickyContainerRef}
                className={getStickyContainerClasses(isLoggedIn, hasBzEdge, isMobile, hasNavAd)}
                style={getStickyContainerStyles(isLoggedIn, hasBzEdge, isMobile, hasNavAd)}
              >
                <div className="hidden min-[1200px]:flex flex-col gap-2 w-40" />
                <div className="flex flex-row gap-1 sm:gap-4 items-start mb-6 min-[1200px]:mb-8 w-full bg-white pt-[5px] min-[1080px]:max-[1199px]:justify-end min-[1080px]:max-[1199px]:mr-[125px]">
                  {(() => {
                    const validTypes = ['momentum', 'value', 'growth', 'quality'];
                    const activeTab = config.tabs.find(tab => tab.isActive);
                    const type =
                      activeTab && validTypes.includes(activeTab.key.toLowerCase())
                        ? activeTab.key.toLowerCase()
                        : 'momentum';
                    return stocks.map(stock => (
                      <div key={stock.symbol} className="flex-1 min-w-0 max-w-[110px] sm:max-w-none">
                        <StockBox
                          {...stock}
                          isBlurred={!(isLoggedIn && hasBzEdge)}
                          type={type as 'momentum' | 'value' | 'growth' | 'quality'}
                        />
                      </div>
                    ));
                  })()}
                </div>
              </div>

              <div className="space-y-2">
                {Object.entries(stockData).map(([label, values]) => (
                  <StockInfoRow key={label} label={label} values={values} />
                ))}
              </div>

              <div className="space-y-4 min-[1200px]:space-y-8">
                {stockSectionsConfig.map(({ icon, title, subtitle, data }) => (
                  <StockSection
                    key={title}
                    icon={icon}
                    title={title}
                    subtitle={subtitle}
                    data={data}
                    shouldBlurRow={label => !isLoggedIn && BLURRED_FIELDS_FOR_NON_LOGGED_IN.includes(label as any)}
                  />
                ))}
              </div>
            </>
          )}
        </div>

        {!(isLoggedIn && hasBzEdge) && (
          <>
            <div className="screener-sidebar-wrapper">
              <EdgeSignUpBanner variant="borderless" isLoggedIn={isLoggedIn} hasBzEdge={hasBzEdge} />
              <RaptiveAdPlaceholder
                className="flex items-center justify-center w-full"
                onlyDesktop={true}
                type="static-sidebar"
              />
            </div>
            <div className="max-[1199px]:block hidden mt-8 w-full max-w-[300px] mx-auto">
              <EdgeSignUpBanner variant="borderless" isLoggedIn={isLoggedIn} hasBzEdge={hasBzEdge} />
              <RaptiveAdPlaceholder
                className="flex items-center justify-center w-full"
                onlyDesktop={true}
                type="static-sidebar"
              />
              <RaptiveAdPlaceholder
                className="flex items-center justify-center w-full"
                onlyMobile={true}
                type="bottom"
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ScreenerTemplate;

export type { ScreenerPageConfig, ScreenerConfig, SectionConfig, TabConfig };
