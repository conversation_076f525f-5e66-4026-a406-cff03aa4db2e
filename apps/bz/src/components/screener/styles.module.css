
.container {
  background-color: #f2f8ff;
  border: 1px solid black;
  border-radius: 10px;
  @media (max-width: 768px) {
    margin-bottom: 50px;
  }
}

.borderless {
  border: none;
}

.header {
  padding-top: 14px;
  padding-left: 14px;
  font-family: 'Manrope', sans-serif;
  font-size: 17px;
  font-weight: 800;
  color: #3d5a80;
  text-transform: uppercase;
  margin-bottom: 10px;
  letter-spacing: 2px;
}

.title {
  padding-left: 14px;
  font-size: 20px;
  font-weight: bold;
  color: black;
  margin-bottom: 20px;
}



.callToAction {
  border-top: 5px solid #3f83f8;;
  margin-top: 20px;
  width: 100%;
  display: block;
  background-image:
  linear-gradient(to bottom, rgba(63, 131, 248, 0.24) 0%, rgba(63, 131, 248, 0) 100%),
  linear-gradient(to bottom, rgba(255, 255, 255, 0.15) 0%, rgba(0, 0, 0, 0.15) 100%),
  linear-gradient(to bottom, #192940 0%, #02060B 100%);
  color: white;
  border-radius: 0 0 8px 8px ;
}

.callToAction p {
  margin: 5px 0;
  width: 76%;
  font-size: 14px;
}

.callToAction p:first-of-type {
  margin-top: 10px;
  margin-bottom: -10px;
  padding-top: 2px;
  padding-left: 14px;
  font-weight: bold;
  font-size: 16px;
}
.callToAction p:nth-of-type(2) {
  padding-top: 10px;
  padding-left: 14px;
  font-weight: 100;
}

.signUpButton {
  border: none;
  color: white;
  padding: 9px 24px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 4px;
  margin: 10px 14px;
}


.featureList {
  list-style-type: none;
  padding-left: 0;
  font-size: 18px;
}

.feature {
  padding-left: 14px;
  display: flex;
  align-items: center;
}

.label {
  color: #283d59;
  margin-left: 8px;
  font-size: 15px;
  font-weight: bold;
}