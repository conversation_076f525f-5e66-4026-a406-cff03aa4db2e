import React from 'react';
import { createPortal } from 'react-dom';

interface StockBoxProps {
  symbol: string;
  company: string;
  price: number;
  change: number;
  isBlurred?: boolean;
  type: 'momentum' | 'value' | 'growth' | 'quality';
}

const StockBox: React.FC<StockBoxProps> = ({ symbol, company, price, change, isBlurred, type }) => {
  const isPositive = change >= 0;
  const [showUpgrade, setShowUpgrade] = React.useState(false);
  const [boxRect, setBoxRect] = React.useState<DOMRect | null>(null);
  const boxRef = React.useRef<HTMLDivElement>(null);

  const handleMouseEnter = () => {
    if (isBlurred && boxRef.current) {
      setBoxRect(boxRef.current.getBoundingClientRect());
      setShowUpgrade(true);
    }
  };
  const handleMouseLeave = () => {
    setShowUpgrade(false);
  };

  return (
    <div
      ref={boxRef}
      className="relative w-full cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <a
        href={
          isBlurred
            ? `https://www.benzinga.com/premium/ideas/benzinga-edge-3/?utm_adType=best-${type}_Blurred_Tickers`
            : `https://www.benzinga.com/quote/${symbol}`
        }
        target="_blank"
        rel="noopener noreferrer"
        className={`block no-underline w-full${isBlurred ? ' opacity-70' : ''}`}
        tabIndex={0}
        aria-disabled={false}
      >
        <div
          className={`flex flex-col justify-between rounded-xl shadow-sm border px-1 sm:px-3 py-2 sm:py-3 space-y-1 h-[110px] sm:h-[150px] lg:h-[130px] cursor-pointer transition-shadow duration-200 bg-white${!isBlurred ? ' hover:shadow-md' : ''}`}
        >
          <div className={`flex flex-col items-center lg:items-start${isBlurred ? ' blur-md' : ''}`}>
            <div className="text-xs sm:text-2xl font-extrabold text-[#192940] truncate text-center lg:text-left">{symbol}</div>
            <div className="text-[8px] sm:text-xs text-gray-400 line-clamp-2 leading-tight text-center lg:text-left">{company}</div>
          </div>
          <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-1 lg:gap-2 items-center${isBlurred ? ' blur-md' : ''}`}>
            <div className="text-xs sm:text-xl font-extrabold text-[#192940] text-center lg:text-left">
              {price === 0 ? <span className="inline-block text-center">-</span> : `$${price.toFixed(2)}`}
            </div>
            <div
              className={`text-[8px] sm:text-sm font-medium px-1 sm:px-1.5 py-0.5 rounded-full ${
                isPositive ? 'text-green-700 bg-green-100' : 'text-red-700 bg-red-100'
              }`}
            >
              {isPositive ? '+' : ''}
              {change.toFixed(2)}%
            </div>
          </div>
        </div>
      </a>
      {isBlurred &&
        showUpgrade &&
        boxRect &&
        createPortal(
          <div
            className="fixed pointer-events-none flex flex-col items-center"
            style={{
              left: boxRect.left + boxRect.width / 2,
              top: boxRect.top - 1,
              transform: 'translateX(-50%) translateY(-100%)',
              zIndex: 9999,
            }}
          >
            <div className="w-[160px] bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg border border-blue-500 text-center">
              Upgrade to Edge for full access
            </div>
            <div className="triangle-down mt-[-1px]"></div>
          </div>,
          document.body,
        )}
    </div>
  );
};

export default StockBox;
