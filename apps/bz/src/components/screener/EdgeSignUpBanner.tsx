import React from 'react';
import styles from './styles.module.css';
import clsx from 'clsx';
import { Button } from '@benzinga/core-ui';

interface EdgeSignUpBannerProps {
  variant?: 'default' | 'borderless';
  isLoggedIn?: boolean;
  hasBzEdge?: boolean;
}

const EdgeSignUpBanner: React.FC<EdgeSignUpBannerProps> = ({
  variant = 'default',
  isLoggedIn = false,
  hasBzEdge = false,
}) => {
  let edgeSignUpLink = 'https://www.benzinga.com/register';

  if (isLoggedIn && !hasBzEdge) {
    edgeSignUpLink =
      'https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=commandcenter&utm_adtype=best-momentum-stocks-page&utm_ad=rightrail';
  }

  const headline = isLoggedIn && !hasBzEdge ? 'UNLOCK EDGE ACCESS' : 'Create a Free Account';
  return (
    <div
      className={clsx(styles.container, {
        [styles.borderless]: variant === 'borderless',
      })}
    >
      <h2 className={styles.header}>EDGE FEATURES</h2>
      <p className={styles.title}>
        Get Full Access to All <br />
        Tools with Benzinga Edge
      </p>
      <ul className={styles.featureList}>
        <li className={styles.feature}>
          <label>
            <input checked type="checkbox" aria-label="Advanced Fundamental Data" tabIndex={-1} readOnly />
            <span className={styles.label}>Advanced Fundamental Data</span>
          </label>
        </li>
        <li className={styles.feature}>
          <label>
            <input checked type="checkbox" aria-label="Dividend Analysis" tabIndex={-1} readOnly />
            <span className={styles.label}>Dividend Analysis</span>
          </label>
        </li>
        <li className={styles.feature}>
          <label>
            <input checked type="checkbox" aria-label="Financial Ratios" tabIndex={-1} readOnly />
            <span className={styles.label}>Financial Ratios</span>
          </label>
        </li>
        <li className={styles.feature}>
          <label>
            <input checked type="checkbox" aria-label="Custom Screening" tabIndex={-1} readOnly />
            <span className={styles.label}>Custom Screening</span>
          </label>
        </li>
      </ul>

      <div className={styles.callToAction}>
        <p>{headline}</p>
        <p>Sign up to access basic stock metrics and create watchlists</p>
        <Button as="a" href={edgeSignUpLink} target="_blank" className={styles.signUpButton} variant="flat-blue">
          SIGN UP NOW
        </Button>
      </div>
    </div>
  );
};
export default EdgeSignUpBanner;
