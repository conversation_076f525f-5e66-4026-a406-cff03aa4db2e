import React from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { Icon } from '@benzinga/core-ui';
import StockInfoRow from './StockInfoRow';

interface ClickableValue {
  isClickable: boolean;
  url: string;
  content: React.ReactNode;
}

interface StockSectionProps {
  icon: IconDefinition;
  title: string;
  subtitle?: string;
  data: Record<string, (string | React.ReactNode | ClickableValue)[]>;
  shouldBlurRow?: (label: string) => boolean;
}

const StockSection: React.FC<StockSectionProps> = ({ icon, title, subtitle, data, shouldBlurRow }) => {
  return (
    <div>
      <div className="flex items-center gap-3 mb-4 mt-8">
        <div className="bg-blue-50 p-2 rounded-md">
          <Icon className="flex items-center justify-center h-5 w-5 fill-current text-blue-500" icon={icon} />
        </div>
        <div>
          <div className="text-m font-bold text-gray-900">{title}</div>
          {subtitle && <div className="text-xs text-gray-500">{subtitle}</div>}
        </div>
      </div>

      <div className="space-y-2 overflow-x-auto">
        {Object.entries(data).map(([label, values]) => (
          <StockInfoRow
            key={label}
            label={label}
            values={values}
            shouldBlur={shouldBlurRow ? shouldBlurRow(label) : false}
          />
        ))}
      </div>
    </div>
  );
};

export default StockSection;
