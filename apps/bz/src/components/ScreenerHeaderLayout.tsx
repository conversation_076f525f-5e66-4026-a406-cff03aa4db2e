'use client';
import React, { useEffect, useState } from 'react';
import styled from '@benzinga/themetron';
import { CallToActionProps } from '@benzinga/ui';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

export interface ScreenerHeaderLayoutProps {
  campaign?: CallToActionProps;
  description?: string;
  services?: {
    service_img?: string;
    title: string;
    url: string;
  }[];
  subtitle?: string;
  title: string;
  is_ranking_screener?: boolean;
  hasNoSidebar?: boolean;
  tabs?: {
    key: string;
    label: string;
    href: string;
    isActive: boolean;
  }[];
}

export const ScreenerHeaderLayout = ({ title, tabs, hasNoSidebar = false }: ScreenerHeaderLayoutProps) => {
  const currentPageURL = usePathname();
  const [currentPath, setCurrentPath] = useState(currentPageURL);

  useEffect(() => {
    setCurrentPath(currentPageURL);
  }, [currentPageURL]);

  return (
    <ScreenerHeaderLayoutWrap>
      <div
        className="absolute inset-0 w-full h-full z-0"
        style={{
          background: 'linear-gradient(135deg, #192940 0%, #243a59 50%, #1e334b 100%)',
          backgroundImage: `radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                  linear-gradient(to bottom, transparent 85%, rgba(25, 41, 64, 0.9) 100%)`,
          backgroundSize: '20px 20px, 100% 100%',
          opacity: 0.95,
        }}
      />
      <div className={`block-content-wrapper ${hasNoSidebar ? 'has-no-sidebar' : ''}`}>
        <div className="content-top">
          <div className="flex text-white flex-col md:flex-row gap-4">
            <div className="md:w-full text-left">
              <h1 className="block-title text-white">{title}</h1>
            </div>
          </div>
        </div>

        <div className="content-bottom">
          {Array.isArray(tabs) && tabs.length > 0 && (
            <ul className="sub-pages-link inline-flex gap-4 flex-col md:flex-row">
              {tabs.map((tab, i) => (
                <li className={currentPath === tab.href ? 'active' : ''} key={`${tab.key}-${currentPath}-${i}`}>
                  <a href={tab.href}>{tab.label}</a>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </ScreenerHeaderLayoutWrap>
  );
};

const ScreenerHeaderLayoutWrap = styled.div`
  position: relative;
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  min-height: 180px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: -1.5px;

  .header-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 1;
    z-index: 1;
    filter: brightness(1.1) contrast(1.1) saturate(1.2);
  }

  .block-content-wrapper {
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 180px;
    padding: 1.5rem 1rem 0 1rem;
  }

  .block-content-wrapper.has-no-sidebar {
    max-width: 1050px;
  }

  .content-top {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .content-bottom {
    margin-top: auto;
    padding-top: 1rem;
    margin-bottom: -2rem;
    padding-bottom: 2rem;
    @media screen and (max-width: 768px) {
      margin-bottom: 0;
      padding-bottom: 1rem;
    }
  }

  .block-title {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1rem;

    @media screen and (max-width: 768px) {
      font-size: 2rem;
    }
  }
  .sub-pages-link {
    @media screen and (max-width: 767px) {
      width: 100%;
    }
    li {
      background: #2a4a6b;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      @media screen and (max-width: 767px) {
        border-radius: 5px;
        width: 100%;
        flex: 1;
        &:last-of-type {
          margin-bottom: 1rem;
        }
      }
      &.active,
      &:hover {
        background: #4083f8;
        transition: 0.4s;
      }
      a {
        display: block;
        padding: 8px 12px;
        color: white;
        font-weight: 500;
        width: 100%;
        text-align: center;
        @media screen and (max-width: 767px) {
          text-align: left;
        }
      }
    }
  }
`;
