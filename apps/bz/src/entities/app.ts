import type { AppProps as NextAppProps } from 'next/app';
import { HeaderProps } from './header';
import { MetaProps } from '@benzinga/seo';

export interface AppPageProps {
  includeAGGridStyles?: boolean;
  metaProps?: MetaProps;
  pageTargeting?: Record<string, string | string[]>;
  disableRaptiveFooter?: boolean;
  disableRaptiveReadyOnPageLoad?: boolean;
}

export interface AppProps extends NextAppProps {
  headerProps: HeaderProps;
  pageProps: AppPageProps;
}
