import { 
  ScreenerPageConfig, 
  BASE_CONFIG, 
  SCREENER_VARIANTS, 
  createTabs, 
  createRankingSection 
} from './shared-screener-config';

const variant = SCREENER_VARIANTS.momentum;

export const MOMENTUM_SCREENER_CONFIG: ScreenerPageConfig = {
  headerConfig: {
    ...BASE_CONFIG.headerConfig,
    title: variant.title,
    subtitle: variant.subtitle,
    description: variant.description,
  },
  pageContent: BASE_CONFIG.pageContent,
  screenerConfig: {
    scannerFields: [variant.percentileField, ...BASE_CONFIG.scannerFields],
    filtersString: `subtype_in_ADR,COMMON_SHARE,ETF;${variant.percentileField}_bt_,;marketCap_bt_25000000,`,
    limit: BASE_CONFIG.limit,
    sortField: variant.percentileField,
    sortDir: BASE_CONFIG.sortDir,
    utmPrefix: variant.utmPrefix,
  },
  tabs: createTabs('momentum'),
  stockInfoRows: BASE_CONFIG.stockInfoRows,
  sections: [createRankingSection(variant), ...BASE_CONFIG.sections],
};
