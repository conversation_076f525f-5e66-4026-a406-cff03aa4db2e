import { ScannerProtos } from '@benzinga/scanner-manager';
import { faStopwatch, faChartLineUp, faDollar, faInfoCircle, faChartLine } from '@fortawesome/pro-regular-svg-icons';
import { formatLarge } from '@benzinga/utils';

export interface ScreenerPageConfig {
  headerConfig: {
    title: string;
    subtitle: string;
    description: string;
    is_ranking_screener: boolean;
  };
  pageContent: {
    title: string;
    subtitle: string;
    description: string;
  };
  screenerConfig: {
    scannerFields: string[];
    filtersString: string;
    limit: number;
    sortField: string;
    sortDir: ScannerProtos.SortDir;
    utmPrefix: string;
  };
  tabs: Array<{
    key: string;
    label: string;
    href: string;
    sortField: string;
    isActive: boolean;
  }>;
  stockInfoRows: Array<{
    label: string;
    fieldKey: string;
    formatter?: (value: any) => string;
  }>;
  sections: Array<{
    icon: any;
    title: string;
    subtitle?: string;
    fields: Array<{
      label: string;
      fieldKey: string;
      format?: 'percentage' | 'dollar' | 'number' | 'ratio';
      formatter?: (value: any) => string;
      isSecure?: boolean;
    }>;
  }>;
}

export const BASE_CONFIG = {
  headerConfig: {
    is_ranking_screener: true,
  },
  pageContent: {
    title: 'Deep Stock Analysis & Comparison',
    subtitle: 'Compare stocks across multiple dimensions with our comprehensive dataset.',
    description: 'Access fundamental metrics, dividend analysis, market sentiment, and more.',
  },
  scannerFields: [
    'symbol',
    'name',
    'price',
    'changePercent',
    'marketCap',
    'gicsSectorName',
    'rsi',
    'high52Week',
    'low52Week',
    'dividendYield',
    'dividendGrowthRate1Year',
    'dividendGrowthRate5Year',
    'dividendPayoutRatio',
    'epsTtm',
    'roe',
    'roic',
    'priceToEarnings',
    'pegRatio',
    'totalDebtEquityRatio',
    'percentInstitutionalOwnership',
    'averageAnalystRecommendation',
  ],
  limit: 5,
  sortDir: ScannerProtos.SortDir.DESC,
  stockInfoRows: [
    {
      label: 'Market Cap',
      fieldKey: 'marketCap',
      formatter: (value: any) => (value ? formatLarge(Number(value)) : 'NA'),
    },
    {
      label: 'Sector',
      fieldKey: 'gicsSectorName',
    },
  ],
  sections: [
    {
      icon: faChartLine,
      title: 'Dividends & Income',
      fields: [
        { label: 'Dividend Yield', fieldKey: 'dividendYield', format: 'percentage' as const },
        { label: '1Y Growth', fieldKey: 'dividendGrowthRate1Year', format: 'percentage' as const, isSecure: true },
        { label: '5Y Growth', fieldKey: 'dividendGrowthRate5Year', format: 'percentage' as const, isSecure: true },
        { label: 'Payout Ratio', fieldKey: 'dividendPayoutRatio', format: 'percentage' as const, isSecure: true },
      ],
    },
    {
      icon: faChartLineUp,
      title: 'Profitability',
      fields: [
        { label: 'EPS (TTM)', fieldKey: 'epsTtm', format: 'number' as const },
        { label: 'ROE', fieldKey: 'roe', format: 'ratio' as const, isSecure: true },
        { label: 'ROIC', fieldKey: 'roic', format: 'ratio' as const, isSecure: true },
      ],
    },
    {
      icon: faDollar,
      title: 'Valuation',
      fields: [
        { label: 'P/E Ratio', fieldKey: 'priceToEarnings' },
        { label: 'D/E Ratio', fieldKey: 'totalDebtEquityRatio', format: 'ratio' as const, isSecure: true },
        { label: 'PEG Ratio', fieldKey: 'pegRatio', format: 'ratio' as const, isSecure: true },
      ],
    },
    {
      icon: faInfoCircle,
      title: 'Market Sentiment',
      fields: [
        { label: 'Analyst Rating', fieldKey: 'averageAnalystRecommendation' },
        {
          label: 'Institutional Ownership',
          fieldKey: 'percentInstitutionalOwnership',
          format: 'percentage' as const,
          isSecure: true,
        },
      ],
    },
  ],
};

export const SCREENER_VARIANTS = {
  momentum: {
    key: 'momentum',
    label: 'Best Momentum Stocks',
    title: 'Best Momentum Stocks',
    subtitle: 'Discover top-performing stocks with strong momentum indicators',
    description:
      'Our momentum screener identifies stocks with the strongest price momentum, helping you find potential winners in the market.',
    percentileField: 'momentumPercentile',
    utmPrefix: 'best-momentum',
  },
  quality: {
    key: 'quality',
    label: 'Best Quality Stocks',
    title: 'Best Quality Stocks',
    subtitle: 'Discover top-performing stocks with exceptional quality metrics',
    description:
      'Our quality screener identifies stocks with strong balance sheets, consistent earnings, and excellent management quality.',
    percentileField: 'qualityPercentile',
    utmPrefix: 'best-quality',
  },
  value: {
    key: 'value',
    label: 'Best Value Stocks',
    title: 'Best Value Stocks',
    subtitle: 'Find undervalued stocks with strong fundamentals',
    description:
      'Our value screener identifies undervalued stocks with strong fundamentals, helping you find bargains in the market.',
    percentileField: 'valuePercentile',
    utmPrefix: 'best-value',
  },
  growth: {
    key: 'growth',
    label: 'Best Growth Stocks',
    title: 'Best Growth Stocks',
    subtitle: 'Discover high-growth stocks with exceptional expansion potential',
    description:
      'Our growth screener identifies stocks with strong revenue and earnings growth, expanding markets, and scalable business models.',
    percentileField: 'growthPercentile',
    utmPrefix: 'best-growth',
  },
} as const;

export function createTabs(activeType: keyof typeof SCREENER_VARIANTS) {
  return [
    {
      key: SCREENER_VARIANTS[activeType].key,
      label: SCREENER_VARIANTS[activeType].label,
      href: `/screener/best-${SCREENER_VARIANTS[activeType].key}-stocks`,
      sortField: SCREENER_VARIANTS[activeType].percentileField,
      isActive: true,
    },
    ...Object.entries(SCREENER_VARIANTS)
      .filter(([key]) => key !== activeType)
      .map(([_, variant]) => ({
        key: variant.key,
        label: variant.label,
        href: `/screener/best-${variant.key}-stocks`,
        sortField: variant.percentileField,
        isActive: false,
      })),
  ];
}

export function createRankingSection(variant: (typeof SCREENER_VARIANTS)[keyof typeof SCREENER_VARIANTS]) {
  return {
    icon: faStopwatch,
    title: `Top Five Highest ${variant.title.split(' ')[1]} Ranking Stocks`,
    subtitle: 'Ranked by percentile (100 = highest)',
    fields: [
      {
        label: `${variant.title.split(' ')[1]} Rank`,
        fieldKey: variant.percentileField,
        formatter: (value: any) => (value ? Number(value).toFixed(2) : 'NA'),
      },
      { label: 'RSI', fieldKey: 'rsi', format: 'number' as const },
      { label: '12M Perf.', fieldKey: 'changePercent', format: 'percentage' as const },
      { label: '52W High', fieldKey: 'high52Week', format: 'dollar' as const },
      { label: '52W Low', fieldKey: 'low52Week', format: 'dollar' as const },
    ],
  };
}
