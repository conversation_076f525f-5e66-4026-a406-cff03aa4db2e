import React, { Suspense } from 'react';

import getQuoteV2PageData, { QuoteV2PageProps, getQuoteFeedTypeByLocale } from '../../../src/quoteV2Utils';

import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';

import { SectionTitle, StatBoxesContainer } from '@benzinga/core-ui';
import { formatPercentage, numberShorthand } from '@benzinga/utils';
import DividendsFAQ from '../../../src/components/Quote/FAQ/DividendsFAQ';
import { DividendSummary } from '../../api/quote/[ticker]/dividends';
import { formatProfileDataToStockFundamentalsData } from '../../../src/components/Quote/utils';
import { QuoteProfile } from '../../../src/entities/quoteEntity';
import StatBox from '../../../src/components/Quote/V2/StatBox';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import { DateTime } from 'luxon';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { useTranslation } from 'react-i18next';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const formatDate = (date: string) => {
  const d = DateTime.fromISO(date);
  let format = 'MMM d';
  if (d < DateTime.now().minus({ year: 1 })) {
    format = 'MMM d, y';
  }
  return date ? DateTime.fromISO(date).setLocale(i18n.language).toFormat(format) : 'N/A';
};

const DividendSummaryBox: React.FC<{ dividendSummary: DividendSummary; profile: QuoteProfile }> = ({ profile }) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });
  const stock = formatProfileDataToStockFundamentalsData({ profile });

  return (
    <>
      <StatBoxesContainer>
        {stock?.dividendYield && (
          <StatBox
            title={`${t('Quote.Dividend.dividend-yield')} (${t('Quote.Dividend.forward')})`}
            value={`${formatPercentage(Number(stock.dividendYield))}%`}
          />
        )}
        {!!stock?.dividend && (
          <StatBox
            title={t('Quote.Dividend.annual-dividend')}
            value={`$${numberShorthand(stock.forwardDividend, 2)}`}
          />
        )}
        {profile?.dividendSummary?.last?.record_date && (
          <StatBox
            title={t('Quote.Dividend.last-dividend')}
            value={formatDate(profile.dividendSummary.last?.record_date)}
          />
        )}
        {profile?.dividendSummary?.next?.ex_dividend_date && (
          <StatBox
            title={t('Quote.Dividend.next-dividend')}
            value={formatDate(profile.dividendSummary.next?.ex_dividend_date)}
          />
        )}
      </StatBoxesContainer>
    </>
  );
};

// const StatBoxesContainer = styled.div`
//   gap: 0.5rem;
//   flex-wrap: wrap;
//   margin-bottom: 0.5rem;
//   display: grid;
//   grid-template-columns: repeat(4, 1fr);
//   @media (max-width: 800px) {
//     grid-template-columns: repeat(2, 1fr);
//   }
// `;

const QuoteDividendsPage: React.FC<QuoteV2PageProps> = ({
  activeTab,
  altSuggestions,
  isETF,
  metaProps,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation('quote', { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  //"{{companyName}} currently pays {{dividendFrequency}} dividend of {{annualDividend}} per year for a yield of {{dividendYield}}%."

  const stock = formatProfileDataToStockFundamentalsData({ profile });

  const dividendFrequencyNumber = profile?.dividendSummary?.dividends.find(
    dividend => dividend.frequency !== 0,
  )?.frequency;
  const dividendFrequency: string =
    dividendFrequencyNumber === 12
      ? t('Date.Periods.monthly')
      : dividendFrequencyNumber === 4
        ? t('Date.Periods.quarterly')
        : dividendFrequencyNumber === 2
          ? t('Date.Periods.semi-annually')
          : dividendFrequencyNumber === 1
            ? t('Date.Periods.annual')
            : dividendFrequencyNumber?.toString() || '-';

  const lastExDividendDate = profile?.dividendSummary?.last?.ex_dividend_date;
  const nextExDividendDate = profile?.dividendSummary?.next?.ex_dividend_date;

  const generateOverviewDescription = (): string => {
    if (!stock.forwardDividend) {
      return t('Quote.Dividend.dividend-overview-description-no-dividend', {
        companyName: profile.richQuoteData?.companyStandardName,
      });
    }
    return t('Quote.Dividend.dividend-overview-description', {
      annualDividend: stock.dividend ? `$${numberShorthand(stock.forwardDividend, 2)}` : '-',
      companyName: profile.richQuoteData?.companyStandardName,
      dividendFrequency: dividendFrequency?.toLocaleLowerCase() || '-',
      dividendYield: stock.dividendYield ? formatPercentage(Number(stock.dividendYield)) : '-',
    });
  };

  const generateOverviewDescriptionSecond = (): string => {
    if (!lastExDividendDate && !nextExDividendDate) {
      return t('Quote.Dividend.dividend-overview-description-2-both-dates-unknown', {
        companyName: profile.richQuoteData?.companyStandardName,
      });
    }
    if (!nextExDividendDate) {
      return t('Quote.Dividend.dividend-overview-description-2-next-date-unknown', {
        companyName: profile.richQuoteData?.companyStandardName,
        lastDividendDate: lastExDividendDate ? formatDate(lastExDividendDate) : '-',
        nextDividendDate: nextExDividendDate ? formatDate(nextExDividendDate) : '-',
      });
    } else if (!lastExDividendDate) {
      return t('Quote.Dividend.dividend-overview-description-2-last-date-unknown', {
        companyName: profile.richQuoteData?.companyStandardName,
        lastDividendDate: lastExDividendDate ? formatDate(lastExDividendDate) : '-',
        nextDividendDate: nextExDividendDate ? formatDate(nextExDividendDate) : '-',
      });
    }
    return t('Quote.Dividend.dividend-overview-description-2', {
      companyName: profile.richQuoteData?.companyStandardName,
      lastDividendDate: lastExDividendDate ? formatDate(lastExDividendDate) : '-',
      nextDividendDate: nextExDividendDate ? formatDate(nextExDividendDate) : '-',
    });
  };

  const generateKeyHighlights = () => {
    return (
      <ul className="md:ml-8 list-disc">
        <li>
          <strong>{t('Quote.Dividend.KeyHighlights.annual-payout')}:</strong>{' '}
          {stock.dividend ? `$${numberShorthand(stock.forwardDividend, 2)}` : '-'}
        </li>
        <li>
          <strong>{t('Quote.Dividend.KeyHighlights.current-yield')}:</strong>{' '}
          {stock.dividendYield ? `${formatPercentage(Number(stock.dividendYield))}%` : '-'}
        </li>
        <li>
          <strong>{t('Quote.Dividend.KeyHighlights.next-ex-dividend-date')}:</strong>{' '}
          {nextExDividendDate ? formatDate(nextExDividendDate) : 'Unknown'}
        </li>
        <li>
          <strong>{t('Quote.Dividend.KeyHighlights.last-ex-dividend-date')}:</strong>{' '}
          {lastExDividendDate ? formatDate(lastExDividendDate) : 'Unknown'}
        </li>
        <li>
          <strong>{t('Quote.Dividend.KeyHighlights.payments-per-year')}:</strong> {dividendFrequencyNumber || '-'}
        </li>
      </ul>
    );
  };

  const overviewDescription = generateOverviewDescription();
  const overviewDescriptionSecond = generateOverviewDescriptionSecond();
  const keyHighlights = generateKeyHighlights();

  return (
    <QuoteLayout defaultPath={activeTab} profile={profile} showGovLink={showGovLink} symbol={symbol}>
      {/* Beef this up. */}
      <div className="main-div px-4">
        <div className="flex flex-col gap-2 mb-8">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="section-description">
            {t('Quote.Paragraphs.dividend', { stockName: profile.richQuoteData?.name })}
          </p>
          <SectionTitle level={2} uppercase={false}>
            {t('Quote.Dividend.dividend-overview-title', {
              companyName: profile.richQuoteData?.companyStandardName,
            })}
          </SectionTitle>
          <p className="section-description" dangerouslySetInnerHTML={{ __html: overviewDescription }} />
          <p className="section-description" dangerouslySetInnerHTML={{ __html: overviewDescriptionSecond }} />
          <SectionTitle level={2} uppercase={false}>
            {t('Quote.Dividend.key-highlights-title')}:
          </SectionTitle>
          {keyHighlights}
        </div>
        <DividendSummaryBox dividendSummary={profile.dividendSummary} profile={profile} />
      </div>
      <div className="main-div p-4">
        <SectionTitle level={2}>
          {t('Quote.Dividend.dividends-for', { companyName: profile.richQuoteData?.name })}
        </SectionTitle>
      </div>
      <div className="main-div p-4 gap-y-8">
        <QuoteCalendar
          calendar="dividends"
          hideFilters
          initialData={profile.dividendSummary?.dividends}
          symbol={symbol}
        />
        <Suspense fallback={<div />}>
          <RaptiveAdPlaceholder className="w-[300px] min-w-[300px]" onlyMobile={true} type="content-small" />
        </Suspense>
        <DrilldownFooter
          isETF={isETF}
          leftSection={
            <>
              <SectionTitle level={3} size="2xl">
                {t('Quote.FAQ.faq')}
              </SectionTitle>
              <DividendsFAQ dividendSummary={profile.dividendSummary} quote={profile.richQuoteData} />
              <p>
                {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
                {t('Buttons.browse', { ns: 'common' })} <a href="/calendars/dividends">{t('Quote.Nav.dividends')}</a>{' '}
                {t('Quote.Helpers.on-all-stocks')}
              </p>
            </>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

export default QuoteDividendsPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'dividends';
  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
  }

  return pageData;
};
