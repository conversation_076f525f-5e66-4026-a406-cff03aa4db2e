import { moneyMetaInfo, NewsTemplate } from '@benzinga/money';
import React from 'react';
import { homeTab } from '../api/home/<USER>';
import Hooks from '@benzinga/hooks';
import HomePageTemplate from '../../src/components/Home/components/HomePageTemplate';
import { HomePageWrapper } from '../indexs';
import { HOME_PAGE_TABS } from '../../src/components/Home/components/HomeTabs';

const StockIdeasPage = props => {
  Hooks.useEffectDidMount(() => {
    if (window.history) {
      window.history.scrollRestoration = 'manual';
    }
  });

  return (
    <HomePageWrapper $page={'stock-ideas'}>
      <HomePageTemplate {...props} />
    </HomePageWrapper>
  );
};

export default StockIdeasPage;

export const getServerSideProps = async ({ req, res }) => {
  try {
    res.setHeader('Cache-Control', 'public, s-maxage=120, stale-while-revalidate=240');

    const pageProps = {
      activeTab: HOME_PAGE_TABS.STOCKIDEAS,
    };

    const slug = '/stock-ideas';
    const tabResponse = await homeTab(slug, req);

    if (tabResponse?.success === false) {
      res.statusCode = tabResponse?.error;

      return {
        props: {
          error: 503,
          metaProps: tabResponse.postData ? moneyMetaInfo(tabResponse.postData) : null,
          post: null,
          slug,
          template: 'page',
        },
      };
    }

    return {
      props: {
        ...pageProps,
        metaProps: tabResponse.postData ? moneyMetaInfo(tabResponse.postData) : null,
        post: tabResponse.postData,
        slug,
        template: 'page',
      },
    };
  } catch {
    res.statusCode = 404;
    return {
      props: {
        error: 404,
        featuredNews: [],
        news: [],
        topic: '',
      },
    };
  }
};
