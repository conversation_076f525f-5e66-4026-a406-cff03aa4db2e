import React from 'react';
import { GetServerSideProps } from 'next';
import { MetaProps, PageType } from '@benzinga/seo';
import { isBrowser } from '@benzinga/device-utils';
import styled from '@benzinga/themetron';
import { Layout } from '@benzinga/core-ui';
import { LiveVideoCard, VideoCard, Carousel } from '@benzinga/ui';
import { FeaturedVideos } from '@benzinga/videos-manager';
import { WordpressSidebar } from '@benzinga/content-manager';
import { HomePageWrapper } from '../indexs';
import { getWatchPageProps, PlaylistItem } from '../api/watch';
import DefaultSidebar from '../../src/components/Sidebars/DefaultSidebar';
//import { HomeTabs } from '../../src/components/Home/components/HomeTabs';
import { MoneySidebar } from '@benzinga/money';

interface WatchPageProps {
  featuredVideos: FeaturedVideos;
  sidebar: WordpressSidebar | null;
  metaProps?: MetaProps;
  url?: string;
  playlists: PlaylistItem[];
}

export const Watch: React.FC<WatchPageProps> = ({ featuredVideos, playlists, sidebar }) => {
  return (
    <HomePageWrapper>
      <Layout
        layoutMain={
          <WatchTabWrapper className="watch-tab-wrapper">
            {featuredVideos?.live?.length > 0 && (
              <div className="live-video-card-wrapper mb-8">
                <LiveVideoCard id={featuredVideos?.live[0].video_id} title={featuredVideos?.live[0].title} />
              </div>
            )}
            <div className="flex flex-col gap-8">
              {playlists?.length > 0 &&
                playlists.map(playlist => (
                  <div className="flex flex-col" key={playlist.playlistId}>
                    <Carousel
                      className="video-list-wrapper"
                      component={video => <VideoCard id={video.id} title={video.title} variant="default" />}
                      embeddedTitle={<h3 className="text-lg md:text-2xl">{playlist.title}</h3>}
                      items={playlist.items}
                      options={{ focus: 0 }}
                      showArrowsOnlyOnOverflow={true}
                    />
                  </div>
                ))}
            </div>
          </WatchTabWrapper>
          // <HomeTabs
          //   activeTab="watch"
          //   tabContent={
          //     <WatchTabWrapper className="watch-tab-wrapper">
          //       {featuredVideos?.live?.length > 0 && (
          //         <div className="live-video-card-wrapper mb-8">
          //           <LiveVideoCard id={featuredVideos?.live[0].video_id} title={featuredVideos?.live[0].title} />
          //         </div>
          //       )}
          //       <div className="flex flex-col gap-8">
          //         {playlists?.length > 0 &&
          //           playlists.map(playlist => (
          //             <div className="flex flex-col" key={playlist.playlistId}>
          //               <Carousel
          //                 className="video-list-wrapper"
          //                 component={video => <VideoCard id={video.id} title={video.title} variant="default" />}
          //                 embeddedTitle={<h3 className="text-lg md:text-2xl">{playlist.title}</h3>}
          //                 items={playlist.items}
          //                 options={{ focus: 0 }}
          //                 showArrowsOnlyOnOverflow={true}
          //               />
          //             </div>
          //           ))}
          //       </div>
          //     </WatchTabWrapper>
          //   }
          // />
        }
        layoutSidebar={
          <React.Suspense fallback={null}>
            {sidebar ? <MoneySidebar sidebar={sidebar} /> : <DefaultSidebar sidebar={sidebar} />}
          </React.Suspense>
        }
        //title="Watch Live Stock Market News"
      />
    </HomePageWrapper>
  );
};

const WatchTabWrapper = styled.div`
  &.watch-tab-wrapper {
    .live-video-card-wrapper {
      height: 500px;
      min-height: 500px;
      border-radius: ${({ theme }) => theme.borderRadius.default};
      overflow: hidden;
      @media screen and (max-width: 800px) {
        height: 200px;
        min-height: 200px;
      }
    }

    .video-list-wrapper {
      .video-card {
        height: 250px;
        width: 350px;
        min-width: 350px;
        display: inline-block;
        box-sizing: content-box;
      }

      .carousel-pagination {
        margin-bottom: 1rem;

        @media (max-width: 500px) {
          button {
            width: 40px;
            height: 40px;
          }
        }
      }

      .embedded-title {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .splide-track-wrapper {
        display: flex;
        flex-direction: column-reverse;
      }
    }
  }
`;

const metaInfo = (): MetaProps => {
  const canonical = `https://www.benzinga.com/watch`;
  return {
    author: 'Benzinga',
    canonical,
    description: '',
    dimensions: {
      authorName: 'Benzinga',
      contentType: 'tool',
    },
    pageType: PageType.Front,
    // structuredData: getStructuredData(),
    title: 'Watch Live Stock Market News',
  };
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  const url = !isBrowser() ? `https://${req.headers.host}/watch` : '';
  const props: WatchPageProps = await getWatchPageProps();

  props.url = url;
  props.metaProps = metaInfo();

  return {
    props: props,
  };
};

export default Watch;
