import NextCors from 'nextjs-cors';
import axios from 'axios';
import { ITERABLE_API_KEY } from '../../src/env';

const handler = async (req, res) => {
  await NextCors(req, res, {
    methods: ['POST'],
    origin: '*',
    optionsSuccessStatus: 200,
  });
  const body = req.body || {};

  if (!body.email) {
    return res.status(400).json({ error: 'Email is required' });
  }
  if (!body.workflowId) {
    return res.status(400).json({ error: 'Workflow ID is required' });
  }

  const submission = await axios.post(
    'https://api.iterable.com/api/workflows/triggerWorkflow',
    {
      dataFields: body.dataFields || {},
      email: body.email,
      workflowId: body.workflowId,
    },
    {
      headers: {
        'Api-Key': ITERABLE_API_KEY,
        'Content-Type': 'application/json',
      },
    },
  );

  if (submission.status === 200) {
    res.status(200).json({ success: true });
  } else {
    res.status(400).json({ success: false, error: submission.data });
  }
};

export default handler;
