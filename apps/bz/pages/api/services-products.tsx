import { NextApiRequest, NextApiResponse } from 'next';
import { ContentManager } from '@benzinga/content-manager';
import { getGlobalSession } from '../../pages/api/session';
import { MoneyBlockProps } from '@benzinga/money';

export interface ServiceProduct {
  description: string;
  highlight: {
    description: string;
    title: string;
    value: string;
    shrink?: boolean;
  };
  logoText: {
    accentColor: string;
    brandName: string;
  };
  title: string;
  url: string;
}

export interface ServicePageProps {
  products: ServiceProduct[];
  webinarBlock: MoneyBlockProps | null;
}

const products = [
  {
    description:
      'Join investing expert <PERSON> as he reveals only the stock ideas that pass his time-tested, 3-point system.',
    highlight: {
      description: 'Get fresh stock recommendations focused on buyback yield, dividend yield, and debt repayment.',
      title: 'Massive Annual Discount',
      value: '$79',
    },
    logoText: {
      accentColor: '#F83F60',
      brandName: 'Yield Report',
    },
    title: 'The Yield Report',
    url: 'https://www.benzinga.com/premium/ideas/yield-report-sales-page/?t=yiyi1we5miseyi3&utm_source=services-page',
  },
  // {
  //   description: 'Go for fast-moving opportunities in the options space with the potential for big wins.',
  //   highlight: {
  //     description:
  //       'Options trading expert Nic Chahine will show how he managed to make significant profits in all market conditions.',
  //     title: 'winning cases',
  //     value: '90%',
  //   },
  //   logoText: {
  //     accentColor: '#3F83F8',
  //     brandName: 'Options',
  //   },
  //   title: 'MoneyLine',
  //   url: 'https://ptxt.io/1r8e8',
  // },
  {
    description:
      '35-year Wall Street veteran Matt Maley delivers high-probability stock and options setups—built for fast-moving, volatile markets.',
    highlight: {
      description: 'Get real-time trade alerts, live analysis, and institutional-level technical insight.',
      shrink: true,
      title: 'Active Trading | Short-Term Profits',
      value: 'Free 7-Day Trial',
    },
    logoText: {
      accentColor: '#F8773F',
      brandName: 'Stocks',
    },
    title: "Matt Maley's Inner Circle",
    url: 'https://www.benzinga.com/premium/ideas/7-day-free-trial-matt-maley-inner-circle/?utm_campaign=7daytrial&utm_adType=services-page&utm_ad=services-page',
  },
];

export async function getServicePageProps(): Promise<ServicePageProps> {
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const postRes = await contentManager.getPost(327599);

  let webinarBlock;
  if (postRes?.ok?.blocks) {
    webinarBlock = postRes.ok.blocks.find(block => block.blockName === 'acf/maven-webinar-banner');
  }

  return {
    products,
    webinarBlock: webinarBlock ?? null,
  };
}

const handler = async (_req: NextApiRequest, res: NextApiResponse) => {
  try {
    const data = await getServicePageProps();
    res.setHeader('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
    res.status(200).json(data);
  } catch (error) {
    console.error('Services Products API error:', error);
    res.status(500).json({ error: 'Failed to fetch data from Services Products API' });
  }
};

export default handler;
