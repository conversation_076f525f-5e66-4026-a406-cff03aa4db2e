import { SafeError, SafePromise } from '@benzinga/safe-await';

import { ArticleData, getArticle } from '@benzinga/article-manager';

import { articlesMetaInfo } from '@benzinga/article-manager';

import { getGlobalSession } from '../session';
import camelcaseKeys from 'camelcase-keys';

export const getArticleData = async (node_id: number | string): SafePromise<ArticleData> => {
  if (node_id === 'undefined') {
    return { err: new SafeError('node_id is undefined', 'getArticleData') };
  }
  const session = getGlobalSession();
  const articleRes = await getArticle(session, node_id);
  const articleData: ArticleData | undefined = articleRes?.ok && camelcaseKeys(articleRes.ok);
  if (articleData) {
    articleData.metaProps = await articlesMetaInfo(articleData);
    return { ok: articleData };
  } else {
    return { err: new SafeError(articleRes?.err?.message ?? 'Something went wrong', 'getArticleData'), ok: undefined };
  }
};
