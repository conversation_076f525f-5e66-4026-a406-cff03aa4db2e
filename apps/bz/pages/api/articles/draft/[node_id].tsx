import type { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession, loadSession } from '../../session';
import { PermissionsManager } from '@benzinga/permission-manager';
import { ArticleManager, parseArticleBodyToBlocks, DraftArticle, replaceRedditHTML } from '@benzinga/article-manager';
import { tickerizeContent } from '@benzinga/article-manager';

export const isAuthorizedTokenToViewDraftArticle = async (benzinga_token: string) => {
  if (!benzinga_token) {
    return false;
  } else {
    const session = await loadSession(benzinga_token);
    const permissionsManager = session.getManager(PermissionsManager);
    const isEditor = permissionsManager.hasAccess('editorial/edit')?.ok ?? false;
    const isContributor =
      permissionsManager.hasAccess('cms/role', 'Benzinga Contributor')?.ok ??
      permissionsManager.hasAccess('cms/role', 'Benzinga Editor')?.ok ??
      false;
    const canNewsdeskLogin = permissionsManager.hasAccess('newsdesk/login')?.ok ?? false;
    const canViewDraftArticle = isEditor || isContributor || canNewsdeskLogin;

    return canViewDraftArticle;
  }
};

const getDraftArticle = async (node_id: number | string, benzinga_token: string): Promise<DraftArticle | null> => {
  const canViewDraftArticle = await isAuthorizedTokenToViewDraftArticle(benzinga_token);
  let draftArticle: DraftArticle | null = null;

  if (canViewDraftArticle) {
    const session = getGlobalSession();
    const articleManager = session.getManager(ArticleManager);

    const draftArticleRes = await articleManager.getEditorialArticlePreview(node_id);

    if (draftArticleRes?.ok) {
      const article = draftArticleRes.ok.data;
      const image = article.field_image?.length > 0 ? article.field_image[0]?.filepath : null;
      const published = article.created ?? article.changed;
      const publishedAt = published ? new Date(published * 1000).toUTCString() : '';
      article.body = tickerizeContent(article.body);
      article.body = replaceRedditHTML(article.body);

      const blocks = await parseArticleBodyToBlocks(session, article.body, false, Number(node_id));

      draftArticle = {
        blocks: blocks,
        body: article.body,
        channels: article.taxonomy?.filter(taxonomy => {
          return taxonomy.vid == 1;
        }),
        createdAt: publishedAt,
        image: image ? `https://www.benzinga.com/${image}` : null,
        key_items: article.key_items,
        name: article?.name,
        nodeId: article.nid,
        tags: article.taxonomy?.filter(taxonomy => {
          return taxonomy.vid == 3;
        }),
        teaserText: article.teaser,
        tickers: article.taxonomy?.filter(taxonomy => {
          return taxonomy.vid == 2;
        }),
        title: article.title,
      };
    } else {
      // if (
      //   draftArticleRes?.err.data &&
      //   typeof draftArticleRes?.err.data === 'object' &&
      //   Object.keys(draftArticleRes.err.data).length
      // ) {
      //   console.error(`Error getting editorial preview - NodeId: ${node_id}`, JSON.stringify(draftArticleRes?.err));
      // }
    }
  }
  return draftArticle;
};

export const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const nodeId = req.query.node_id as string;

    const token =
      (req.query.token as string) || (req.headers['authorization'] as string) || req.cookies.benzinga_token || '';

    const isAuthorized = await isAuthorizedTokenToViewDraftArticle(token);

    if (!isAuthorized) {
      res.status(401).json({ status: 'not_authorized' });
    } else {
      if (!nodeId) {
        res.status(422).json({ status: 'missing_node_id' });
      } else {
        const data = await getDraftArticle(nodeId, token);
        if (data) {
          res.status(200).json(data);
        } else {
          res.status(500).json({ message: `failed to get draft article ${String(req.query.node_id)}` });
        }
      }
    }
  } catch (error) {
    res.status(401).json({ status: 'not_authorized' });
  }
};

export default handler;
