import { NextApiRequest, NextApiResponse } from 'next';
import probeImage from 'probe-image-size';

interface ProbeResult {
  width: number;
  height: number;
  type: string;
  mime: string;
  wUnits: string;
  hUnits: string;
  length?: number;
  url?: string;
}

interface ErrorResponse {
  error: {
    message: string;
    name: string;
    code?: string;
  };
}

const PROBE_IMAGE_TIMEOUTS = {
  open_timeout: 500,
  read_timeout: 500,
  response_timeout: 500,
  timeout: 750,
} as const;

const handler = async (req: NextApiRequest, res: NextApiResponse<ProbeResult | ErrorResponse>): Promise<void> => {
  const src = req.query.src as string | undefined;

  if (!src) {
    return res.status(400).json({
      error: {
        message: 'Missing src parameter',
        name: 'ValidationError',
      },
    });
  }

  try {
    const data = await probeImage(src, PROBE_IMAGE_TIMEOUTS);
    res.status(200).json(data);
  } catch (error) {
    // const ignoreErrors = ['ECONNRESET', 'ENOTFOUND', 'socket hang up', 'Unknown error'];
    // const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    // const ignoredErrorExists = ignoreErrors.some(type => errorMessage.includes(type));
    // if (!ignoredErrorExists) {
    //   console.error(`(Probe image error) - src: ${src}`, errorMessage);
    // }
    res.status(422).json({
      error: {
        code: error instanceof Error && 'code' in error ? (error as { code: string }).code : undefined,
        message: error instanceof Error ? `${error.message} - src: ${src}` : `Unknown error - src: ${src}`,
        name: error instanceof Error ? error.name : 'Error',
      },
    });
  }
};

export default handler;
