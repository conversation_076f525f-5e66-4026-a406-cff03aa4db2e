import { lensPath, view } from 'ramda';

import { Session } from '@benzinga/session';
import { getGlobalSession, getGlobalSessionSingleton } from './session';
import { CalendarManager } from '@benzinga/calendar-manager';
import { AutocompleteManager } from '@benzinga/autocomplete-manager';
import { SecuritiesManager } from '@benzinga/securities-manager';
import { DelayedQuote, QuoteSessionType, QuotesManager, TickerDetail } from '@benzinga/quotes-manager';
import { StockReportsManager } from '@benzinga/stock-reports-manager';
import { QuotePageTabSlugs } from '../../src/components/Quote/QuoteTabs';
import { SafeType, safeAwait, safeErrorStatus, safeTimeout } from '@benzinga/safe-await';
import { fetchShortInterest } from './quote/[ticker]/short-interest';
import { getConferenceCallsSummary } from './quote/[ticker]/conference-calls';
import { getStockSplitsSummary } from './quote/[ticker]/stock-splits';
import { getDividendSummary } from './quote/[ticker]/dividends';
import { getEarningsSummary } from './quote/[ticker]/earnings';
import { getRatingsSummary } from './quote/[ticker]/ratings';
import { getMASummary } from './quote/[ticker]/ma';
import { formatTickerInURLToTicker, Nullable } from '@benzinga/utils';
// import { QuoteProfile } from '../../src/entities/quoteEntity';

import { getCurrentSessionType } from '../../src/quoteV2Utils';
//import { getSponsoredContentForTicker } from '../api/sponsored';

import { getMoneyPost as getMoneyPostModule } from '@benzinga/money';

import { fetchWiim, getPressContentData } from './quote/[ticker]/news';
import { CryptoManager } from '@benzinga/crypto-manager';
import { BasicNewsManager, News, NodeQueryParams, formatToSimpleNewsQueryParams } from '@benzinga/basic-news-manager';
import { formatSymbols, StoryObject } from '@benzinga/advanced-news-manager';
import { DateTime } from 'luxon';
import { ScannerManager, ScannerProtos } from '@benzinga/scanner-manager';
import { QuoteProfile } from '../../src/entities/quoteEntity';
import { getGuidanceSummary } from './quote/[ticker]/guidance';
import { ChartManager } from '@benzinga/chart-manager';
import { ContentManager, WordpressPost } from '@benzinga/content-manager';
import { WordpressPage } from '@benzinga/content-manager';
import { ETFManager, Fund } from '@benzinga/etf-manager';
import i18n from '@benzinga/translate';

// enum FetchMethod {
//   DELETE = 'DELETE',
//   GET = 'GET',
//   PATCH = 'PATCH',
//   POST = 'POST',
//   PUT = 'PUT',
// }

// export const fetchData: RequestInit = {
//   headers: { Accept: 'application/json' },
//   method: FetchMethod.GET,
// };

export interface QuoteFund extends Fund {
  sector: string | null;
}

export const fetchTickerDetail = async (
  symbol: string[],
  session: Session,
): Promise<{
  quotes: Record<string, DelayedQuote> | null;
  tickerDetails: TickerDetail[];
}> => {
  try {
    const QuoteManager = session.getManager(QuotesManager);
    const tickerDetailsResponse = await QuoteManager.getTickerDetails(symbol);
    const tickerDetails = tickerDetailsResponse?.ok?.result || [];

    const detailedTickersSymbols: string[] =
      (!!tickerDetails.length && tickerDetails[0].peers?.map(peer => peer.symbol)) || [];
    const delayedQuotesSymbols: string[] = [...symbol, ...detailedTickersSymbols];

    const { ok: delayedQuotesResponse } = await QuoteManager.getDelayedQuotes(delayedQuotesSymbols);

    return { quotes: delayedQuotesResponse ?? null, tickerDetails: tickerDetails };
  } catch (error) {
    return { quotes: null, tickerDetails: [] };
  }
};

export const isTab = (value: string) => {
  return QuotePageTabSlugs.includes(value);
};

export const formatTickerWithPoint = (ticker: string): string => {
  if (!ticker) return '';
  return ticker.replace('-', '.').replace('/', '.');
};

export const getCryptoData = async (quote: DelayedQuote) => {
  const symbol = quote.symbol;
  const baseSymbol = symbol?.replace('/USD', '');
  const cashSymbol = `$${baseSymbol}`;
  const session = getGlobalSession();
  const basicNewsManager = session.getManager(BasicNewsManager);
  const cryptoNewsQuery = { limit: 5, tickers: [cashSymbol] };
  const cryptoNewsRes = await safeTimeout(basicNewsManager.simplyQueryNews(cryptoNewsQuery), 3000);
  const cryptoData = await session.getManager(CryptoManager).getCoin(baseSymbol);
  let logoUrl: string | null = null;
  logoUrl = cryptoData.ok?.image ?? null;

  if (!logoUrl) {
    const quoteManager = session.getManager(QuotesManager);
    const logoRes = await quoteManager.getQuotesLogos([symbol], {
      fields: 'mark_composite_light',
    });
    const originalLogoFilesPath = lensPath([0, 'files', 'mark_composite_light']);
    logoUrl = logoRes?.ok ? view(originalLogoFilesPath, logoRes.ok) : null;
  }

  let sidebar: SafeType<WordpressPost> | null = null;
  const contentManager = getGlobalSession().getManager(ContentManager);
  sidebar = await contentManager.getWordpressPost(59458);

  return {
    cryptoData: cryptoData.ok ?? null,
    cryptoNews: (cryptoNewsRes?.ok as News[]) || [],
    logoUrl: logoUrl || null,
    newsSymbol: cashSymbol,
    notFound: false,
    richQuoteData: quote,
    sidebar,
    symbol: quote.symbol,
    tickerDetails: null,
  };
};

export const getQuoteData = async (symbol: string): Promise<QuoteProfileData> => {
  symbol = formatTickerInURLToTicker(symbol);

  const session = getGlobalSession();
  const globalSession = getGlobalSessionSingleton();
  const quoteManager = session.getManager(QuotesManager);
  const securitiesManager = session.getManager(SecuritiesManager);

  const symbolWithPoint = formatTickerWithPoint(symbol) as string;

  const richQuoteResp = await safeTimeout(quoteManager.getDelayedQuotes([symbol]), 1200);
  // const richQuoteResp = await quoteManager.getDelayedQuotes([symbol]);
  const quote = richQuoteResp?.ok?.[symbol];
  if (quote?.error?.code === 1) {
    return { notFound: true, errorCode: 410 };
  } else if (quote?.error?.code === 2) {
    return { notFound: true, errorCode: 404 };
  } else if (quote?.error) {
    return { notFound: true, errorCode: 503 };
  }

  if (richQuoteResp?.err) {
    if (richQuoteResp.err.type === 'timeout') {
      return { notFound: true, errorCode: 408 };
    }
    return { notFound: true, errorCode: 503 };
  }

  if (quote?.type === 'CRYPTO') {
    return getCryptoData(quote);
  }

  const logoRes = quoteManager.getQuotesLogos([symbol], {
    fields: 'mark_composite_light',
  });
  const schedule = quoteManager.getSchedule();
  const fundamentals = securitiesManager.getFinancials({ period: '3M', symbols: symbol });
  const fundamentalsAnnual = securitiesManager.getFinancials({ period: '12M', symbols: symbol });
  const tickerDetail = fetchTickerDetail([symbol, symbolWithPoint], session);
  const wiimReq = fetchWiim(session, symbol);
  const splitsSummaryReq = safeAwait(getStockSplitsSummary(symbol));
  const shortInterest = fetchShortInterest(session, [symbol]);
  const maSummaryReq = safeAwait(getMASummary(symbol));
  const dividendSummaryReq = safeAwait(getDividendSummary(symbol));
  const conferenceCallsSummaryReq = safeAwait(getConferenceCallsSummary(symbol));

  const [
    conferenceCallsSummaryResp,
    dividendSummaryResp,
    fundamentalsResp,
    fundamentalsAnnualResp,
    logoResp,
    scheduleResp,
    tickerDetailResp,
    splitsDataResp,
    shortInterestResp,
    wiimResp,
    maSummaryResp,
  ] = await Promise.all([
    conferenceCallsSummaryReq,
    dividendSummaryReq,
    fundamentals,
    fundamentalsAnnual,
    logoRes,
    schedule,
    tickerDetail,
    splitsSummaryReq,
    shortInterest,
    wiimReq,
    maSummaryReq,
  ]);

  if (tickerDetailResp?.quotes?.[symbolWithPoint]?.error?.code === 1) {
    return { notFound: true, errorCode: 410 };
  }

  const tickerDetails = tickerDetailResp?.tickerDetails?.find(item => !item.error);

  const tickerDetailsQuotes: Record<string, DelayedQuote> = {};
  if (tickerDetailResp?.quotes) {
    Object.keys(tickerDetailResp.quotes).forEach(key => {
      if (tickerDetailResp?.quotes?.[key] && !tickerDetailResp?.quotes?.[key]?.error) {
        tickerDetailsQuotes[key] = tickerDetailResp.quotes[key];
      }
    });
  }

  const originalLogoFilesPath = lensPath([0, 'files', 'mark_composite_light']);
  const logoUrl: string | null = logoResp?.ok ? view(originalLogoFilesPath, logoResp.ok) : null;

  const type = getCurrentSessionType(scheduleResp?.ok);
  const richQuoteData = richQuoteResp?.ok?.[symbol] ?? null;
  const shortInterestData = shortInterestResp?.ok?.[symbol]?.data || null;
  const wiim = wiimResp?.ok ?? null;

  // let cryptoNews: News[] = [];
  // if (isCrypto(richQuoteData)) {
  //   const cryptoNewsQuery = { tickers: [`$${symbol?.replace('/USD', '')}`] };
  //   const cryptoNewsRes = await safeTimeout(basicNewsManager.simplyQueryNews(cryptoNewsQuery), 3000);
  //   cryptoNews = cryptoNewsRes?.ok as News[] ?? [];
  // }

  const isStockReportSupportedRes = await safeAwait(
    globalSession.getManager(StockReportsManager).isSupportedTicker(symbol),
  );

  return {
    conferenceCallsSummary: conferenceCallsSummaryResp?.ok || null,
    dividendSummary: dividendSummaryResp?.ok || null,
    fundamentals: fundamentalsResp.ok && fundamentalsResp.ok.length ? fundamentalsResp.ok[0] : null,
    fundamentalsAnnual:
      fundamentalsAnnualResp.ok && fundamentalsAnnualResp.ok.length ? fundamentalsAnnualResp.ok[0] : null,
    isStockReportSupported: isStockReportSupportedRes.ok ?? false,
    logoUrl: logoUrl || null,
    maSummary: maSummaryResp?.ok || null,
    quotes: tickerDetailsQuotes,
    richQuoteData: richQuoteData,
    schedule: {
      schedule: scheduleResp?.ok || null,
      type: type as QuoteSessionType,
    },
    shortInterest: shortInterestData,
    splitsSummary: splitsDataResp?.ok || null,
    symbol,
    tickerDetails: tickerDetails || null,
    wiim: wiim,
  };
};

export interface QuoteProfileData extends Partial<Nullable<QuoteProfile>> {
  notFound?: boolean;
  errorCode?: number;
}

// TODO: Temporary isQuoteV2 prop
export const getQuoteProfile = async (
  symbol: string,
  isQuoteV2?: boolean,
  feedType?: string,
): Promise<QuoteProfileData> => {
  // console.log('Stop 1.001');
  const quoteData = await getQuoteData(symbol);

  if (quoteData.notFound) {
    console.log(`Quote not found for symbol: ${symbol}`);
    return quoteData;
  }

  if (symbol.includes('-')) {
    symbol = formatTickerInURLToTicker(symbol);
  }

  const session = getGlobalSession();
  const globalSession = getGlobalSessionSingleton();

  const calendarManager = session.getManager(CalendarManager);
  const autocompleteManager = session.getManager(AutocompleteManager);
  const scannerManager = session.getManager(ScannerManager);
  const chartManager = session.getManager(ChartManager);

  let rsiValue = 0;

  // console.log('Stop 1.1');
  // console.log('Stop 1.2');
  // const richQuoteResp = await quoteManager.getDelayedQuotes([symbol]);
  const quote = quoteData?.richQuoteData;
  if (quote?.type === 'CRYPTO') {
    return getCryptoData(quote);
  } else {
    // console.log('Stop 1.3');
    const rsiResponse = await safeTimeout(
      scannerManager.getInstrumentsWithIQuery({
        fields: ['symbol', 'rsi'],
        filtersAsString: `symbol_eq_${symbol}`,
        limit: 1,
        sortDir: ScannerProtos.SortDir.DESC,
      }),
      1000,
    );
    // const rsiResponse = await scannerManager.getInstrumentsWithIQuery({
    //   fields: ['symbol', 'rsi'],
    //   filtersAsString: `symbol_eq_${symbol}`,
    //   limit: 1,
    //   sortDir: ScannerProtos.SortDir.DESC,
    // });

    if (rsiResponse.err) {
      console.error(`Error getting RSI - Ticker ${symbol}`);
      rsiValue = 0;
    } else if (rsiResponse?.ok?.totalInstruments && rsiResponse?.ok?.totalInstruments > 0) {
      rsiValue = rsiResponse?.ok?.instruments?.[0].rsi ?? 0;
    }
  }

  const technicals = {
    rsi: rsiValue,
  };
  // console.log('Stop 1.4');
  const listings = autocompleteManager.getAutocompleteListing(symbol);

  const basicNewsManager = await session.getManager(BasicNewsManager);

  // const partnerContent = basicNewsManager.fetchNodes({
  //   displayOutput: 'full',
  //   page: 0,
  //   pageSize: 10,
  //   symbols: [symbol],
  //   tokenType: 'partner',
  //   type: feedType,
  // });

  const dateFrom = DateTime.fromJSDate(new Date()).minus({ years: 1 });
  const oneYearAgoString = dateFrom.toFormat('yyyy-MM-dd');
  // console.log('Stop 1.6');
  const pressContent = basicNewsManager.fetchNodes({
    dateFrom: oneYearAgoString,
    page: 0,
    pageSize: 10,
    sort: 'updated:desc',
    symbols: [symbol],
    tokenType: 'press-release',
    type: feedType,
  });
  // console.log('Stop 1.5');
  const conferenceCallsSummaryReq = safeAwait(getConferenceCallsSummary(symbol));
  const dividendSummaryReq = safeAwait(getDividendSummary(symbol));
  const earningsSummaryReq = safeAwait(getEarningsSummary(symbol));
  const ratingsSummaryReq = safeAwait(getRatingsSummary(symbol));
  const guidanceSummaryReq = safeAwait(getGuidanceSummary(symbol));
  //const ratings = calendarManager.getCalendarData('ratings', { symbols: [symbol] });
  const splitsData = calendarManager.getCalendarData('splits', { symbols: [symbol] });
  const guidanceData = calendarManager.getCalendarData('guidance', { symbols: [symbol] });
  // const sponsoredPosts = getSponsoredContentForTicker(symbol.toUpperCase());
  // console.log('Stop 1.7');
  const recentNewsQuery = {
    excludeAutomated: true,
    limit: isQuoteV2 ? 3 : 10,
    tickers: formatSymbols(symbol),
    type: feedType || ['story', 'benzinga_reach', 'marketbeat'],
  };
  // console.log('Stop 1.8');
  const formattedQuery = formatToSimpleNewsQueryParams(recentNewsQuery);
  const recentNewsRes = await safeTimeout(
    basicNewsManager.simplyQueryNews(formattedQuery.query, formattedQuery.options),
    3000,
  );
  // console.log('Stop 1.9');
  const sponsoredContentQuery = {
    excludeAutomated: true,
    limit: 10,
    tickers: formatSymbols(symbol),
    type: ['benzinga_reach'],
  };
  // console.log('Stop 1.10');
  const formattedSponsoredContentQuery = formatToSimpleNewsQueryParams(sponsoredContentQuery);
  // console.log('Stop 1.11');
  const sponsoredContentRes = await safeTimeout(
    basicNewsManager.simplyQueryNews(formattedSponsoredContentQuery.query, formattedSponsoredContentQuery.options),
    3000,
  );

  let postData: Promise<WordpressPage | null> | null = null;
  if (symbol.toLowerCase() === 'nnox') postData = (await getMoneyPostModule)(150264);
  else if (symbol.toLowerCase() === 'save') postData = (await getMoneyPostModule)(153726);
  else if (symbol.toLowerCase() === 'qs') postData = (await getMoneyPostModule)(153730);
  // console.log('Stop 1.12');
  const [
    conferenceCallsSummaryResp,
    dividendSummaryResp,
    earningsSummaryResp,
    guidanceSummaryResp,
    ratingsSummaryResp,
    listingsResp,
    //partnerContentResp,
    pressContentResp,
    postDataResp,
    //ratingsResp,
    splitsDataSet,
    guidanceResp,
    //sponsoredPostsResp,
  ] = await Promise.all([
    conferenceCallsSummaryReq,
    dividendSummaryReq,
    earningsSummaryReq,
    guidanceSummaryReq,
    ratingsSummaryReq,
    listings,
    //partnerContent,
    pressContent,
    postData,
    //ratings,
    splitsData,
    guidanceData,
    //sponsoredPosts,
  ]);

  //const partnersData = getPartnersData(partnerContentResp, symbol);
  const pressContentData = getPressContentData(pressContentResp, symbol);
  // console.log('Stop 1.13');
  const isStockReportSupportedRes = await safeAwait(
    globalSession.getManager(StockReportsManager).isSupportedTicker(symbol),
  );
  // console.log('Stop 1.14');
  const isPublicComSupported = await chartManager.isPublicComSupported(symbol);

  let etfFund: QuoteFund | null = null;
  if (quote?.type === 'ETF') {
    const fundResponse = await session.getManager(ETFManager).getFund(symbol);
    etfFund = fundResponse?.ok ? { ...fundResponse.ok, sector: null } : null;
    if (etfFund) {
      const sector =
        Array.isArray(etfFund?.equity_sector_allocation) && etfFund?.equity_sector_allocation.length > 0
          ? etfFund?.equity_sector_allocation.reduce((sectorA, sectorB) => {
              return sectorA?.percent > sectorB?.percent ? sectorA : sectorB;
            }).name
          : null;
      etfFund.sector = sector ?? 'Unknown';
    }
  }

  return {
    ...quoteData,
    conferenceCallsSummary: conferenceCallsSummaryResp?.ok || null,
    dividendSummary: dividendSummaryResp?.ok || null,
    earningsSummary: earningsSummaryResp?.ok || null,
    etfFund,
    guidance: guidanceResp?.ok || [],
    guidanceSummary: guidanceSummaryResp?.ok || null,
    initialRecentNews: recentNewsRes?.ok || [],
    isPublicComSupported: isPublicComSupported ?? false,
    isStockReportSupported: isStockReportSupportedRes.ok ?? false,
    listings: listingsResp?.ok?.result || null,
    //partnersRelease: partnersData || null,
    postData: postDataResp,
    pressRelease: pressContentData || null,
    //ratings: ratingsResp?.ok || null,
    ratingsSummary: ratingsSummaryResp?.ok || null,
    recentNewsQuery,
    splitsData: splitsDataSet?.ok || null,
    sponsoredPosts: sponsoredContentRes?.ok || [],
    technicals: technicals,
  };
};

export interface GroupedNewsI {
  date: string;
  articles: StoryObject[];
}

export const getQuoteNews = async (session: Session, query: NodeQueryParams): Promise<News[]> => {
  const basicNewsManager = session.getManager(BasicNewsManager);
  query.sort = 'created:desc';
  const response = await basicNewsManager.getNewsWithMultipleQueries([{ query: query }], query.pageSize ?? 20);
  const news = response?.ok?.[0]?.nodes ?? [];
  return news;
};

export const splitQuoteNewsByDate = (news: News[]): GroupedNewsI[] => {
  if (!Array.isArray(news)) return [];

  const filteredNews = news
    .filter((v, i, a) => a.findIndex(t => t.id === v.id) === i)
    .sort((a, b) => {
      return new Date(b.created).getTime() - new Date(a.created).getTime();
    });

  const groups = filteredNews.reduce((articles, article) => {
    const parsedDate = DateTime.fromJSDate(new Date(article.created));
    const date = parsedDate.setLocale(i18n.language).toFormat('EEEE, MMMM dd, yyyy');

    if (!articles[date]) {
      articles[date] = [];
    }

    articles[date].push(article);

    return articles;
  }, {});

  const groupedNews: GroupedNewsI[] = Object.keys(groups).map(date => {
    return {
      articles: groups[date],
      date,
    };
  });

  return groupedNews;
};

// const handler = async (req, res) => {
//   const data = await getQuoteData();
//   res.status(200).json(data);
// };

// export default handler;
