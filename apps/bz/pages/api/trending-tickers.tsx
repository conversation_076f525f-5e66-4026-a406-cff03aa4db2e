import { NextApiRequest, NextApiResponse } from 'next';
import { safeJsonFetch, SafeType } from '@benzinga/safe-await';
import { BASE_URL, TRENDING_TICKERS_TOKEN } from '../../src/env';

const ALLOWED_PARAMS = new Set(['date_from', 'date_to', 'timeframe']);

export interface TrendingTicker {
  security: {
    ticker: string;
    name: string;
    exchange: string;
  };
  count: number;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const url = new URL(`${BASE_URL}${req.url}` as string);
    const incomingParams = url.searchParams;
    const filteredParams = new URLSearchParams();

    for (const [key, value] of incomingParams.entries()) {
      if (ALLOWED_PARAMS.has(key)) {
        filteredParams.set(key, value.toLowerCase());
      }
    }

    filteredParams.set('token', TRENDING_TICKERS_TOKEN);

    const apiUrl = new URL('https://api.benzinga.com/api/v1/trending-tickers/list');
    apiUrl.search = filteredParams.toString();

    const data: SafeType<{ data: TrendingTicker[] }> = await safeJsonFetch(apiUrl.toString());

    res.setHeader('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
    res.status(200).json(data.ok || { data: [], ok: false });
  } catch (error) {
    console.error('Trending Tickers API error:', error);
    res.status(500).json({ error: 'Failed to fetch data from Trending Tickers API' });
  }
}
