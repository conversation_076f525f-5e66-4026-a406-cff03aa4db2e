import { getSheetToKVPair, getSheetToObjects, GoogleSheetsManager, Sheet } from '@benzinga/google-sheets-db';
import { getGlobalSession } from '../session';
import { SafeError, SafeType } from '@benzinga/safe-await';
import { Iter } from '@benzinga/iter';

const session = getGlobalSession();
const manager = session.getManager(GoogleSheetsManager);

let cache: { quoteFieldOptions: any; quoteFields: DataField[] } | undefined = undefined;
// let cachePreset: { preset: SafeType<DataFieldPreset>[] } | undefined = undefined;

export const setCache = async (
  quoteFields: Extract<SafeType<SafeType<DataField | undefined>[]>, { ok }>,
  quoteFieldOptions: Extract<SafeType<SafeType<{ key: string; value: Record<string, unknown> } | undefined>[]>, { ok }>,
) => {
  const obj: Record<string, Record<string, unknown>> = {};
  quoteFieldOptions.ok.forEach(row => {
    if (row.ok == null) {
      return;
    }
    obj[row.ok['key']] = row.ok['value'];
  });
  cache = {
    quoteFieldOptions: obj,
    quoteFields: quoteFields.ok.flatMap(row => (row.ok != null ? [row.ok] : [])),
  };
};

// export const setCachePreset = async (preset: SafeType<DataFieldPreset>[]) => {
//   cachePreset = {
//     preset,
//   };
// };

export const getCache = () => cache;
// export const getCachePreset = () => cachePreset;

interface DataField {
  name: string;
  label: string;
  labelShort: string;
  description?: string;
  updateFrequency: string;
  category: string;
  filterable: boolean;
  columnable: boolean;
  format: string;
  options?: string;
  permissions: string;
  source: string;
  sourceSupport: string;
  permissionsCom: string;
}

// interface DataFieldPreset {
//   ranking: string;
//   topic: string;
//   description: string;
//   filter: string;
//   sortField: string;
//   sortDir: string;
//   fields: string;
//   limit: string;
// }

const sheetToQuoteField = (sheet: Sheet): Iter<SafeType<DataField>> => {
  return sheet.apply(getSheetToObjects()).flatMap<SafeType<DataField>>(obj => {
    if (obj.err != null) {
      return [{ err: obj.err }];
    }
    if (obj.ok['Name'].length === 0) {
      return [];
    }
    const missingField = [
      'Label',
      'Label Short (12 Max)',
      'Update Frequency',
      'Category',
      'Filterable',
      'Columnable',
      'Format',
      'Permissions',
      'Source',
      'SourceSupport',
    ].find(field => obj.ok[field].length === 0);
    if (missingField) {
      return [
        {
          err: new SafeError(
            `Missing field ${missingField} in row ${obj.ok['Name']}`,
            `Missing field ${missingField} in row ${obj.ok['Name']}`,
            obj.ok,
          ),
        },
      ];
    }
    return [
      {
        ok: {
          category: obj.ok['Category'],
          columnable: obj.ok['Columnable'] === 'TRUE',
          description: obj.ok['Description'],
          filterable: obj.ok['Filterable'] === 'TRUE',
          format: obj.ok['Format'],
          label: obj.ok['Label'],
          labelShort: obj.ok['Label Short (12 Max)'],
          name: obj.ok['Name'],
          options: obj.ok['Options'],
          permissions: obj.ok['Permissions'],
          permissionsCom: obj.ok['PermissionsCom'],
          source: obj.ok['Source'],
          sourceSupport: obj.ok['SourceSupport'],
          updateFrequency: obj.ok['Update Frequency'],
        },
      },
    ];
  });
};
const sheetToQuoteFieldOptions = (sheet: Sheet): Iter<SafeType<{ key: string; value: Record<string, unknown> }>> => {
  return sheet
    .transpose()
    .apply(getSheetToKVPair())
    .flatMap<SafeType<{ key: string; value: Record<string, unknown> }>>(obj => {
      if (obj.err != null) {
        return [{ err: obj.err }];
      }
      if (obj.ok.value['label'] === 'value') {
        delete obj.ok.value['label'];
        return [{ ok: obj.ok }];
      } else if (obj.ok.value['label'] === 'json value') {
        delete obj.ok.value['label'];
        try {
          const new_obj: Record<string, any> = {};
          Object.entries(obj.ok.value).forEach(([key, value]) => {
            new_obj[key] = JSON.parse(value);
          });
          return [{ ok: { key: obj.ok.key, value: new_obj } }];
        } catch (e) {
          return [
            {
              err: new SafeError(
                'QuoteFieldOptions: Failed to parse JSON',
                'QuoteFieldOptions: Failed to parse JSON',
                obj.ok,
              ),
            },
          ];
        }
      }
      return [{ ok: obj.ok }];
    });
};

// const sheetToScreenerList = (sheet: Sheet): Iter<SafeType<DataFieldPreset>> => {
//   return (
//     sheet
//       // .transpose()
//       .apply(getSheetToObjects())
//       .flatMap<SafeType<DataFieldPreset>>((obj: Record<string, any>) => {
//         if (obj.err != null) {
//           return [{ err: obj.err }];
//         }
//         if (obj.ok['ranking'].length === 0) {
//           return [];
//         }
//         return [{ ok: obj.ok }];
//       })
//   );
// };

export const fetchData = async () => ({
  quoteFieldOptions: await manager
    .fetchCSV(
      new URL(
        'https://docs.google.com/spreadsheets/d/1rHyi8avLyfnKFt2_fosBaPjH5K6Kk2Ra4ee4ukFbwBk/pub?gid=707608318&single=true&output=csv',
      ),
    )
    .then(sheet => (sheet.err ? { err: sheet.err } : { ok: sheet.ok.apply(sheetToQuoteFieldOptions).collect() })),
  quoteFields: await manager
    .fetchCSV(
      new URL(
        'https://docs.google.com/spreadsheets/d/1rHyi8avLyfnKFt2_fosBaPjH5K6Kk2Ra4ee4ukFbwBk/pub?gid=1090117951&single=true&output=csv',
      ),
    )
    .then(sheet => (sheet.err ? { err: sheet.err } : { ok: sheet.ok.apply(sheetToQuoteField).collect() })),
});

// export const fetchDataPreset = async () => ({
//   preset: await manager
//     .fetchCSV(
//       new URL(
//         'https://docs.google.com/spreadsheets/d/1rHyi8avLyfnKFt2_fosBaPjH5K6Kk2Ra4ee4ukFbwBk/pub?gid=895563665&single=true&output=csv',
//       ),
//     )
//     .then(sheet => {
//       return sheet.err ? { err: sheet.err } : { ok: sheet.ok.apply(sheetToScreenerList).collect() };
//     }),
// });
