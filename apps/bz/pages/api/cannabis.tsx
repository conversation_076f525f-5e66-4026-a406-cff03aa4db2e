import { TradeIdeaManager } from '@benzinga/trade-ideas-manager';
import { InternalNewsManager } from '@benzinga/internal-news-manager';
import { getGlobalSession } from './session';
import { ContentManager, WordpressPage } from '@benzinga/content-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';

export const getProps = async () => {
  // Top
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const internalNewsManager = session.getManager(InternalNewsManager);
  const basicNewsManager = session.getManager(BasicNewsManager);

  const pageReq = await contentManager.getPage(232396);
  const sidebarDataReq = await contentManager.getWordpressPost(54194);
  const brokerWidgetReq = contentManager.getWordpressPost(154603);
  const chartbeatPopularStoriesReq = await basicNewsManager.getTopPostsUsingChartbeat({
    limit: 4,
    section: 'cannabis',
  });

  const cannabisTid = 123430;

  const latestNewsReq = internalNewsManager.getStories(
    {
      expression: ['Channels.tid', 'any', [cannabisTid]],
      operator: 'expression',
    },
    { excludeAutomated: true, limit: 4 },
  );

  const [pageDataRes, chartbeatPopularStories, sidebarData, brokerWidgetRes, latestNewsRes] = await Promise.all([
    pageReq,
    chartbeatPopularStoriesReq,
    sidebarDataReq,
    brokerWidgetReq,
    latestNewsReq,
  ]);

  const pageData = pageDataRes.ok as WordpressPage;

  const brokerWidget = brokerWidgetRes.ok;

  const taxonomies = {};
  pageData?.taxonomies?.map(taxonomy => {
    taxonomies[taxonomy.taxonomy] = taxonomies[taxonomy.taxonomy] || [];
    taxonomies[taxonomy.taxonomy].push(taxonomy);
  });

  // Get Sidebar Data & Relevant Tickers
  const moversBlock = sidebarData.ok?.blocks?.filter(block => {
    if (block.blockName === 'acf/stock-movers-compact') {
      return true;
    } else {
      return false;
    }
  });
  const tickers = moversBlock?.[0]?.attrs?.data?.tickers;

  const feed = session
    .getManager(TradeIdeaManager)
    .createFeed({ filter: 'ideas', limit: 3, tickers: tickers?.join(',') });
  const tradeIdeas = await feed.loadTradeIdeas();

  if (pageData?.blocks) {
    pageData.blocks = await loadServerSideBlockData(session, pageData.blocks);
  }

  return {
    brokerWidget,
    latestNews: latestNewsRes?.ok ?? [],
    latestNewsQuery: {
      channels: [cannabisTid],
      excludeAutomated: true,
      excludeSponsored: true,
    },
    meta: {
      ...pageData?.meta,
      dimensions: {
        authorName: pageData.meta?.author,
        contentType: 'channel',
      },
    },
    page: pageData,
    pageTargeting: { BZ_CHANNEL: 'cannabis' },
    parselyPopularStories: chartbeatPopularStories?.ok ?? [],
    taxonomies: taxonomies,
    tickers: tickers ? tickers : [],
    tradeIdeas: tradeIdeas.ok ?? [],
  };
};

const handler = async (_req, res) => {
  const data = await getProps();
  res.status(200).json(data);
};

export default handler;
