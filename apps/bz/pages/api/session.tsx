import {
  SESSION_ENV,
  SESSION_SERVER_ENV,
  SESSION_STAGING_ENV,
  SESSION_SERVER_STAGING_ENV,
  SESSION_DEV_ENV,
  SESSION_SERVER_DEV_ENV,
  SESSION_SANDBOX_ENV,
  SESSION_SERVER_SANDBOX_ENV,
} from '../../src/env';
import { AuthenticationManager, Session, createSession, getSessionSingleton } from '@benzinga/session';
import { getRuntimeEnvironment, isBetaSite, runningServerSide } from '@benzinga/utils';

export const getSessionEnvironment = () => {
  // if (runningServerSide()) {
  //   if (environment === 'development') return SESSION_SERVER_DEV_ENV;
  //   if (environment === 'staging') return SESSION_SERVER_STAGING_ENV;
  //   if (environment === 'sandbox' || isSandboxDomain()) return SESSION_SERVER_SANDBOX_ENV;
  //   return SESSION_SERVER_ENV;
  // } else {
  //   if (environment === 'development') return SESSION_DEV_ENV;
  //   if (['staging', 'sandbox'].includes(environment) || isStagingOrSandboxDomain()) return SESSION_STAGING_ENV;
  //   return SESSION_ENV;
  // }
  const environment = getRuntimeEnvironment();
  switch (environment) {
    case 'development':
      return runningServerSide() ? SESSION_SERVER_DEV_ENV : SESSION_DEV_ENV;
    case 'staging':
      if (isBetaSite() && !runningServerSide()) return SESSION_SANDBOX_ENV;
      return runningServerSide() ? SESSION_SERVER_STAGING_ENV : SESSION_STAGING_ENV;
    case 'sandbox':
      return runningServerSide() ? SESSION_SERVER_SANDBOX_ENV : SESSION_SANDBOX_ENV;
    default:
      return runningServerSide() ? SESSION_SERVER_ENV : SESSION_ENV;
  }
};

export const getGlobalSession = (): Session => createSession(getSessionEnvironment());

// WARNING: Use only for local packages, memory leak caution
export const getGlobalSessionSingleton = () => getSessionSingleton(getSessionEnvironment());

export const loadSession = async (benzinga_token?: string): Promise<Session> => {
  const session = createSession();
  if (benzinga_token) {
    session.getManager(AuthenticationManager).setBenzingaToken(benzinga_token);
    await session.getManager(AuthenticationManager).getAuthSession();
  }
  return session;
};
