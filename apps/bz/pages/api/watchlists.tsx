import { WatchlistManager } from '@benzinga/watchlist-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';

export const getWatchlistData = async (session, tickers: string[]) => {
  const watchlistRes = await session.getManager(WatchlistManager).getWatchlists();
  const symbols: string[] = [];
  watchlistRes?.ok?.forEach(portfolio => {
    portfolio.symbols.forEach(item => {
      symbols.push(item.symbol);
    });
  });
  const filtered = symbols.filter(value => tickers.includes(value));
  const unique = [...new Set(filtered)];
  const basicNewsManager = session.getManager(BasicNewsManager);
  const res = await basicNewsManager.getNewsWithMultipleQueries([{ query: { symbols: symbols } }], 4);
  let watchlistNews = [];
  if (res.ok) {
    watchlistNews = res.ok[0]?.nodes ?? [];
  }
  return {
    news: watchlistNews,
    symbols: unique,
    watchlists: watchlistRes.ok,
  };
};
