import {
  FeaturedVideos,
  FormattedVideoItem,
  formatToYouTubeVideoCard,
  VideosManager,
  YouTubeVideoWordpress,
} from '@benzinga/videos-manager';
import { getGlobalSession } from './session';
import { ContentManager, WordpressSidebar } from '@benzinga/content-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';

export interface PlaylistItem {
  title: string;
  playlistId: string;
  items: FormattedVideoItem[];
  type?: 'any' | 'video' | 'shorts';
}

export const getWatchPageProps = async () => {
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const sidebarReq = contentManager.getWordpressPost(333491);
  const featuredVideosReq = await session.getManager(VideosManager).getFeaturedVideos();

  const fetchMultiplePlaylists = async (playlists: PlaylistItem[]): Promise<PlaylistItem[]> => {
    const filteredPlaylists = playlists.filter(playlist => playlist.playlistId);
    const playlistPromises = filteredPlaylists.map(async playlist => {
      try {
        const items = await fetchPlaylist(playlist.playlistId, playlist.type);
        return { ...playlist, items };
      } catch (error) {
        console.error(`Error fetching playlist ${playlist.playlistId}, skipping:`, error);
        return playlist;
      }
    });

    return Promise.all(playlistPromises);
  };

  const fetchPlaylist = async (id: string, type: 'any' | 'video' | 'shorts' = 'any'): Promise<FormattedVideoItem[]> => {
    try {
      const playlistReq = await session.getManager(VideosManager).getYouTubePlaylist(id, { maxResults: 12, type });
      return playlistReq?.ok || [];
    } catch (error) {
      throw new Error(`Failed to fetch playlist ${id}`);
    }
  };

  const playlistsPlaceholders: PlaylistItem[] = [
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3bQSn91atO-3lPT4V6Nwsmh',
      title: 'All Access',
    },
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3ZVRPM7INpG--GvohnBVTnT',
      title: "Benzinga's PreMarket Playbook",
    },
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3YTdqniRmxrvz97w3IQ61rD',
      title: 'Webinars',
    },
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3buZI5529R53wJUNd9NS4_s',
      title: 'Earnings',
    },
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3YfEHxO1onTkEYBaGRY7kJA',
      title: 'YouTube Shorts',
      type: 'shorts',
    },
    {
      items: [],
      playlistId: 'PL4k0fH8EgI3ZvKhVTJjBjxm_4NbWpmVr_',
      title: 'Live Trading',
    },
    // The Trader Bacon Show
    {
      items: [],
      playlistId: '',
      title: 'The Trader Bacon Show',
    },
    // Futures Fever
    {
      items: [],
      playlistId: '',
      title: 'Futures Fever',
    },
  ];

  const playlists = await fetchMultiplePlaylists(playlistsPlaceholders);

  const [featuredVideosRes, sidebarRes] = await Promise.all([featuredVideosReq, sidebarReq]);

  const featuredVideosSections: FeaturedVideos = {
    live: [],
    recent: [],
    upcoming: [],
  };

  const featuredVideos = featuredVideosRes?.ok ?? [];
  const featuredVideosKeys = ['recent', 'live', 'upcoming'];
  featuredVideosKeys.forEach(key => {
    const featuredVideosArray = featuredVideos?.[key] || [];
    const result: YouTubeVideoWordpress[] = featuredVideosArray
      .filter(video => video.title !== 'Deleted video')
      .filter(video => video.title !== 'Private video');
    featuredVideosSections[key] = result ? result.map(formatToYouTubeVideoCard) : [];
  });

  if (Array.isArray(sidebarRes?.ok?.blocks)) {
    sidebarRes.ok.blocks = await loadServerSideBlockData(session, sidebarRes.ok.blocks);
  }

  return {
    featuredVideos: featuredVideosSections,
    pageTargeting: { BZ_CHANNEL: ['Watch Live Stock Market News'], BZ_PTYPE: 'website' },
    playlists,
    sidebar: (sidebarRes?.ok as WordpressSidebar) || null,
  };
};

const handler = async (_req, res) => {
  const data = await getWatchPageProps();
  res.status(200).json(data);
};

export default handler;
