import dayjs from 'dayjs';
import { getGlobalSession } from '../../pages/api/session';
import type { AppPageProps } from '../../src/entities/app';

import { BASE_URL } from '../../src/env';

import { safeFetch } from '@benzinga/safe-await';
import { BasicNewsManager, StoryObject } from '@benzinga/basic-news-manager';
import { QuoteProtos, ScannerManager } from '@benzinga/scanner-manager';
import { DelayedQuote, QuotesManager } from '@benzinga/quotes-manager';
import { Mover, MoversManager } from '@benzinga/movers-manager';
import { getRankingTableConfig, RankingColumn, RankingTable } from '@benzinga/blocks-utils';
import { ContentBlock } from '@benzinga/content-manager';

export interface MarketPageAPIProps extends AppPageProps {
  ok: MarketPageProps;
  err: string | null;
  status: number;
}

type RankingTableOverview = {
  slug: string;
  title: string;
  tickers: QuoteProtos.IQuote[];
};

type DelayedQuoteWithLogo = DelayedQuote & {
  logo?: string | null;
};

export interface MarketPageProps {
  marketPageTickers: MarketPageTickers;
  latestNews?: StoryObject[];
  rankings: RankingTableOverview[];
  movers?: Mover[];
  wiims?: StoryObject[];
  inContentBlock?: ContentBlock;
}

interface MarketPageTickers {
  commodities: string[] | DelayedQuoteWithLogo[];
  crypto: string[] | DelayedQuoteWithLogo[];
  etfs: string[] | DelayedQuoteWithLogo[];
  internationalIndices: string[] | DelayedQuoteWithLogo[];
  markets: string[] | DelayedQuoteWithLogo[];
  trending: string[] | DelayedQuoteWithLogo[];
}

const marketPageTickers: MarketPageTickers = {
  commodities: ['USO', 'GLD', 'SLV', 'CPER', 'UNG', 'CORN', 'WEAT', 'SOYB'],
  crypto: ['BTC/USD', 'ETH/USD', 'SOL/USD', 'DOGE/USD'],
  etfs: ['SPY', 'QQQ', 'VTI', 'ARKK'],
  internationalIndices: ['EWU', 'DAX', 'EWQ', 'EWJ', 'EWH', 'ASHR', 'INDA', 'EWA'],
  markets: ['SPY', 'DIA', 'QQQ', 'IWM'],
  trending: [],
};
const rankingTables = ['top-growth-stocks', 'top-value-stocks', 'top-momentum-stocks', 'top-quality-stocks'];

export const getMarketPageProps = async (): Promise<MarketPageAPIProps> => {
  try {
    const session = getGlobalSession();
    const basicNewsManager = session.getManager(BasicNewsManager);

    const rankingsConfigs = rankingTables.map(async table => {
      const config = getRankingTableConfig(table);
      config.scanner_query.limit = 4;
      const response = await session.getManager(ScannerManager).getInstrumentsWithIQuery(config.scanner_query);
      const rankings =
        response?.ok?.instruments?.map((ranking): QuoteProtos.IQuote => {
          const obj = ranking instanceof Object ? { ...ranking } : ranking;
          // if the ranking includes a field with format longPrice, convert it to a number
          config.table_columns.forEach((column: RankingColumn) => {
            if (column.format === 'longPrice' && obj[column.field]) {
              obj[column.field] = Number(obj[column.field]);
            }
          });
          Object.keys(obj).forEach(key => {
            if (obj[key] && typeof obj[key] === 'object') {
              obj[key] = { ...obj[key] };
            }
          });
          return obj;
        }) ?? [];
      config.rankings = rankings;
      config.slug = table;
      return config;
    });
    const rankings: RankingTable[] = await Promise.all(rankingsConfigs);

    const today = dayjs();
    const dayAgo = today.subtract(1, 'day').format('YYYY-MM-DD');
    const trendingTickers = await safeFetch(
      `${BASE_URL}/api/trending-tickers?timeframe=1h&date_from=${dayAgo}&date_to=${today.format('YYYY-MM-DD')}`,
    );
    const trendingTickersData = await trendingTickers.ok?.json();
    marketPageTickers.trending =
      trendingTickersData?.data?.slice(0, 8)?.map((ticker: any) => ticker.security.ticker) ?? [];

    const tickerQuotes = await session
      .getManager(QuotesManager)
      .getDelayedQuotes(Object.values(marketPageTickers).flat());

    for (const key in marketPageTickers) {
      let section = marketPageTickers[key];
      section = section.map(ticker => {
        const quote = tickerQuotes?.ok?.[ticker];
        if (quote && quote.error === undefined) {
          return {
            ...quote,
          } as DelayedQuoteWithLogo;
        }
        return typeof ticker === 'string' ? { symbol: ticker } : ticker;
      });
      marketPageTickers[key] = section;
    }

    const rankingsOverview = rankings.map((table: RankingTable) => {
      const title = table?.slug?.split('-')?.[1];
      const rankingKey = title + 'Percentile';
      const rankingQuotes = table.rankings.map(ticker => {
        ticker.rankingValue = ticker[rankingKey];
        return ticker;
      });
      return {
        slug: table.slug,
        tickers: rankingQuotes,
        title,
      } as RankingTableOverview;
    });

    const wiims = await basicNewsManager.simplyQueryNews({ tags: ['768599'] }, { limit: 4 });
    const movers = await session
      .getManager(MoversManager)
      .getMovers({ maxResults: 6, moversQuery: 'volume_gt_1000000' });

    let topMovers = movers?.ok ? [...(movers.ok.gainers ?? []), ...(movers.ok.losers ?? [])] : [];
    topMovers?.sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent));
    topMovers = topMovers.slice(0, 4);

    const props = {
      marketPageTickers,
      movers: topMovers,
      rankings: rankingsOverview,
      wiims: wiims?.ok || [],
    };

    return {
      err: null,
      ok: props,
      status: 200,
    };
  } catch (err) {
    console.log('Error fetching /market props', err);
    const props = {
      marketPageTickers,
      movers: [],
      rankings: [],
      wiims: [],
    };
    return {
      err: 'Error fetching /market props',
      ok: props,
      status: 500,
    };
  }
};

const handler = async (_req, res) => {
  const data = await getMarketPageProps();
  if (data.status === 200) {
    res.setHeader('Cache-Control', 'public, max-age=300, stale-while-revalidate=600');
  }
  res.status(data.status).json(data.ok);
};

export default handler;
