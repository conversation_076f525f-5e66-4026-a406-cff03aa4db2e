import { getGlobalSession } from '../../session';
import { CalendarManager, Dividend } from '@benzinga/calendar-manager';
import isPast from 'date-fns/isPast';
import parseISO from 'date-fns/parseISO';
import { DateTime } from 'luxon';

export interface DividendSummary {
  next: Dividend | null;
  last: Dividend | null;
  dividends: Dividend[];
  peersAverageDividend?: number;
  yearOverYearGrowth?: number | null;
  avgAnnualizedDividendGrowth?: number | null;
  largestDividendYield?: Dividend;
  sustainabilityData?: any;
}

export const fetchDividends = (symbol: string, session) => {
  const calendarManager = session.getManager(CalendarManager);
  try {
    return calendarManager.getCalendarData(
      'dividends',
      {
        pageSize: 1000,
        symbols: [symbol],
      },
      true,
    );
  } catch (error) {
    return { err: error, ok: [] };
  }
};

const getFutureDividends = (dividends: Dividend[]) => {
  return dividends?.filter((dividend: Dividend) => !isPast(parseISO(dividend.ex_dividend_date)));
};

const getPastDividends = (dividends: Dividend[]) => {
  return dividends?.filter((dividend: Dividend) => isPast(parseISO(dividend.ex_dividend_date)));
};

export const getNextDividendInfo = dividends => {
  const futureDividends = getFutureDividends(dividends);
  return futureDividends?.[0] ?? null;
};

const getLastDividendInfo = (dividends: Dividend[]) => {
  const pastDividends = getPastDividends(dividends);
  return pastDividends?.[0] ?? null;
};

const getYearOverYearGrowth = (dividends: Dividend[]) => {
  const dividendFrequency = dividends.find((dividend: Dividend) => dividend.frequency !== 0)?.frequency;

  if (!dividendFrequency) {
    return null;
  }

  const completedDividends = dividends?.filter(dividend => dividend.frequency !== 0).slice(0, dividendFrequency * 2);

  if (completedDividends.length < dividendFrequency * 2) {
    return null;
  }

  const yearOverYearDividends = completedDividends.reduce(
    (acc, curr, index) => {
      const dividend = curr.dividend;
      if (index < dividendFrequency) {
        acc.lastYear += Number(dividend);
      } else {
        acc.trailingYear += Number(dividend);
      }
      return acc;
    },
    { lastYear: 0, trailingYear: 0 },
  );

  const yearOverYearGrowth =
    (yearOverYearDividends.lastYear / dividendFrequency - yearOverYearDividends.trailingYear / dividendFrequency) /
    (yearOverYearDividends.trailingYear / dividendFrequency);

  if (!yearOverYearGrowth) {
    return null;
  }

  return yearOverYearGrowth * 100;
};

const get5YearAvgDividendGrowth = (dividends: Dividend[]) => {
  const completedDividends = dividends?.filter(dividend => dividend.frequency !== 0);
  const currentDividend = completedDividends?.[0];

  if (currentDividend === undefined) {
    return null;
  }

  const currentDate = DateTime.fromISO(currentDividend.ex_dividend_date);

  const lastDividend = completedDividends.find(d => {
    const dividendYear = DateTime.fromISO(d.ex_dividend_date).year;
    const dividendMonth = DateTime.fromISO(d.ex_dividend_date).month;
    if (dividendYear === currentDate.year - 5 && dividendMonth === currentDate.month) {
      return true;
    }
    return false;
  });

  if (lastDividend === undefined) {
    return null;
  }

  const dividendGrowth = (Number(currentDividend.dividend) / Number(lastDividend.dividend)) ** (1 / 5) - 1;
  return dividendGrowth * 100;
};

const getLargestDividendYield = (dividends: Dividend[]) => {
  const sortedDividends = [...dividends].sort((a, b) => Number(b.dividend_yield) - Number(a.dividend_yield));
  return sortedDividends[0];
};

const getDetailedData = (dividends: Dividend[]): DividendSummary => {
  return {
    avgAnnualizedDividendGrowth: get5YearAvgDividendGrowth(dividends) ?? null,
    dividends: dividends ?? [],
    largestDividendYield: getLargestDividendYield(dividends) ?? null,
    last: getLastDividendInfo(dividends),
    next: getNextDividendInfo(dividends),
    yearOverYearGrowth: getYearOverYearGrowth(dividends) ?? null,
  };
};

export const getDividendSummary = async (symbol: string): Promise<DividendSummary> => {
  const session = getGlobalSession();
  const dividendRes = await fetchDividends(symbol, session);
  const dividends = dividendRes?.ok ?? [];
  return await getDetailedData(dividends);
};

const handler = async (req, res) => {
  const summary = await getDividendSummary(req.query.ticker);
  res.status(200).json(summary);
};

export default handler;
