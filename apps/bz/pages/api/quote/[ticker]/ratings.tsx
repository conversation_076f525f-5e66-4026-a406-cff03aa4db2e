import { getGlobalSession } from '../../session';
import { CalendarManager, ISetParams, Ratings } from '@benzinga/calendar-manager';
import { SafePromise } from '@benzinga/safe-await';
import { Session } from '@benzinga/session';
import { DateTime } from 'luxon';

import { AnalystRatingsSummary, getDetailedData } from '@benzinga/calendar-manager';

const fetchAnalystRatings = (symbol: string, session: Session): SafePromise<Ratings[] | undefined> => {
  const dateFrom = DateTime.fromJSDate(new Date()).minus({ years: 3 }).toJSDate();

  const params: ISetParams = {
    dateFrom,
    fields:
      'fields=id,ticker,analyst_id,ratings_accuracy.smart_score,analyst_name,action_company,action_pt,adjusted_pt_current,adjusted_pt_prior,analyst,analyst_name,currency,date,exchange,id,importance,name,notes,pt_current,pt_prior,rating_current,rating_prior,ticker,time,updated,url,url_calendar,url_news,logo,quote',
    pageSize: 100,
    symbols: [symbol.toUpperCase()],
  };

  const calendarManager = session.getManager(CalendarManager);
  return calendarManager.getCalendarData('ratings', params, true);
};

export const getRatingsSummary = async (symbol: string): Promise<AnalystRatingsSummary> => {
  const session = getGlobalSession();
  const ratingRes = await fetchAnalystRatings(symbol, session);
  const ratings = ratingRes?.ok ?? [];
  return await getDetailedData(ratings);
};

const handler = async (req, res) => {
  const summary = await getRatingsSummary(req.query.ticker);
  res.status(200).json(summary);
};

export default handler;
