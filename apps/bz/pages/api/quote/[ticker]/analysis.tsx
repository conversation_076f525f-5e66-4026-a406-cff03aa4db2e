import { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession } from '../../session';
import { ScannerManager, ScannerProtos } from '@benzinga/scanner-manager';
import { Financials, SecuritiesManager } from '@benzinga/securities-manager';
import { QuoteAnalysisResponse, QuotesManager } from '@benzinga/quotes-manager';
import { DateTime } from 'luxon';
import { CalendarManager, Ratings } from '@benzinga/calendar-manager';
import { SafePromise } from '@benzinga/safe-await';

interface RatingsByMonthWithCounts {
  date: string;
  ratingBuyCount: number;
  ratingCountTotal: number;
  ratingHoldCount: number;
  ratingSellCount: number;
  ratingStrongBuyCount: number;
  ratingStrongSellCount: number;
}

const calculateRatingsByMonthWithCounts = (ratings: Ratings[]): RatingsByMonthWithCounts[] => {
  const ratingsByMonth: { [key: string]: Ratings[] } = {};
  ratings?.forEach(rating => {
    const dateNow = DateTime.fromJSDate(new Date());
    const ratingDateFormatted = DateTime.fromISO(rating.date).toFormat('LLL/yyyy');

    const sixMonthsAgo = dateNow.minus({ months: 6 });
    const ratingDate = DateTime.fromISO(rating.date);
    if (ratingDate < sixMonthsAgo) return;
    if (ratingsByMonth[ratingDateFormatted]) {
      ratingsByMonth[ratingDateFormatted].push(rating);
    } else {
      ratingsByMonth[ratingDateFormatted] = [rating];
    }
  });

  const ratingsByMonthWithCounts = Object.keys(ratingsByMonth).map(key => {
    const ratings = ratingsByMonth[key];
    const ratingStrongBuyCount = ratings.filter(rating => rating.rating_current === 'Strong Buy').length;
    const ratingBuyCount = ratings.filter(rating =>
      ['Buy', 'Outperform', 'Overweight'].includes(rating.rating_current),
    ).length;
    const ratingHoldCount = ratings.filter(rating =>
      ['Market Perform', 'Equal-Weight', 'Neutral'].includes(rating.rating_current),
    ).length;
    const ratingSellCount = ratings.filter(rating =>
      ['Sell', 'Underweight', 'Underperform'].includes(rating.rating_current),
    ).length;
    const ratingStrongSellCount = ratings.filter(rating => ['Strong Sell'].includes(rating.rating_current)).length;
    const ratingCountTotal =
      ratingStrongBuyCount + ratingBuyCount + ratingHoldCount + ratingStrongSellCount + ratingSellCount;
    return {
      date: key,
      ratingBuyCount,
      ratingCountTotal,
      ratingHoldCount,
      ratingSellCount,
      ratingStrongBuyCount,
      ratingStrongSellCount,
    };
  });

  ratingsByMonthWithCounts.sort((a, b) => {
    const aDate = DateTime.fromFormat(a.date, 'LLL/yyyy');
    const bDate = DateTime.fromFormat(b.date, 'LLL/yyyy');
    return aDate > bDate ? 1 : -1;
  });

  return ratingsByMonthWithCounts;
};

const calculateFundamentalRatings = (
  fundamentals: Financials | undefined,
): { financialDown: number; financialUp: number } => {
  if (
    !fundamentals?.earningReports?.[0]?.basicEps ||
    !fundamentals?.valuationRatios?.[0]?.peRatio ||
    !fundamentals?.valuationRatios?.[0]?.pbRatio ||
    !fundamentals?.operationRatios?.[0]?.roe ||
    !fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio
  ) {
    return { financialDown: 0, financialUp: 0 };
  }

  let financialUp = 0;
  let financialDown = 5;

  if (fundamentals?.earningReports?.[0]?.basicEps > 0.5) {
    financialUp++;
    financialDown--;
  }

  if (fundamentals?.valuationRatios?.[0]?.peRatio < 17) {
    financialUp++;
    financialDown--;
  }

  if (fundamentals?.valuationRatios?.[0]?.pbRatio < 1) {
    financialUp++;
    financialDown--;
  }

  if (fundamentals?.operationRatios?.[0]?.roe > 0.15) {
    financialUp++;
    financialDown--;
  }

  if (fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio < 2) {
    financialUp++;
    financialDown--;
  }
  return { financialDown, financialUp };
};

const calculateTechnicalRatings = (data: {
  richQuoteData: { lastTradePrice: number; fiftyTwoWeekLow: number; fiftyTwoWeekHigh: number };
  technicals: { rsi: number };
  ratingsByMonthWithCounts: RatingsByMonthWithCounts[];
}): { technicalDown: number; technicalUp: number } => {
  if (
    !data.technicals?.rsi ||
    !data.richQuoteData.lastTradePrice ||
    !data.richQuoteData.fiftyTwoWeekLow ||
    !data.richQuoteData.fiftyTwoWeekHigh ||
    !data.ratingsByMonthWithCounts?.length
  ) {
    return { technicalDown: 0, technicalUp: 0 };
  }

  let technicalUp = 0;
  let technicalDown = 0;
  let totalBuyRatings = 0;
  let totalSellRatings = 0;

  // Total the ratingBuyCount and ratingStrongBuyCount of each month.
  data.ratingsByMonthWithCounts.forEach(month => {
    totalBuyRatings += month?.ratingBuyCount + month?.ratingStrongBuyCount;
    totalSellRatings += month?.ratingSellCount + month?.ratingStrongSellCount;
  });

  if (totalBuyRatings > totalSellRatings) {
    technicalUp++;
  } else {
    technicalDown++;
  }

  if (data.technicals?.rsi < 70) {
    technicalUp++;
  } else {
    technicalDown++;
  }

  const yearLow = data.richQuoteData.fiftyTwoWeekLow;
  const yearHigh = data.richQuoteData.fiftyTwoWeekHigh;
  const yearMidpoint = (yearHigh + yearLow) / 2;

  if (data.richQuoteData.lastTradePrice > yearMidpoint) {
    technicalUp++;
  } else {
    technicalDown++;
  }

  return { technicalDown, technicalUp };
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<QuoteAnalysisResponse | { error: string }>,
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check origin
  // const origin = req.headers.origin;
  // const allowedOrigins = [
  //   'https://www.benzinga.com',
  //   'https://pro.benzinga.com',
  //   process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : null,
  // ].filter(Boolean);

  // if (origin && !allowedOrigins.includes(origin)) {
  //   return res.status(403).json({ error: 'Forbidden' });
  // }

  const symbol = req.query.ticker as string;

  if (!symbol) {
    return res.status(400).json({ error: 'Symbol is required' });
  }

  try {
    const session = getGlobalSession();
    const securitiesManager = session.getManager(SecuritiesManager);
    const scannerManager = session.getManager(ScannerManager);
    const quoteManager = session.getManager(QuotesManager);

    const quoteResp = await quoteManager.getDelayedQuotes([symbol]);
    const quote = quoteResp?.ok?.[symbol];

    if (!quote) {
      return res.status(404).json({ error: 'Quote not found' });
    }

    let rsi = 0;
    try {
      const rsiResponse = await scannerManager.getInstrumentsWithIQuery({
        fields: ['symbol', 'rsi'],
        filtersAsString: `symbol_eq_${symbol}`,
        limit: 1,
        sortDir: ScannerProtos.SortDir.DESC,
      });
      rsi = rsiResponse?.ok?.instruments?.[0]?.rsi ?? 0;
    } catch (error) {
      console.error('Error getting RSI', error);
    }

    const fundamentalsResp = await securitiesManager.getFinancials({
      period: '3M',
      symbols: symbol,
    });
    const fundamentals = fundamentalsResp?.ok?.[0];

    const calendarManager = session.getManager(CalendarManager);
    const ratingsData = await calendarManager.getCalendarData('ratings', { symbols: [symbol] });
    const ratingsByMonthWithCounts = calculateRatingsByMonthWithCounts(ratingsData?.ok || []);

    const { financialDown, financialUp } = calculateFundamentalRatings(fundamentals);
    const { technicalDown, technicalUp } = calculateTechnicalRatings({
      ratingsByMonthWithCounts,
      richQuoteData: quote,
      technicals: { rsi },
    });

    const technicalAnalysisRating = Math.floor((technicalUp / (technicalDown + technicalUp)) * 100);
    const financialAnalysisRating = Math.floor((financialUp / (financialDown + financialUp)) * 100);
    const ratingsExist = !!technicalAnalysisRating && !!financialAnalysisRating;

    const response: QuoteAnalysisResponse = {
      fundamentals: {
        down: financialDown,
        metrics: {
          basicEps: fundamentals?.earningReports?.[0]?.basicEps ?? null,
          debtEquityRatio: fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio ?? null,
          pbRatio: fundamentals?.valuationRatios?.[0]?.pbRatio ?? null,
          peRatio: fundamentals?.valuationRatios?.[0]?.peRatio ?? null,
          roe: fundamentals?.operationRatios?.[0]?.roe ?? null,
        },
        up: financialUp,
      },
      isComplete: ratingsExist,
      technicals: {
        down: technicalDown,
        rsi,
        up: technicalUp,
      },
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching analysis:', error);
    res.status(500).json({ error: 'Failed to fetch analysis data' });
  }
}
