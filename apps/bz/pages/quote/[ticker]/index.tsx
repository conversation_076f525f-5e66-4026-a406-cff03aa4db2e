import React from 'react';
import Head from 'next/head';
import { DateTime } from 'luxon';
import styled from '@benzinga/themetron';

import { MetaProps } from '@benzinga/seo';

import QuoteTab from '../../../src/components/Quote/QuoteTab';
import { addLocalUserRecentTicker } from '@benzinga/quotes-manager';

import { AppContext } from '../../../src/ContextApi/context';
import { DFPManager } from 'react-dfp';
import { PageType, Schema } from '@benzinga/seo';

import { StockSymbol } from '@benzinga/session';

import { Layout } from '@benzinga/core-ui';

import { CryptoHeader } from '../../../src/components/Quote/CryptoHeader';
import { CryptoProfile } from '../../../src/components/Quote/CryptoProfile';
import { QuoteLayout } from '../../../src/components/Quote/QuoteLayout';
import { QuoteProfile as QuoteProfileInterface, ScheduleProps } from '../../../src/entities/quoteEntity';
import { QuoteProfileData, getQuoteProfile } from '../../api/quote';
import { getGlobalSession } from '../../api/session';

import QuoteProfile from '../../../src/components/Quote/QuoteProfile';

import { ContentManager, WordpressSidebar, WordpressPost } from '@benzinga/content-manager';
import { SessionContext } from '@benzinga/session-context';
import { QuotesManager } from '@benzinga/quotes-manager';
import { SafeType } from '@benzinga/safe-await';
import { formatTickerInURLToTicker } from '@benzinga/utils';

const DefaultSidebar = React.lazy(() => import('../../../src/components/Sidebars/DefaultSidebar'));

export interface QuotePageProps {
  activeTab: string;
  profile: QuoteProfileInterface;
  symbol: string;
  sidebar: WordpressSidebar;
  metaProps: MetaProps;
}

export interface AppContextProps {
  schedule: ScheduleProps;
  symbol: StockSymbol;
  profile: QuoteProfileInterface;
}

const zone = 'America/Detroit';
const now = DateTime.now().setZone(zone);

const DATE_CREATED = '2019-01-01T01:01:01';
const DATE_UPDATED = now.toISO();

const generateMetaDescription = (
  symbol: string,
  companyName: string,
  exchange: string,
  activeTab: string,
  isStock: boolean,
  linkBackToPublic = false,
) => {
  let description = symbol;

  if (exchange) {
    description = `${exchange}: ${description}`;
  }

  if (companyName) {
    description = `${companyName}${isStock ? ' Stock' : ''} (${description})`;
  }

  if (linkBackToPublic) {
    description = `<a href="https://public.com/stocks/${symbol}">${description}</a>`;
    companyName = `<a href="https://public.com/stocks/${symbol}">${companyName}</a>`;
  }

  switch (activeTab) {
    case 'profile':
    case null:
      return `${description} stock price, news, charts, stock research, profile.`;
    case 'analyst-ratings':
      return `Analysts publish ratings and price targets on most stocks. Benzinga tracks 120 analyst firms so investors can understand if analysts expect a stock to trade higher or lower. Ratings are directional and typically buy, sell or hold. Price Targets are an analyst's best guess at where the stock will trade in 12 months.`;
    case 'guidance':
      return `Companies issue guidance, or expectations of future EPS and revenue, so investors have a sense of how much money a company will make in a future period.`;
    case 'dividends':
      return `The dividend schedule below includes dividend amounts, payment dates and ex-dividend dates for ${companyName}. ${companyName} issues dividends to shareholders from excess cash the company generates. Most companies pay dividends on a quarterly basis, but dividends may also be paid monthly, annually or at irregular intervals.`;
    case 'earnings':
      return `${companyName} reports earnings on a quarterly basis. These quarterly earnings reports give investors a glimpse into financial results from a company for a 3 month period. Earnings reports almost always include EPS and Revenue results.`;
    case 'key-statistics':
      return `${description} analysis, key statistics.`;
    case 'short-interest':
      return `Short interest for ${companyName} gives investors a sense of the degree to which investors are betting on the decline of ${companyName}'s stock. Short interest data is updated every two weeks.`;
    case 'insider-trades':
      return `The table below includes insider transactions amounts, trade dates and executives completing transactions. ${companyName} insider trades include the CEO, CFO, board of directors and is updated today.`;
    default:
      return description;
  }
};

export const getQuoteTitle = (symbol: string, companyName: string, exchange: string, isStock: boolean) => {
  let title = '';
  if (exchange) {
    title = `${companyName}${isStock ? ' Stock' : ''} (${exchange}:${symbol})`;
  } else {
    title = `${companyName}${isStock ? ' Stock' : ''} (${symbol})`;
  }
  return title;
};

export const generateMetaTitle = (
  symbol: string,
  companyName: string,
  exchange: string,
  activeTab: string,
  isStock: boolean,
) => {
  const title = getQuoteTitle(symbol, companyName, exchange, isStock);

  switch (activeTab) {
    case null:
      return `${title}, Quotes and News Summary`;
    case 'profile':
      return `${title}, Quotes and News Summary`;
    case 'analyst-ratings':
      return `${title}, Analyst Ratings, Price Targets, Predictions`;
    case 'guidance':
      return `${title}, Guidance and Forecast`;
    case 'dividends':
      return `${title} Dividends: History, Yield and Dates`;
    case 'earnings':
      return `${title} Earnings Dates and Earning Calls`;
    case 'news':
      return `Latest News for ${title}`;
    case 'ideas':
      return `${title}, Trade Ideas`;
    case 'key-statistics':
      return `${title}, Key Statistics`;
    case 'short-interest':
      return `${title}, Short Interest Report`;
    case 'insider-trades':
      return `${title} Insider Trades`;
    default:
      return title;
  }
};

const generateMetaSubTitle = (companyName: string, activeTab: string) => {
  switch (activeTab) {
    case 'dividends':
      return `Historical and Upcoming ${companyName} Dividends`;
    case 'insider-trades':
      return `Historical ${companyName} Insider Trades, Insider Buys and Sells`;
    default:
      return '';
  }
};

const getStructuredData = (symbol, activeTab, richQuoteData, tickerDetails, canonical) => {
  const keywords = [`"symbol: ${symbol}"`];

  if (activeTab) {
    keywords.push(`"section: ${activeTab}"`);
  }

  const structuredData = {
    keywords: keywords,
  };

  if (richQuoteData?.companyStandardName) {
    structuredData['mainEntity'] = corporationSchema(symbol, richQuoteData, tickerDetails, canonical);
  }

  return {
    keywords: keywords,
  };
};

export const getCryptoMetaInfo = (profile): MetaProps => {
  const name = profile?.cryptoData?.name ?? profile?.richQuoteData?.name ?? profile?.richQuoteData?.description;
  const keywords = [`"symbol: ${profile?.symbol}"`, `"section: Crypto"`];

  const urlFriendlySymbol = profile?.symbol?.replace('/', '-');
  // Testing SEO, will remove after swapping all URLs
  const canonical = `https://www.benzinga.com/quote/${urlFriendlySymbol}`;
  return {
    author: 'Benzinga',
    canonical,
    dateCreated: DATE_CREATED,
    dateUpdated: DATE_UPDATED,
    description: `${name} crypto price, news, charts, data, profile.`,
    image: profile?.logoUrl || null,
    pageType: PageType.Ticker,
    structuredData: {
      keywords: keywords,
    },
    title: `${name} (${profile?.symbol}) Crypto Price, News and Market Data`,
  };
};

export const getStockMetaInfo = (
  profile: QuoteProfileData,
  activeTab: string,
  linkBackToPublic: boolean,
): MetaProps => {
  const type = profile?.richQuoteData?.type;
  const isStock = type === 'STOCK';

  const companyName = (profile?.richQuoteData && profile.richQuoteData?.name) || '';
  const exchange = (profile?.richQuoteData && profile.richQuoteData.bzExchange) || '';

  const urlFriendlySymbol = profile?.symbol?.replace('/', '-');
  // Testing SEO, will remove after swapping all URLs
  let canonical = `https://www.benzinga.com/quote/${urlFriendlySymbol}`;
  if (activeTab && activeTab !== 'profile') {
    canonical = `${canonical}/${activeTab}`;
  }

  return {
    author: 'Benzinga',
    canonical,
    dateCreated: DATE_CREATED,
    dateUpdated: DATE_UPDATED,
    description: generateMetaDescription(
      profile?.symbol ?? '',
      companyName,
      exchange,
      activeTab,
      isStock,
      linkBackToPublic,
    ),
    image: profile?.logoUrl || null,
    pageType: PageType.Ticker,
    robots: null,
    structuredData: getStructuredData(
      profile?.symbol,
      activeTab,
      profile?.richQuoteData,
      profile?.tickerDetails,
      canonical,
    ),
    subTitle: generateMetaSubTitle(companyName, activeTab),
    title: generateMetaTitle(profile?.symbol ?? '', companyName, exchange, activeTab, isStock),
  };
};

export const metaInfo = (profile, activeTab, linkBackToPublic = true): MetaProps => {
  if (profile?.richQuoteData?.type === 'CRYPTO') {
    return getCryptoMetaInfo(profile);
  } else {
    return getStockMetaInfo(profile, activeTab, linkBackToPublic);
  }
};

export const retrieveQuoteProfileNamingData = (
  profile: QuoteProfileData,
):
  | {
      companyName: string;
      companyStandardName: string;
      exchange: string;
      isStock: boolean;
      symbol: string;
      type: string;
    }
  | Record<string, never> => {
  if (!profile) {
    return {};
  }

  const { richQuoteData, symbol: profileSymbol } = profile;

  const type = richQuoteData?.type || '';
  const isStock = type === 'STOCK';

  const companyName = richQuoteData?.name || '';
  const companyStandardName = richQuoteData?.companyStandardName || '';
  const exchange = richQuoteData?.bzExchange || '';
  const symbol = profileSymbol ?? '';

  return {
    companyName,
    companyStandardName,
    exchange,
    isStock,
    symbol,
    type,
  };
};

export const getPageTitle = (profile: QuoteProfileData, activeTab: string) => {
  if (!profile) return '';

  const { companyName, exchange, isStock, symbol, type } = retrieveQuoteProfileNamingData(profile);
  const tab = type === 'CRYPTO' ? '' : activeTab;

  return generateMetaTitle(symbol, companyName, exchange, tab, isStock);
};

const corporationSchema = (symbol, richQuoteData, tickerDetails, canonical) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Corporation',
    description: tickerDetails?.[0]?.company?.longDescription ?? '',
    name: richQuoteData?.companyStandardName ?? '',
    tickerSymbol: symbol,
    url: canonical ?? '',
  };
};

export const getActiveTab = query => {
  return query && query.length > 1 ? query[1] : null;
};

interface QuotePageContentProps {
  activeTab: string;
  profile: QuoteProfileInterface;
  symbol: string;
  sidebar?: WordpressSidebar | null;
  tabKey: string;
}

export const QuotePageContent: React.FC<React.PropsWithChildren<QuotePageContentProps>> = ({
  activeTab,
  children,
  profile,
  sidebar,
  symbol,
  tabKey,
}) => {
  const session = React.useContext(SessionContext);

  React.useEffect(() => {
    const quotesManager = session.getManager(QuotesManager);
    quotesManager.getDelayedQuotes([symbol]);
    setInterval(() => {
      quotesManager.getDelayedQuotes([symbol]);
    }, 60000);

    // Taboola Consent Script
    if (window?.performance && typeof window?.performance?.mark == 'function') {
      window?.performance?.mark('tbl_ic');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    DFPManager.setTargetingArguments({ BZ_PTYPE: 'ticker' });
  }, []);

  if (typeof window !== 'undefined') {
    addLocalUserRecentTicker(symbol);
  }

  const metaInfoData = metaInfo(profile, activeTab);

  if (profile?.richQuoteData?.type === 'CRYPTO') {
    return (
      <QuotePageContainer>
        <Layout
          layoutHeader={<CryptoHeader profile={profile} symbol={symbol} />}
          layoutMain={<CryptoProfile profile={profile} symbol={symbol} />}
          layoutSidebar={
            <React.Suspense>
              <DefaultSidebar showWatchlistWidget={false} sidebar={sidebar} />
            </React.Suspense>
          }
        />
      </QuotePageContainer>
    );
  }

  return (
    <QuotePageContainer>
      <Head>
        {/* Here for a Zacks deal. Needs to be on Quotes pages only */}
        <script id="googletagscript" key="googletagscript">
          {`window.googletag = window.googletag || { cmd: [] };`}
        </script>
        <link as="script" href="https://ad.wsod.com/site/391c5ac9e899a3f96ad04ca51ea98cc2/0.0.async/" rel="preload" />
        <script async key="wsod script" src="https://ad.wsod.com/site/391c5ac9e899a3f96ad04ca51ea98cc2/0.0.async/" />
      </Head>
      <Schema
        data={corporationSchema(symbol, profile.richQuoteData, profile.tickerDetails, metaInfoData.canonical)}
        name="corporation-schema"
      />
      <AppContext.Provider
        value={{
          activeTab,
          profile,
          schedule: profile.schedule,
          symbol,
        }}
      >
        <QuoteLayout
          activeTab={activeTab}
          metaInfoData={metaInfoData}
          profile={profile}
          symbol={symbol}
          tabKey={tabKey}
        >
          {children}
        </QuoteLayout>
      </AppContext.Provider>
    </QuotePageContainer>
  );
};

const QuotePage = ({ activeTab, metaProps, profile, sidebar, symbol }: QuotePageProps) => {
  return (
    <QuotePageContent activeTab={activeTab} profile={profile} sidebar={sidebar} symbol={symbol} tabKey={activeTab}>
      <QuoteTab description={metaProps.description} title={getPageTitle(profile, activeTab)}>
        <QuoteProfile postData={profile.postData} profile={profile} symbol={symbol} />
      </QuoteTab>
    </QuotePageContent>
  );
};

export const getServerSideProps = async ({ query, res }) => {
  let symbol = query.ticker?.toUpperCase();
  // const relatedSymbol = query.ticker;
  // const activeTab = getActiveTab(query?.ticker);
  // const tab = 'profile';
  const activeTab = 'profile';

  // const redirect = getRedirect(symbol, relatedSymbol, tab);
  // if (redirect) {
  //   return redirect;
  // }

  if (symbol?.includes(':')) {
    symbol = symbol.split(':')[1];
    return {
      redirect: {
        destination: `/quote/${symbol}`,
        permanent: true,
      },
    };
  }

  if (symbol?.includes('-')) {
    symbol = formatTickerInURLToTicker(symbol);
  }

  const profile = await getQuoteProfile(symbol);

  if (profile.notFound === true) {
    return { notFound: true };
  }

  const { richQuoteData } = profile;

  let headerProps = {};
  let sidebar: SafeType<WordpressPost> | null = null;

  if (richQuoteData?.type === 'CRYPTO') {
    headerProps = {
      logoVariant: 'crypto',
    };

    const contentManager = getGlobalSession().getManager(ContentManager);
    sidebar = await contentManager.getWordpressPost(59458);
  }

  if (typeof res.setHeader === 'function') {
    const sanitizedSymbol = symbol.replace(/[\x00-\x1f\x7f-\xff]/g, '');
    if (sanitizedSymbol) {
      res.setHeader('Surrogate-Key', sanitizedSymbol);
    }
  }

  return {
    props: {
      activeTab: activeTab,
      headerProps: headerProps,
      metaProps: metaInfo(profile, activeTab),
      profile: profile,
      sidebar: sidebar?.ok ?? null,
      symbol: symbol,
    } as QuotePageProps,
  };
};

const QuotePageContainer = styled.div`
  .section-title {
    margin-bottom: 1rem;
  }
  .quote-news-menu {
    position: relative;
    min-height: 500px;

    .tab-list-wrapper {
      overflow-x: auto;
    }

    .trading-view-widget-wrapper {
      min-height: 400px;
    }

    li {
      button {
        font-size: 1.4rem;
        font-weight: normal;
      }
    }
  }
`;

export default QuotePage;
