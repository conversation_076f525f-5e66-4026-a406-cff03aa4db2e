import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { MetaProps, StructuredDataI, PageType } from '@benzinga/seo';
import { ContentManager, formatTermsSurrogateKeys, Term } from '@benzinga/content-manager';
import { combineStoryObjectArrays, filterDuplicateArticles, StoryObject } from '@benzinga/advanced-news-manager';
import { toTitleCase } from '@benzinga/utils';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { PageProps, moneyMetaInfo, injectBlockInLayout, generateTargeting } from '@benzinga/money';
import { safeErrorStatus, SafeType, safeTimeout } from '@benzinga/safe-await';
import { DateTime } from 'luxon';
import Error from './_error';
import { getTermByPath } from './api/term';
import { getGlobalSession } from './api/session';
import { ToolsPageMain } from './tools-bak';
import { getTaboolaBlock } from '@benzinga/ads-utils';
import { EntitiesTemplate, MoneyPostTemplate, MoneyPageTemplate, NewsTemplate } from '@benzinga/money';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { ArticleManager } from '@benzinga/article-manager';

const DefaultSidebar = React.lazy(() => import('../src/components/Sidebars/DefaultSidebar'));

const shouldTopicPage410 = news => {
  if (Array.isArray(news) && news.length < 25 && news[0]?.createdAt < DateTime.fromISO('2025-01-01').toISO()) {
    return true;
  }

  return false;
};

export const getStructuredData = (term: Term): StructuredDataI => {
  const structuredData = { pageType: 'WebPage' };
  if (term.vid === '1') {
    structuredData['keywords'] = [
      `"category: ${term?.name?.replace(/"/g, '&quot;')}"`,
      // `"PageIsBzPro: BZ"`
    ];
  }
  if (term.vid === '3') {
    structuredData['keywords'] = [
      `"tag: ${term?.name?.replace(/"/g, '&quot;')}"`,
      // `"PageIsBzPro: BZ"`
    ];
  }
  return structuredData;
};

const Page: NextPage<PageProps> = props => {
  if (props.error) {
    return (
      <Error statusCode={props.error ?? 503} title={props.error === 410 ? 'Content Removed' : 'Error'} />
    ) as React.ReactElement;
  }

  if (props.template == 'money') return <MoneyPostTemplate {...props} />;

  if (props.template == 'page')
    return (
      <MoneyPageTemplate
        {...props}
        layoutFooter={
          props.brokerWidget && (
            <>
              <h2>Explore Benzinga&apos;s Financial Tools</h2>
              <ToolsPageMain brokerWidget={props.brokerWidget} />
            </>
          )
        }
      />
    );

  if (props.template == 'entities') return <EntitiesTemplate {...props} />;

  if (props.template == 'topic') {
    return (
      <NewsTemplate
        {...props}
        disableDefaultAd={true}
        layoutSidebar={
          <div>
            <React.Suspense>
              <DefaultSidebar />
            </React.Suspense>
          </div>
        }
        showCommentsIcon={true}
      />
    );
  }

  if (props.template == 'channel') {
    return (
      <NewsTemplate
        {...props}
        layoutFooter={
          props.brokerWidget && (
            <div className="px-4">
              <h2>Explore Benzinga&apos;s Financial Tools</h2>
              <ToolsPageMain brokerWidget={props.brokerWidget} />
            </div>
          )
        }
        layoutSidebar={
          <div>
            <React.Suspense>
              <DefaultSidebar />
            </React.Suspense>
          </div>
        }
      />
    );
  }

  return (<Error statusCode={404} />) as React.ReactElement;
};

export const termMetaInfo = (term: Term): MetaProps => {
  // Testing SEO, will remove after swapping all URLs
  const canonical = `https://www.benzinga.com/${term.url_public}`;
  // ToDo: Allow page, dispaly title overrides via the CMS
  const termTitle =
    term?.name?.toLowerCase() === 'news'
      ? `Latest Stock Market News and Breaking Headlines`
      : `${toTitleCase(term?.name) || 'Benzinga'} - Latest News and breaking headlines`;
  return {
    author: 'Benzinga',
    canonical,
    description:
      'Stock Market Quotes, Business News, Financial News, Trading Ideas, and Stock Research by Professionals.',
    dimensions: {
      contentType: term.vid === '3' ? PageType.Channel : PageType.Topic,
    },
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: term.vid === '3' ? PageType.Channel : PageType.Topic,
    structuredData: getStructuredData(term),
    title: termTitle,
  };
};

export const getServerSideProps: GetServerSideProps = async ({ query: { slug: querySlug }, req, res }) => {
  let slug = '';
  if (Array.isArray(querySlug)) {
    slug = sanitizeHTML(querySlug.join('/'));
  } else if (typeof querySlug === 'string') {
    slug = sanitizeHTML(querySlug as string);
  }
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);

  try {
    // Check for Money Page
    const postResponse = await contentManager.getPageWithPath(slug);
    const postData = postResponse.ok;

    const responseCode = (postResponse?.err?.data as safeErrorStatus)?.status;

    if (responseCode && responseCode === 410 && postResponse?.ok === undefined) {
      //Just for 410 // cannabis clean up
      //404 response is expected for other pages to check for topic and channel
      res.statusCode = responseCode;
      return {
        props: {
          error: 410,
          news: [],
          topNews: [],
          topic: '',
        },
      };
    }

    if (postData && postData?.success !== false && postData.blocks?.length) {
      if (Array.isArray(postData?.blocks)) {
        postData.blocks = await loadServerSideBlockData(session, postData?.blocks, req.headers, req.cookies);
      }

      if (Array.isArray(postData?.sidebar?.blocks)) {
        postData.sidebar.blocks = await loadServerSideBlockData(
          session,
          postData.sidebar.blocks,
          req.headers,
          req.cookies,
        );
      }

      if (postData?.template !== 'channel') {
        const targeting = postData?.template ? generateTargeting(postData, postData?.template) : {};
        const brokerWidgetRes = postData?.layout?.settings?.hide_footer
          ? await contentManager.getWordpressPost(154603)
          : null;

        return {
          props: {
            brokerWidget: brokerWidgetRes?.ok || null,
            headerProps: {
              hideBanner: true,
              hideFooter: !!postData?.layout?.settings?.hide_site_footer,
              hideNavigationBar: !!postData?.layout?.settings?.hide_navigation_bar,
              hideQuoteBar: !!postData?.layout?.settings?.hide_quotes_searchbar,
              hideSearchBar: true,
              isMainDrawerVisible: true,
            },
            metaProps: moneyMetaInfo(postData),
            pageTargeting: targeting,
            post: postData,
            template: postData?.template ?? 'money',
          },
        };
      }
    }

    if (Array.isArray(postData?.sidebar?.blocks)) {
      postData.sidebar.blocks = await loadServerSideBlockData(
        session,
        postData.sidebar.blocks,
        req.headers,
        req.cookies,
      );
    }

    // News Listing Page
    let featuredNewsResponse: SafeType<StoryObject[]> | null = null;
    let newsResponse: SafeType<StoryObject[]> | null = null;
    let featuredNews: StoryObject[] = [];

    const result = await getTermByPath(`${slug}`);

    if (!result?.ok || result?.ok?.response_code === 400) {
      res.statusCode = 404;
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    const term = result?.ok?.data && result.ok.data[0];

    const metaProps = postData ? moneyMetaInfo(postData) : termMetaInfo(term);

    let template = '';

    let pageTargeting = {};

    const featuredNewsLimit = term.tid === '2' ? 3 : 7;

    if (term.vid === '1') {
      template = 'channel';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: [138079], primaryChannel: term.tid },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            excludeAutomated: true,
            limit: featuredNewsLimit,
          },
        ),
        3000,
      );
      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: [term.tid] },
          {
            excludeAutomated: true,
            limit: 20,
          },
        ),
        3000,
      );
    }

    const isIsraelPage = term?.tid === '21365';

    if (term.vid === '3') {
      template = 'topic';

      if (slug) {
        try {
          const articleManager = session.getManager(ArticleManager);
          const expiredUrlCheck = await articleManager.checkExpiredUrl('topic', slug);
          if (expiredUrlCheck.ok?.status === 'success' && expiredUrlCheck.ok?.data) {
            res.statusCode = 410;
            return {
              props: {
                error: 410,
                featuredNews: [],
                headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true },
                news: [],
                slug,
                topic: '',
              },
            };
          }
        } catch (error) {
          console.warn('Editorial Tools Expired URLs API error:', error);
        }
      }

      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();
      const newsType =
        term.tid === '938584'
          ? 'benzinga_stockinsiderreport'
          : term.tid === '802151' //partner-content // include all in featured and main list
            ? ['story', 'benzinga_reach']
            : 'story';
      // const newsType = term.tid === '938584' ? 'benzinga_stockinsiderreport' : 'story';

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            displayOutput: 'abstract',
            excludeAutomated: true,
            limit: featuredNewsLimit,
            type: newsType,
          },
        ),
        3000,
      );

      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            displayOutput: 'abstract',
            headlines: isIsraelPage ? 'include' : undefined,
            limit: 30, //this is BAD // This parameter is not accurate often return less articles,
            type: newsType,
          },
        ),
        3000,
      );

      const removeTopic = shouldTopicPage410(newsResponse?.ok);
      if (removeTopic) {
        res.statusCode = 410;
        return {
          props: {
            error: 410,
            featuredNews: [],
            headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true },
            news: [],
            slug,
            topic: '',
          },
        };
      }
    }

    // TODO: Fetch related WP Page, use SEO Meta Data and Layout

    // const sidebarRes = await safeAwait(getMoneySidebar(96438));
    if (Array.isArray(featuredNewsResponse?.ok)) {
      featuredNews = featuredNewsResponse.ok;
    }

    let news = newsResponse?.ok ? filterDuplicateArticles(newsResponse?.ok) : [];

    const featuredNewsIds = featuredNews.map(node => node.id);

    if (featuredNewsLimit - featuredNews.length) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
      featuredNews = featuredNews.concat(news.splice(0, featuredNewsLimit - featuredNews.length));
    }

    if (news.length > 0) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
    }

    if (featuredNews.length < 3) {
      news = combineStoryObjectArrays(news, featuredNews);
      featuredNews = [];
    }

    const getTopicByNameRes = await session.getManager(ContentManager).getTopicByName(term?.name);

    if (
      !getTopicByNameRes?.ok?.layout_meta?.force_index &&
      Array.isArray(newsResponse?.ok) &&
      newsResponse.ok.length <= 5
    ) {
      metaProps.robots = 'noindex, nofollow';
    }

    if (Array.isArray(getTopicByNameRes?.ok?.above_content?.blocks)) {
      getTopicByNameRes.ok.above_content.blocks = await loadServerSideBlockData(
        session,
        getTopicByNameRes.ok.above_content.blocks,
        req.headers,
      );
    }

    if (featuredNews[0]) {
      metaProps.dateCreated = featuredNews[0].updated;
      metaProps.dateUpdated = featuredNews[0].updated;
    }

    if (term) {
      res.setHeader('Surrogate-Key', formatTermsSurrogateKeys(term));
    }

    if (
      ['topic', 'channel'].includes(template) &&
      (featuredNewsResponse?.err || newsResponse?.err) &&
      process.env.NODE_ENV !== 'development'
    ) {
      res.statusCode = 503;
      return {
        props: {
          error: 503,
          featuredNews: [],
          ...(template === 'topic'
            ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
            : {}),
          news: [],
          slug,
          topic: '',
        },
      };
    }

    news = isIsraelPage ? combineStoryObjectArrays(news, featuredNews) : news;

    if (!news?.length && !getTopicByNameRes?.ok && !postData && !featuredNews?.length) {
      res.statusCode = 404;
      return {
        props: {
          error: 404,
          featuredNews: [],
          ...(template === 'topic'
            ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
            : {}),
          news: [],
          topic: '',
        },
      };
    }

    let layout = postData ?? getTopicByNameRes.ok ?? null;

    if (template === 'channel' || template === 'topic') {
      if (!layout) {
        layout = {
          below_main_content: null,
          header: null,
          in_content: null,
          sidebar: null,
        };
      }

      if (postData && Array.isArray(postData?.in_content?.blocks)) {
        postData.in_content.blocks = await loadServerSideBlockData(
          session,
          postData?.in_content?.blocks,
          req.headers,
          req.cookies,
        );
      }

      injectBlockInLayout(layout, template === 'channel' ? 'below_main_content' : 'in_content', getTaboolaBlock());

      pageTargeting = { BZ_PTYPE: template };
      if (term) {
        if (template === 'channel') {
          pageTargeting['BZ_CHANNEL'] = [term.name, term.tid];
        } else if (template === 'topic') {
          pageTargeting['BZ_TAG'] = [term.name, term.tid];
        }
      }
    }

    const brokerWidgetRes = postData?.layout?.settings?.hide_footer
      ? await contentManager.getWordpressPost(154603)
      : null;

    return {
      props: {
        brokerWidget: brokerWidgetRes?.ok || null,
        ...(template === 'topic'
          ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
          : {}),
        featuredNews,
        layout,
        metaProps,
        news,
        pageTargeting,
        post: postData ?? null,
        slug,
        template: template,
        term: term,
      },
    };
  } catch (error) {
    console.error('Error with [...slug] page data', error);
    res.statusCode = 503;
    return {
      props: {
        error: 503,
        featuredNews: [],
        news: [],
        slug,
        topic: '',
      },
    };
  }
};

export default Page;
