import React from 'react';
import { HOME_PAGE_TABS } from '../../src/components/Home/components/HomeTabs';
import Hooks from '@benzinga/hooks';
import HomePageTemplate from '../../src/components/Home/components/HomePageTemplate';
import { HomePageWrapper } from '../indexs';
import { moneyMetaInfo } from '@benzinga/money';
import { homeTab } from '../api/home/<USER>';

const BriefsPage = props => {
  Hooks.useEffectDidMount(() => {
    // Hack to prevent jumping if the last scroll position was at LazyLoaded block
    if (window.history) {
      window.history.scrollRestoration = 'manual';
    }
  });

  return (
    <HomePageWrapper>
      <HomePageTemplate {...props} />
    </HomePageWrapper>
  );
};

export const getServerSideProps = async ({ req, res }) => {
  try {
    res.setHeader('Cache-Control', 'public, s-maxage=120, stale-while-revalidate=240');

    const pageProps = {
      activeTab: HOME_PAGE_TABS.BRIEFS,
    };

    const slug = '/briefs';
    const tabResponse = await homeTab(slug, req);

    if (tabResponse?.success === false) {
      res.statusCode = tabResponse?.error;

      return {
        props: {
          error: 503,
          metaProps: tabResponse.postData ? moneyMetaInfo(tabResponse.postData) : null,
          post: null,
          slug,
          template: 'page',
        },
      };
    }

    return {
      props: {
        ...pageProps,
        metaProps: tabResponse.postData ? moneyMetaInfo(tabResponse.postData) : null,
        post: tabResponse.postData,
        slug,
        template: 'page',
      },
    };
  } catch {
    res.statusCode = 404;
    return {
      props: {
        error: 404,
        featuredNews: [],
        news: [],
        topic: '',
      },
    };
  }
};

export default BriefsPage;
