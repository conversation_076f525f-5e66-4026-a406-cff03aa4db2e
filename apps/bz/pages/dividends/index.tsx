import React from 'react';
import { NextPage } from 'next';

import { ShareButtons } from '@benzinga/ui';
import { StoryObject } from '@benzinga/basic-news-manager';
import { useSponsoredContentArticle } from '@benzinga/content-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import { CalendarType } from '@benzinga/calendar-manager';
import styled from '@benzinga/themetron';

import {
  getServerSideProps as CalendarGetServerSideProps,
  CalendarPageProps,
  getCalendarData,
} from '../calendars/[calendar]';
import CalendarsList from '../../src/components/CalendarsList';
import { ToolsPageMain } from '../../app/tools/ToolsPageMain';

import { ContentFeed, DefaultNewsElement } from '@benzinga/news';
import { MoneyBlocksLayout, MoneyPageTemplate } from '@benzinga/money';
import { DividendsCalendar } from '@benzinga/calendars';

const DividendsPage: NextPage<CalendarPageProps> = ({
  brokerWidget,
  calendarDataSet,
  initialDateRange,
  metaProps,
  news,
  page,
}) => {
  const calendarData = getCalendarData('dividends');
  const session = React.useContext(SessionContext);
  const [sponsoredArticles] = useSponsoredContentArticle(session);

  const tabs = [
    {
      key: '/dividends',
      name: 'Overview',
    },
    {
      key: '/dividends/trading-ideas',
      name: 'Trading Ideas',
    },
  ];

  return (
    <DividendsPageContainer className="dividends-page">
      {page && (
        <MoneyPageTemplate
          layoutAboveArticle={
            <div className="mb-4">
              <ContentFeed
                contentId={'Recent Dividend News'}
                excludeIds={[]}
                isInfinite={false}
                limit={20}
                loadMore={true}
                newsItemElement={(node, index) => {
                  return (
                    <DefaultNewsElement
                      key={`${node.id}-${index}`}
                      node={node as StoryObject}
                      postCardProps={undefined}
                    />
                  );
                }}
                nodes={news}
                poolLatest={false}
                poolLatestInterval={30000}
                query={{ channels: ['Dividends'], pageSize: 10 }}
                realtime={false}
                showSponsoredContent={true}
                sponsoredNodes={sponsoredArticles}
                title={'Recent Dividend News'}
              />
            </div>
          }
          layoutFooter={
            <div className="mx-4">
              <h2>Explore Benzinga&apos;s Financial Tools</h2>
              <ToolsPageMain brokerWidget={brokerWidget} />
              {page?.footer && <MoneyBlocksLayout post={page.footer} />}
            </div>
          }
          layoutHeader={
            <div
              className={`calendar-container ${
                calendarData?.separateTitle || calendarData?.description ? 'mt-4' : 'lg:mt-4'
              }`}
            >
              <CalendarsList hideLabels={['Dividends Calendar']} />
              <DividendsCalendar
                additionalFetchParams={{
                  'parameters[date_from]': undefined,
                  'parameters[date_to]': undefined,
                }}
                calendar={'dividends' as CalendarType}
                calendarData={calendarData}
                hasStockTableStyling={true}
                hiddenColumns={[]}
                hideFilters={false}
                initialData={calendarDataSet || null}
                initialDateRange={initialDateRange}
                leftSlot={
                  <ShareButtons
                    className="dividends-share"
                    title={calendarData?.title}
                    url={`https://benzinga.com/dividends`}
                  />
                }
              />
            </div>
          }
          layoutTabs={tabs}
          layoutTabsOptions={{ prefetchAllTabs: true }}
          post={page}
          tabOptions={{
            rightSideElement: (
              <div className="right-side-element text-gray-500 text-sm pb-1.5 hidden md:block">
                {metaProps.title.replace('Dividend Calendar', 'Data')}
              </div>
            ),
          }}
          tabsTitle="Dividends"
          width="wide"
        />
      )}
    </DividendsPageContainer>
  );
};

export const getServerSideProps = CalendarGetServerSideProps;

export default DividendsPage;

const DividendsPageContainer = styled.div`
  &.dividends-page {
    .calendar-container {
      max-width: 1300px;
      margin: 1rem auto 2rem auto;
      position: relative;
      width: 100%;

      display: flex;
      flex-direction: row;
      gap: 2rem;

      .calendar-table {
        flex-grow: 1;
      }

      @media (max-width: 1300px) {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      @media (max-width: 800px) {
        flex-direction: column-reverse;
      }
    }
    .page-share-buttons {
      margin-bottom: 0px;
    }
  }
`;
