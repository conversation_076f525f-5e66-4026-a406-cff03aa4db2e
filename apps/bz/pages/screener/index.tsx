import React from 'react';
import { ScannerCom } from '@benzinga/pro-scanner-widget';
import styled from '@benzinga/themetron';

const ScreenerPost: React.FC = () => {
  if (typeof window !== 'undefined')
    return (
      <PageContainer>
        <ScannerCom />
      </PageContainer>
    );
  return null;
};

export default ScreenerPost;

const PageContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  background: #fafafa;
  box-sizing: border-box;
  color: rgb(212, 219, 231);
  flex: 1 1 0%;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
`;
