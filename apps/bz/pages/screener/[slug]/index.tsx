import React from 'react';
import { GetServerSideProps } from 'next';
import { Meta } from '@benzinga/seo';
import ScreenerTemplate from '../../../src/components/screener/templates/ScreenerTemplate';
import { screenerConfigMap } from '../../../screener-configs/screenerConfigMap';
import { ContentManager, WordpressPage } from '@benzinga/content-manager';
import { getGlobalSession } from '../../api/session';
import { MoneyPageTemplate, moneyMetaInfo, handleCanonicalRedirect } from '@benzinga/money';
import { loadServerSideBlockData, isRankingScreener } from '@benzinga/blocks-utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { safeTimeout } from '@benzinga/safe-await';

interface ScreenerPostsProps {
  screenerPost?: WordpressPage;
  staticSlug?: string | null;
}

const ScreenerPosts: React.FC<ScreenerPostsProps> = ({ screenerPost, staticSlug }) => {
  if (staticSlug && screenerConfigMap[staticSlug]) {
    const { config, meta } = screenerConfigMap[staticSlug];
    return (
      <>
        <Meta {...meta} />
        <ScreenerTemplate key={staticSlug} config={config} />
      </>
    );
  }
  if (screenerPost) {
    return <MoneyPageTemplate post={screenerPost} />;
  }
  return null;
};

export default ScreenerPosts;

export const getServerSideProps: GetServerSideProps = async ({ query, req, res }) => {
  const slug = query?.slug ? sanitizeHTML(query?.slug as string) : '';
  const screenerSlug = Array.isArray(slug) ? slug[0] : slug;

  if (screenerConfigMap[screenerSlug]) {
    return {
      props: {
        staticSlug: screenerSlug,
      },
    };
  }

  try {
    const session = getGlobalSession();
    const ScreenerPostRes = await session.getManager(ContentManager).getPageWithPath(screenerSlug);
    const screenerPost = ScreenerPostRes?.ok || null;

    if (!screenerPost) {
      res.statusCode = 404;
      return { notFound: true };
    }

    if (screenerPost && screenerPost.success !== false) {
      const redirectHandled = handleCanonicalRedirect(
        screenerPost?.canonical_link ?? '',
        'screener/' + screenerSlug,
        req,
        res,
      );

      if (redirectHandled) {
        return { props: {} };
      }

      if (Array.isArray(screenerPost?.blocks)) {
        screenerPost.blocks = await loadServerSideBlockData(session, screenerPost?.blocks, req.headers, req.cookies);
      }

      if (screenerPost?.sidebar?.blocks && Array.isArray(screenerPost?.sidebar?.blocks)) {
        screenerPost.sidebar.blocks = await loadServerSideBlockData(
          session,
          screenerPost?.sidebar?.blocks,
          req.headers,
          req.cookies,
        );
      }

      // fetch news for rankings based on tickers
      const tickers =
        screenerPost?.blocks?.find(block => block.blockName === 'acf/stocks-list-container')?.attrs?.data?.tickers ??
        null;
      if (isRankingScreener(slug) && tickers) {
        const contentBlock = screenerPost?.blocks?.find(block => block.blockName === 'acf/content-feed');
        const query = { tickers: tickers };
        const options = { limit: Number(contentBlock?.attrs?.data?.contentFeedProps?.limit ?? 10) };
        const res = await safeTimeout(session.getManager(BasicNewsManager).simplyQueryNews(query, options), 3000);
        if (Array.isArray(res?.ok) && contentBlock?.attrs?.data?.contentFeedProps) {
          contentBlock.attrs.data.contentFeedProps.nodes = res.ok;
          contentBlock.attrs.data.contentFeedProps.query.tickers = tickers;
        }
        const headerBlock = screenerPost?.header?.blocks?.[0];
        if (headerBlock) {
          headerBlock.attrs.data.is_ranking_screener = true;
        }
      }
    }

    return {
      props: {
        metaProps: screenerPost ? moneyMetaInfo(screenerPost) : null,
        screenerPost: screenerPost,
      },
    };
  } catch (error) {
    res.statusCode = 404;
    console.log('error:', error);
    return {
      props: {
        slug: null,
      },
    };
  }
};
