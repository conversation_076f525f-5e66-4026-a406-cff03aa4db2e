

.screener-container {
  max-width: 1400px;
  margin: 0 auto 4rem auto;
  box-sizing: border-box;
  flex: 1;
  position: static;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 1.5rem;
  padding: 0 1rem;
}

.screener-container.has-no-sidebar {
  justify-content: center;
}

.screener-main-content {
  flex: 1;
  min-width: 0;
}

.screener-main-content.has-no-sidebar {
  max-width: 1050px;
  margin: 0 auto;
}

.screener-stock-boxes-container {
  display: flex;
  gap: 0.25rem;
  padding-bottom: 0.75rem;
  margin: 0 -1rem;
  padding-left: 1rem;
  padding-right: 1rem;
}


@media (max-width: 1199px) {
  .screener-stock-boxes-container {
    scrollbar-width: none;
    background-color: white;
    padding-top: 5px;
    padding-bottom: 0px;
  }

  .screener-stock-boxes-container::-webkit-scrollbar {
    display: none;
  }

  .overflow-x-auto {
    scrollbar-width: none;
  }

  .overflow-x-auto::-webkit-scrollbar {
    display: none;
  }
}

@media (min-width: 1200px) {
  .screener-stock-boxes-container {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }

  .screener-stock-boxes-container::-webkit-scrollbar {
    height: 6px;
  }

  .screener-stock-boxes-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .screener-stock-boxes-container::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
}

.screener-extra-bold {
  font-weight: 950;
  -webkit-font-smoothing: auto;
  text-rendering: optimizeLegibility;
}

.screener-sidebar-wrapper {
  display: none;
}


@media (min-width: 640px) {
  .screener-stock-boxes-container {
    gap: 1rem;
  }
}

@media (min-width: 500px) {
  .sticky-position{
    position: sticky;
    top: 132px;
  }

  .sticky-position-with-ad{
    position: sticky;
    top: 212px
  }
}

@media (min-width: 1000px) and (max-width: 1199px) {
  .screener-stock-boxes-container > *:first-child {
    margin-left: 170px;
  }
}

@media (min-width: 1024px) and (max-width: 1199px) {
  .screener-container {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
    max-width: 100%;
  }

  .screener-main-content {
    width: 100%;
    max-width: 100%;
    flex: none;
  }

  .screener-sidebar-wrapper {
    display: none !important;
  }
}

@media (min-width: 1024px) and (max-width: 1416px) {
  .screener-container {
    max-width: 100%;
    padding: 0 1rem;
  }
}

@media (min-width: 1200px) {
  .screener-container {
    flex-direction: row;
    gap: 2rem;
    padding: 0;
  }

  .screener-stock-boxes-container {
    z-index: 10;
    display: flex;
    flex-direction: row;
    margin: 0;
    overflow-x: visible;
    padding-left: 0;
    padding-right: 0;
    margin-left: 20px;
    scrollbar-width: none;
  }

  .screener-stock-boxes-container::-webkit-scrollbar {
    display: none;
  }

  .screener-sidebar-wrapper {
    display: block;
    width: 300px;
    margin-top: 2rem;
    flex-shrink: 0;
  }
}

.sticky-position-mobile{
  position: sticky;
  top: var(--mobile-nav-height, 60px);
  transition: top 0.2s ease-in-out;
}


@media (min-width: 1200px) and (max-width: 1416px) {
  .screener-container {
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media (max-width: 1199px) {
  .screener-main-content.has-no-sidebar {
    width: 100%;
    max-width: 100%;
    flex: none;
    min-height: 0;
    margin: 0;
  }
}
.triangle-down {
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid #2563eb;
}

