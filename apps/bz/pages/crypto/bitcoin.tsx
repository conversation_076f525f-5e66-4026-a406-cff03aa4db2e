import React from 'react';
import { MetaProps, PageType } from '@benzinga/seo';

import { MoneyPage } from '@benzinga/money';
// import { VersusCard } from '@benzinga/crypto';
import { News } from '@benzinga/basic-news-manager';
import { WordpressPage } from '@benzinga/content-manager';
import { TradeIdea } from '@benzinga/trade-ideas-manager';
import styled from '@benzinga/themetron';
import { getCryptoPageProps } from '../api/crypto';
import { getGlobalSession } from '../api/session';

import type { CallToActionFormProps } from '@benzinga/forms-ui';
import { QuotesManager } from '@benzinga/quotes-manager';

const DefaultSidebar = React.lazy(() => import('../../src/components/Sidebars/DefaultSidebar'));

interface CryptoPageProps {
  headerProps?: any;
  metaProps?: MetaProps;
  meta?: any;
  newsletter?: CallToActionFormProps;
  page?: WordpressPage;
  taxonomies?: any;
  ticker?: string;
  tickers?: string[];
  title?: string;
  topStories?: News[];
  tradeIdeas?: TradeIdea[];
  url?: string;
}

const CryptoHomePage = (props: CryptoPageProps) => {
  return (
    <HomePageWrapper className="home-page-container">
      <div className="home-page-wrapper flex lg:flex-row flex-col">
        <div className="main flex-1">
          {props.page && <MoneyPage page={props.page} showTitle={false} />}
          {/* <VersusCard {...{
            coins: ['Bitcoin', 'Ethereum'],
            content:
              'Many people consider Bitcoin  and Ethereum the Coke and Pepsi of crypto. Yes, they are No. 1 and No. 2 in terms of total market cap and public notoriety.',
            title: 'Their features and differences',
            url: '',
          }}/> */}
          {/* <FeaturedSection {...props}/>
          {props.recentNews.length && <NewsSection nodes={props.recentNews} />} */}
        </div>
        <div className="sidebar flex-none min-w-[300px] lg:ml-4 ml-0">
          <React.Suspense>
            <DefaultSidebar sidebar={props.page?.sidebar} />
          </React.Suspense>
        </div>
      </div>
    </HomePageWrapper>
  );
};

export const PageTitle = styled.h1`
  margin-right: 0.5rem;
`;

export const PageTitleWrapper = styled.div`
  display: inline-flex;
  margin-bottom: 0.75rem;
  align-items: baseline;
  border-bottom: solid 1px ${({ theme }) => theme.colorPalette.neutral500};
  padding-bottom: 0.25rem;
  width: 100%;
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

export const HomePageWrapper = styled.div``;

export async function getServerSideProps({ isServer, req, res }) {
  const pageProps: CryptoPageProps = await getCryptoPageProps('BTC');
  pageProps.url = isServer ? `https://${req.headers.host}/crypto/bitcoin` : '';

  if (!pageProps.page) {
    res.statusCode = 404;
    return {
      props: pageProps,
    };
  }

  const session = getGlobalSession();
  const quoteManager = session.getManager(QuotesManager);
  const quotesRes = await quoteManager.getDelayedQuotes(pageProps?.tickers || []);

  pageProps.headerProps = {
    logoVariant: 'crypto',
    marketTickers: pageProps.tickers,
    quotes: quotesRes?.ok,
  };

  pageProps.metaProps = {
    author: pageProps.meta?.author,
    canonical: 'https://www.benzinga.com/crypto/bitcoin',
    dateCreated: pageProps.meta?.publishedDate ?? null,
    description: pageProps.meta?.description ?? null,
    image: pageProps.meta?.image ?? null,
    pageType: PageType.Channel ?? null,
    title: pageProps.meta?.title ?? null,
  };

  return {
    props: pageProps,
  };
}

export default CryptoHomePage;
