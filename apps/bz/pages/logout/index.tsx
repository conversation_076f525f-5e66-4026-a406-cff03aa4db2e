import { Spinner } from '@benzinga/core-ui';
import { BzImage } from '@benzinga/image';
import { NotificationManager } from '@benzinga/notification-manager';
import { SessionContext } from '@benzinga/session-context';
import styled from '@benzinga/themetron';
import { getLanguageCodeByHost, setLanguageByHost, translate } from '@benzinga/translate';
import { useClearBrowserStorage, useIsUserLoggedIn } from '@benzinga/user-context';
import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const LogoutPage = ({ next }) => {
  const { t } = useTranslation(['auth', 'common']);
  useClearBrowserStorage();
  const session = useContext(SessionContext);
  const [loaded, setLoaded] = useState(false);
  const [pushUpdated, setPushUpdated] = useState(false);
  const isLoggedIn = useIsUserLoggedIn();

  useEffect(() => {
    const updatePush = async () => {
      if ('Notification' in window && Notification.permission === 'granted') {
        await session.getManager(NotificationManager).registerPushDevice();
      }
      setPushUpdated(true);
    };
    if (!isLoggedIn) {
      updatePush();
    }
  }, [isLoggedIn, session]);

  useEffect(() => {
    if (pushUpdated && loaded) {
      window.location.href = next ? (next as string) : '/';
    }
  }, [pushUpdated, loaded, next]);

  const onLoad = async () => {
    setLoaded(true);
  };

  return (
    <LogoutWrapper>
      <MessageWrapper>
        <BzImage
          height="100"
          src="https://cdnwp-s3.benzinga.com/wp-content/uploads/2024/03/08101240/logout-graphic-1.png"
          width="100"
        />
        <h1>{t('Auth.Logout.logout-successful')}</h1>
        <div>
          <SpinnerContent>
            <Spinner />
          </SpinnerContent>
          <p>{t('Auth.Logout.returning-to-prev-page')}</p>
        </div>
      </MessageWrapper>
      <iframe
        frameBorder="0"
        onLoad={onLoad}
        src="https://pro.benzinga.com/logout"
        style={{ display: 'none' }}
      ></iframe>
    </LogoutWrapper>
  );
};

const LogoutWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 500px;
  background: #f4f8fe;
`;

const MessageWrapper = styled.div`
  padding: 1rem;
  background: white;
  border-radius: 8px;
  width: 300px;
  height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
`;

const SpinnerContent = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-bottom: 8px;
`;

export async function getServerSideProps({ isServer, query, req }) {
  const host = req.headers.host;
  setLanguageByHost(host);
  const url = isServer ? `https://${host}/logout` : '';
  const next = query.next ? query.next : '';
  return {
    props: {
      disablePushPrompt: true,
      headerProps: {
        hideBanner: true,
        hideFooter: false,
        hideNavigationBar: false,
        hideQuoteBar: true,
      },
      metaProps: {
        canonical: 'https://www.benzinga.com/logout',
        description: translate('Auth.Meta.logout-from-benzinga', { ns: 'auth' }),
        language: getLanguageCodeByHost(host),
        title: translate('Auth.Meta.logout-from-benzinga', { ns: 'auth' }),
      },
      next,
      url: url,
    },
  };
}

export default LogoutPage;
