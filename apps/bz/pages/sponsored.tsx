import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import Head from 'next/head';
import { ArticlePageTemplate } from '@benzinga/templates';
import { ArticleData } from '@benzinga/article-manager';
import styled from '@benzinga/themetron';

const NativoScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.NativoScript };
  }),
);

export const SponsoredPage: NextPage = () => {
  return (
    <>
      <Head>
        <meta content="noindex, nofollow" name="Googlebot-News" />
      </Head>
      <NativoScript />
      <StyledContainer>
        <ArticlePageTemplate
          article={
            {
              name: null,
              nodeId: 1,
            } as unknown as ArticleData
          }
          deviceType={null}
          enableConnatixScript={false}
          isTemplate={true}
          layout={null}
          loadInfiniteArticles={false}
          metaProps={null}
          nid="1"
          raptiveEnabled={false}
          useNewTemplate={true}
          wordCount={null}
        />
      </StyledContainer>
    </>
  );
};

const StyledContainer = styled.div`
  .article-layout-main-header {
    min-height: 100vh;
  }
`;

export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {
      disablePageTracking: true,
      headerProps: {
        disableOptinMonster: true,
      },
    },
  };
};

export default SponsoredPage;
