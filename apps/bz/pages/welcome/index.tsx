import React, { useCallback, useEffect, useState } from 'react';
import { Meta } from '@benzinga/seo';
import Error from 'next/error';
import styled from '@benzinga/themetron';
import { Steps } from '@benzinga/core-ui';
import { useRouter } from 'next/router';
import { GetServerSideProps, NextPage } from 'next';
import { isMobile } from '@benzinga/device-utils';
import { SessionContext } from '@benzinga/session-context';
import { UserManager } from '@benzinga/user-manager';
import axios from 'axios';
import { useTranslation } from 'react-i18next';
import i18n, { LOCALES, getLanguageCodeByHost, setLanguageByHost } from '@benzinga/translate';
import { IdentityContext } from '@benzinga/identity';

import { useIsUserLoggedIn } from '@benzinga/user-context';

import {
  FirstNameForm,
  OnboardingWatchlist,
  OnboardingNotifications,
  OnboardingForm,
  TickerToAdd,
} from '@benzinga/bz-onboarding';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { MainLogo } from '@benzinga/logos-ui';

export interface OnboardingNewsTopic {
  topic: string;
  channel?: string;
  tags?: string;
  news: string[];
  tickers: string[];
}

export interface WelcomePageProps {
  data: Array<TickerToAdd>;
  errorCode?: number;
  step: string;
  url?: string;
}

const welcomeSteps = ['', 'watchlist', 'alerts'];

export const WelcomePage: NextPage<WelcomePageProps> = ({ data, errorCode, step }) => {
  const { t } = useTranslation(['welcome']);
  const { config } = React.useContext(IdentityContext);
  const session = React.useContext(SessionContext);
  const userManager = session.getManager(UserManager);
  const watchlistManager = session.getManager(WatchlistManager);
  const router = useRouter();

  const currentStep = welcomeSteps.indexOf(step) ?? 0;
  const [redirectUrl, setRedirectUrl] = useState(router.query?.redirect ?? router.query?.next ?? '');
  const isLoggedIn = useIsUserLoggedIn();
  const [enableEmail, setEnableEmail] = useState(false);
  const [selectedTickers, setSelectedTickers] = useState<TickerToAdd[]>([]);
  const [addedSearchSymbols, setAddedSearchSymbols] = useState<TickerToAdd[]>([]);

  const [firstName, setFirstName] = useState('');
  const [phone, setPhone] = useState('');
  const [smsPolicy, setSmsPolicy] = useState(true);
  const [enableSMS, setEnableSMS] = useState(false);
  const [traderType, setTraderType] = useState('');

  const handleSavePreferences = useCallback(() => {
    userManager.editUser({
      enable_email: enableEmail,
      enable_sms: enableSMS,
      first_name: firstName,
      ...(enableSMS && { phone_number: phone }),
    });
  }, [enableEmail, enableSMS, firstName, phone, userManager]);

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login?next=/welcome');
    }
  }, [isLoggedIn, router]);

  useEffect(() => {
    const handleBeforeUnload = event => {
      // Note: Many browsers will not display this custom message
      event.returnValue = t('Welcome.before-you-leave');
      const email = userManager.getUser()?.email;
      session.getManager(TrackingManager).trackOnboardingEvent('close', {
        onboarding_step: currentStep.toString(),
      });
    };

    // Note Limitation: if the user does not interact with the page, the eventListeners are not set
    if (currentStep === 0 && !traderType) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [session, currentStep, traderType, userManager, t]);

  const steps = [
    {
      title: t('Welcome.steps.profile-info', { ns: 'welcome' }),
    },
    {
      title: t('Welcome.steps.watchlist'),
    },
    {
      title: t('Welcome.steps.alerts'),
    },
  ];

  const goToWatchlist = useCallback(() => {
    session.getManager(TrackingManager).trackLinkEvent('click', {
      link_action: 'goToWatchlist',
      link_id: 'onboard_experience_level',
      link_type: 'onboard_step_button',
      value: traderType,
    });

    const redirectPath = redirectUrl ? `?next=${redirectUrl}` : '';
    router.push('/welcome/[step]' + redirectPath, `/welcome/watchlist` + redirectPath, { scroll: false });
    window.scrollTo(0, 0);
  }, [session, redirectUrl, router, traderType]);

  const goToAlerts = useCallback(() => {
    const redirectPath = redirectUrl ? `?next=${redirectUrl}` : '';
    router.push('/welcome/[step]' + redirectPath, `/welcome/alerts` + redirectPath, { scroll: false });
    window.scrollTo(0, 0);
  }, [redirectUrl, router]);

  const handleShowProfile = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.location.href = 'https://www.benzinga.com/profile/portfolio/';
    }
  }, []);

  const createWatchlist = useCallback(() => {
    (async () => {
      const settings = {
        emailSummary: enableEmail,
        realtimeBZ: false,
        realtimePR: false,
      };

      const tickerArr = [...selectedTickers, ...addedSearchSymbols];
      const watchlistName = t('Watchlist.my-first-watchlist');

      const symbols = tickerArr.map(ticker => {
        return {
          countWeek: 0,
          isBZ: null,
          isBZPro: null,
          isPR: null,
          isSEC: null,
          sendRealtime: null,
          sound: null,
          status: null,
          symbol: ticker.ticker,
          symbolId: '',
          type: 'ticker',
          watchlistId: '',
        };
      });
      try {
        const res = await watchlistManager.createWatchlist(watchlistName, settings, symbols);
        const watchlist = res.ok;

        if (watchlist) {
          session.getManager(TrackingManager).trackLinkEvent('click', {
            link_action: 'createWatchlist',
            link_id: 'onboard_watchlist',
            link_type: 'onboard_step_button',
            value: symbols.map(s => s.symbol).join(','),
          });
        }
      } catch (err) {
        console.log(err);
      }
    })();
  }, [session, enableEmail, selectedTickers, addedSearchSymbols, watchlistManager, t]);

  const subscribeSMS = useCallback(() => {
    try {
      userManager.editUser({ phone_number: phone, sms_opt_in: true });
    } catch (err) {
      console.log(err);
    }
  }, [phone, userManager]);

  const getRedirectPath = useCallback((url: string | string[]): string | null => {
    let redirectPath = Array.isArray(url) ? url[0] : url;

    // Strip special non-alphanumeric characters at the start of url
    if (redirectPath) {
      redirectPath = redirectPath.replace(/^[^a-zA-Z0-9]+/, '');
    }

    try {
      const absoluteUrl = new URL(redirectPath);
      if (!isTrustedDomain(absoluteUrl.hostname)) {
        return '/profile/portfolio/'; // Redirect to a safe default URL
      }
    } catch (err) {
      console.log(err);
      try {
        let relativeUrl;
        if (redirectPath) {
          relativeUrl = new URL(redirectPath, window.location.origin);
        } else {
          relativeUrl = new URL(window.location.origin);
        }
        if (!isTrustedDomain(relativeUrl.hostname)) {
          return '/profile/portfolio/'; // Redirect to a safe default URL
        }
      } catch (err) {
        return null;
      }
    }

    if (!redirectPath || !redirectPath.startsWith('http')) {
      return '/profile/portfolio/';
    }

    return redirectPath;
  }, []);

  const isTrustedDomain = (hostname: string): boolean => {
    return hostname.endsWith('benzinga.com') || hostname.endsWith('zingbot.bz');
  };

  const handleGoBack = useCallback((url?: string | null) => {
    if (url) {
      window.location.href = url;
    }
  }, []);

  const goPreviousPath = currentStep => {
    const step = welcomeSteps[currentStep - 1];
    const redirectPath = redirectUrl ? `?next=${redirectUrl}` : '';
    router.push(`/welcome/${step}` + redirectPath);
  };

  const handleCompleteOnboarding = useCallback(
    url => {
      createWatchlist();
      if (enableSMS) {
        subscribeSMS();
      }

      handleSavePreferences();

      userManager.setGlobalSetting('isUserOnBoarded', true);
      if (url) {
        handleGoBack(getRedirectPath(url));
      } else if (i18n.language !== LOCALES.EN) {
        handleGoBack(window.location.origin);
      } else {
        handleShowProfile();
      }
    },
    [
      createWatchlist,
      enableSMS,
      getRedirectPath,
      handleGoBack,
      handleSavePreferences,
      handleShowProfile,
      subscribeSMS,
      userManager,
    ],
  );

  const handleContinue = useCallback(
    (currentStep: number) => {
      if (currentStep === 0) {
        goToWatchlist();
      } else if (currentStep === 1) {
        goToAlerts();
      } else if (currentStep === 2) {
        handleCompleteOnboarding('');
      }
    },
    [goToAlerts, goToWatchlist, handleCompleteOnboarding],
  );

  const handleSkip = useCallback(() => {
    userManager.setGlobalSetting('isUserOnBoarded', true);
    handleGoBack(getRedirectPath(redirectUrl));
  }, [getRedirectPath, handleGoBack, redirectUrl, userManager]);

  const validateStep = useCallback(
    (currentStep: number) => {
      if (currentStep === 0) {
        return traderType !== '';
      } else if (currentStep === 1) {
        return selectedTickers.length > 0 || addedSearchSymbols.length > 0;
      } else if (currentStep === 2) {
        return enableSMS ? smsPolicy && phone : true;
      }
      return false;
    },
    [addedSearchSymbols.length, enableSMS, phone, selectedTickers.length, smsPolicy, traderType],
  );

  const formsVariant = i18n.language !== LOCALES.EN ? 'simple' : 'default';
  const bgButtonClass = i18n.language !== LOCALES.EN ? 'bg-sky-950' : 'bg-bzorange-500';

  if (errorCode) {
    return <Error statusCode={errorCode} />;
  }

  return (
    <div>
      <Meta
        canonical="https://www.benzinga.com/welcome"
        description={t('Welcome.meta.description')}
        title={t('Welcome.meta.title')}
      />
      <WelcomeWrapper className="welcome relative">
        <div className="flex flex-row justify-between items-center z-20 relative">
          <div style={{ width: '150px' }}>
            <MainLogo logoVariant={config.logoVariant} variant="dark" />
          </div>
          {currentStep !== steps.length - 1 && currentStep !== 0 && (
            <button
              className="px-4 py-2 rounded-md border border-[#CEDDF2] text-[#5B7292] uppercase"
              onClick={handleSkip}
            >
              {t('Buttons.skip')}
            </button>
          )}
        </div>
        {(currentStep === 0 || !isMobile()) && (
          <div className="py-8 border-b border-[#CEDDF2]">
            <Steps current={currentStep} steps={steps} variant="arrow" />
          </div>
        )}
        <div className="h-fit flex justify-center items-center">
          {currentStep === 0 ? (
            <div className="onboarding-layout">
              <h1 className="mb-4">{t('Welcome.welcome-to')}</h1>
              <OnboardingForm
                setTraderType={setTraderType}
                traderType={traderType}
                userManager={userManager}
                variant={formsVariant}
              />
            </div>
          ) : null}
          {currentStep === 1 ? (
            <div className="onboarding-layout">
              <OnboardingWatchlist
                addedSearchSymbols={addedSearchSymbols}
                data={data}
                selectedTickers={selectedTickers}
                setAddedSearchSymbols={setAddedSearchSymbols}
                setSelectedTickers={setSelectedTickers}
                variant={formsVariant}
              />
            </div>
          ) : null}
          {currentStep === 2 ? (
            <div className="onboarding-layout">
              <OnboardingNotifications
                enableEmail={enableEmail}
                enableSMS={enableSMS}
                phone={phone}
                setEnableEmail={setEnableEmail}
                setEnableSMS={setEnableSMS}
                setPhone={setPhone}
                setSmsPolicy={setSmsPolicy}
                smsPolicy={smsPolicy}
                variant={formsVariant}
              />
              <FirstNameForm firstName={firstName} setFirstName={setFirstName} />
            </div>
          ) : null}
        </div>
        <div className="onboarding-controls">
          {currentStep > 0 && (
            <button className="border border-[#CEDDF2] left-0" onClick={() => goPreviousPath(currentStep)}>
              {t('Buttons.back')}
            </button>
          )}
          {currentStep !== steps.length - 1 || (currentStep === steps.length - 1 && i18n.language === LOCALES.EN) ? (
            <button
              className={`right-0 ${
                validateStep(currentStep) ? `${bgButtonClass} text-white` : 'bg-[#E1EBFA] text-[#5B7292]'
              }`}
              disabled={!validateStep(currentStep)}
              onClick={() => handleContinue(currentStep)}
            >
              {currentStep === steps.length - 1 ? t('Welcome.view-dashboard') : t('Welcome.continue')}
            </button>
          ) : null}
          {currentStep === steps.length - 1 && (
            <button
              className={`left-0 right-0 w-fit mx-auto mt-14 md:mt-0 ${
                enableSMS && (!smsPolicy || !phone) ? 'bg-[#E1EBFA] text-[#5B7292]' : 'bg-bzblue-700 text-white'
              }`}
              onClick={() => handleCompleteOnboarding(redirectUrl)}
            >
              {t('Welcome.return-to-benzinga')}
            </button>
          )}
        </div>
      </WelcomeWrapper>
    </div>
  );
};

export const WelcomeWrapper = styled.div`
  &.welcome {
    color: #5b7292;
    max-width: 1200px;
    margin: auto;
    padding: ${({ theme }) => theme.fontSize.xl};
    h1 {
      font-size: ${({ theme }) => theme.fontSize['4xl']};
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      margin-top: ${({ theme }) => theme.fontSize.lg};
      text-align: center;
      text-transform: uppercase;
    }
    .welcome-subtitle {
      font-size: ${({ theme }) => theme.fontSize.lg};
      text-align: center;
      margin-bottom: ${({ theme }) => theme.fontSize.xl};
    }
    .onboarding-text {
      font-size: ${({ theme }) => theme.fontSize['4xl']};
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      text-align: center;
      margin-bottom: ${({ theme }) => theme.fontSize.base};
      color: ${({ theme }) => theme.colorPalette.gray800};
    }
    .onboarding-subtext {
      max-width: 500px;
      margin: auto;
      margin-bottom: ${({ theme }) => theme.fontSize['xl']};
    }
    .onboarding-layout {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 3rem 0px;
    }

    .onboarding-controls {
      position: relative;
      padding: 24px 0;
      height: 80px;
      border-top: 1px solid ${({ theme }) => theme.colorPalette.gray300};
      button {
        position: absolute;
        padding: 8px 32px;
        text-transform: uppercase;
        top: 16px;
        border-radius: 4px;
      }
    }
  }
`;

export const getServerSideProps: GetServerSideProps = async ({ query, req }) => {
  const translations = await setLanguageByHost(req.headers.host ?? '', ['welcome', 'common']); // set language for server side translations

  const url = `https://${req.headers.host}/welcome`;
  const step: string = Array.isArray(query.step) ? query.step[0] : query.step ?? '';

  if (welcomeSteps.indexOf(step ?? '') < 0) {
    return {
      notFound: true,
    };
  }

  let onboardingData = [];
  try {
    const response = await axios.get(`${process.env.BASE_URL}/research/api2/onboarding-data`);
    onboardingData = response?.data?.onboardingData ?? [];
  } catch (err) {
    console.log('Error fetching onboarding data');
  }

  return {
    props: {
      data: onboardingData,
      headerProps: {
        hideBanner: true,
        hideFooter: true,
        hideNavigationBar: true,
        hideQuoteBar: true,
      },
      metaProps: {
        host: req.headers.host,
        language: getLanguageCodeByHost(req.headers.host ?? ''),
        translations,
      },
      step: query.step ?? '',
      url: url,
    },
  };
};

export default WelcomePage;
