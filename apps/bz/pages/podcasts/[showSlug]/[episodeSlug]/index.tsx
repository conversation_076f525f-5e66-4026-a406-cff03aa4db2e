import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { decode } from 'html-entities';
import { YouTubeEmbed } from '@next/third-parties/google';

import styled from '@benzinga/themetron';
import { MetaProps, PageType } from '@benzinga/seo';

import { sanitizeHTML } from '@benzinga/frontend-utils';
import { Breadcrumb, ContentManager, PodcastPost } from '@benzinga/content-manager';

import { BzImage } from '@benzinga/image';
import { Breadcrumbs, Layout } from '@benzinga/core-ui';
import type { PodcastCardData } from '@benzinga/ui';
import { PodcastPlatformBadges, ShowsAndPodcasts } from '@benzinga/ui';

import { getGlobalSession } from '../../../api/session';
import {
  formatShowsToPodcastCardData,
  formatPodcastEpisodeToPodcastCardData,
  formatPodcastProvidersUrlsToPodcastPlatformBadgesProps,
} from '../../../../utils/formatters/podcast';

import { Container } from '../../index';
import PodcastPageFooter from '../../../../src/components/Podcasts/PodcastPageFooter';
import { NoFirstRender } from '@benzinga/hooks';
import { getYoutubeVideoIdFromUrl } from '@benzinga/utils';
import { LayoutAdmin } from '@benzinga/blocks';
import { safeErrorStatus } from '@benzinga/safe-await';
import Error from '../../../_error';

const BenzingaProBannerAd = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.BenzingaProBannerAd,
  })),
);

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => ({
    default: module.CallToActionForm,
  })),
);

export interface PodcastShowPageProps {
  breadcrumbs: Breadcrumb[];
  error?: number;
  shows: PodcastCardData[];
  episodes: PodcastCardData[];
  metaProps?: MetaProps;
  post: PodcastPost | null;
  pageTargeting?: Record<string, string | string[]>;
}

const PodcastEpisodePage: NextPage<PodcastShowPageProps> = ({ breadcrumbs, episodes, post, shows, error }) => {
  if (error) {
    return (<Error statusCode={error} />) as React.ReactElement;
  }
  const youtubeVideoId = post?.youtube_link ? getYoutubeVideoIdFromUrl(post.youtube_link) : null;
  //const youtubeEmbedUrl = youtubeVideoId ? `https://www.youtube.com/embed/${youtubeVideoId}` : null;

  const SpotifyEmbed = () => {
    if (!post?.spotify_link) return null;
    return (
      <iframe
        allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
        className="rounded-xl"
        height="250"
        src={post?.spotify_link?.replace('/episode', '/embed/episode')}
        title="Spotify player"
        width="100%"
      ></iframe>
    );
  };

  return (
    <PodcastShowPageContainer className="podcasts-page">
      <Layout
        layoutBelow={
          <div className="mt-8 md:mt-2 w-full">
            <BenzingaProBannerAd />
          </div>
        }
        layoutFooter={<PodcastPageFooter />}
        layoutHeader={
          <section className="hero-section relative flex flex-col text-center text-white min-h-[600px]">
            <span className="z-[1] absolute contents">
              <BzImage
                alt=""
                layout="fill"
                loader={({ src }) => src}
                loading={'eager'}
                objectFit="cover"
                preload={true}
                preloadOptions={{ media: '(min-width: 750px)' }}
                src={'/next-assets/images/podcasts/podcasts-hero-section2.png'}
              />
            </span>
            <div className="z-[2] flex flex-col items-center px-4">
              <div className="mb-4 text-white mt-12">
                <Breadcrumbs data={breadcrumbs} />
              </div>
              <h1
                className="text-5xl text-white uppercase mt-4 max-w-4xl"
                dangerouslySetInnerHTML={{ __html: sanitizeHTML(post?.title ?? '') }}
              />
              <p
                className="text-[#B8CBE5] mt-9 max-w-3xl"
                dangerouslySetInnerHTML={{ __html: sanitizeHTML(post?.summary ?? '') }}
              />
              <div className="mt-8 mb-12">
                {youtubeVideoId ? (
                  <YouTubeEmbed height={315} videoid={youtubeVideoId} width={560} />
                ) : (
                  <div className="w-[700px]">
                    <SpotifyEmbed />
                  </div>
                )}
              </div>
              <NoFirstRender>
                <React.Suspense>
                  {post && <LayoutAdmin className="mb-4" post={post} showClearCache={true} />}
                  {post?.show?.term_id && (
                    <LayoutAdmin
                      className="mb-4"
                      post={null}
                      type="show"
                      wordpressUrl={`https://www.benzinga.com/wp-admin/term.php?taxonomy=podcast_show&tag_ID=${post.show.term_id}&post_type=fintech_podcast`}
                    />
                  )}
                </React.Suspense>
              </NoFirstRender>
            </div>
          </section>
        }
        layoutHeaderOptions={{ full_width: true }}
        layoutMain={
          <section className="flex flex-col">
            {youtubeVideoId && <SpotifyEmbed />}
            {post?.content && (
              <article
                className="post-content px-4"
                dangerouslySetInnerHTML={{ __html: sanitizeHTML(post?.content) }}
              />
            )}
            <div className="px-4">
              <span className="inline-block uppercase font-bold text-xl mt-6 mb-4">
                Subscribe to the benzinga podcast today!
              </span>
              {post?.podcast_providers_urls && Object.keys(post?.podcast_providers_urls).length > 0 && (
                <React.Suspense fallback={<div />}>
                  <PodcastPlatformBadges
                    platforms={formatPodcastProvidersUrlsToPodcastPlatformBadgesProps(post.podcast_providers_urls)}
                    variant="default"
                  />
                </React.Suspense>
              )}
            </div>
          </section>
        }
        layoutSidebar={
          <div>
            <React.Suspense fallback={<div />}>
              <CallToActionForm
                // hubspotFormId="************************************"
                beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
                styleOption="secondary"
                subtitle="A newsletter built for market enthusiasts, by market enthusiasts"
                title="Ding Dong, That’s The Sound of Money"
              />
            </React.Suspense>
            <React.Suspense fallback={<div />}>
              <ShowsAndPodcasts podcasts={episodes} shows={shows} />
            </React.Suspense>
          </div>
        }
      />
    </PodcastShowPageContainer>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query, res }) => {
  const showSlug: string | null = sanitizeHTML(query?.showSlug as string)?.split('-id')?.[0] || null;
  const showId: number | null = parseInt(sanitizeHTML(query?.showSlug as string)?.split('-id')?.[1] as string) || null;
  const episodeSlug = sanitizeHTML(query?.episodeSlug as string);

  if (!showId || !episodeSlug) {
    return {
      notFound: true,
    };
  }

  const contentManager = getGlobalSession().getManager(ContentManager);
  const post = await contentManager.getPodcastPost({ post_name: episodeSlug });

  const responseCode = (post?.err?.data as safeErrorStatus)?.status;

  if (responseCode && responseCode >= 400 && responseCode < 600 && post?.ok === undefined) {
    res.statusCode = responseCode;
    return {
      props: {
        error: [404, 410].includes(responseCode) ? responseCode : 503,
      },
    };
  }

  if (!post?.ok) {
    res.statusCode = 404;
    return {
      props: {
        error: 404,
      },
    };
  }

  const showsResponse = await contentManager.getShows({ limit: 4 });
  const episodesResponse = await contentManager.getPodcastEpisodes({ limit: 4, show: showId });

  const shows = formatShowsToPodcastCardData(showsResponse?.ok ?? []);
  const formattedEpisodes = formatPodcastEpisodeToPodcastCardData(episodesResponse?.ok ?? []);

  const relativeUrl = `/podcasts/${showSlug}-id${showId}/${episodeSlug}`;

  const showName = decode(post?.ok?.show?.term.name);

  const breadcrumbs = [
    {
      href: '/podcasts',
      id: 'podcasts',
      name: 'Benzinga Podcasts',
    },
    {
      href: `/podcasts/${showSlug}-id${showId}`,
      id: `podcasts_${showSlug}_SHOW#${showId}`,
      name: showName ?? '',
    },
    {
      href: relativeUrl,
      id: `podcasts_${showSlug}_SHOW#${showId}_EPISODE#${episodeSlug}`,
      name: post?.ok?.title ? decode(post?.ok?.title) : '',
    },
  ];

  const metaProps: MetaProps = {
    canonical: `https://www.benzinga.com${relativeUrl}`,
    dateCreated: '2020-06-27T03:29:22.000Z',
    dateUpdated: new Date().toISOString(),
    description: post?.ok?.summary || '',
    image: post?.ok?.image || null,
    ogImage: post?.ok?.image || null,
    pageType: PageType.Tool,
    title: post?.ok?.title ? `Benzinga Podcasts - ${post?.ok?.title}` : 'Benzinga Podcasts',
    twitterCard: 'summary_large_image',
  };

  const props: PodcastShowPageProps = {
    breadcrumbs,
    episodes: formattedEpisodes,
    metaProps,
    pageTargeting: { BZ_CHANNEL: ['Podcasts'], BZ_PTYPE: 'podcasts' },
    post: post?.ok || null,
    shows,
  };

  return {
    props,
  };
};

export default PodcastEpisodePage;

const PodcastShowPageContainer = styled(Container)`
  .post-content {
    ul {
      display: block;
      list-style-type: disc;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
      list-style-type: disc;
    }
  }
`;
