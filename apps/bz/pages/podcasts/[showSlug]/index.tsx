import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import Vibrant from 'node-vibrant';
import { decode } from 'html-entities';

import { MetaProps, PageType } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { sanitizeHTML } from '@benzinga/frontend-utils';

import { Breadcrumb, ContentManager } from '@benzinga/content-manager';

import { BzImage } from '@benzinga/image';
import { Breadcrumbs, Button, Layout } from '@benzinga/core-ui';
import type { PodcastCardData } from '@benzinga/ui';
import { PodcastCard, ShowsAndPodcasts } from '@benzinga/ui';
import { FeaturedPodcastCard, type FeaturedPodcastCardEpisode, LayoutAdmin } from '@benzinga/blocks';

import { getGlobalSession } from '../../api/session';
import {
  formatShowsToPodcastCardData,
  formatPodcastEpisodeToFeaturedPodcastCardProps,
  formatPodcastEpisodeToPodcastCardData,
} from '../../../utils/formatters/podcast';

import { Container } from '../index';
import PodcastPageFooter from '../../../src/components/Podcasts/PodcastPageFooter';

import { rgbToHex } from '@benzinga/utils';
import { NoFirstRender } from '@benzinga/hooks';
import { safeErrorStatus, SafeType } from '@benzinga/safe-await';
import Error from '../../_error';

const BenzingaProBannerAd = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.BenzingaProBannerAd,
  })),
);

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => ({
    default: module.CallToActionForm,
  })),
);

export interface PodcastShowPageProps {
  breadcrumbs: Breadcrumb[];
  episodes: PodcastCardData[];
  error?: number;
  pageData: {
    title: string;
    description: string;
    slug: string;
    showId: number;
  };
  latestEpisode: FeaturedPodcastCardEpisode | null;
  shows: PodcastCardData[];
  pageTargeting?: Record<string, string | string[]>;
  metaProps: MetaProps;
}

const PodcastShowPage: NextPage<PodcastShowPageProps> = ({
  breadcrumbs,
  episodes: initialEpisodes,
  error,
  latestEpisode,
  pageData,
  shows,
}) => {
  const [episodes, setEpisodes] = React.useState<PodcastCardData[]>(initialEpisodes);
  const [areMoreEpisodesLoading, setAreMoreEpisodesLoading] = React.useState(false);
  const [noMoreResults, setNoMoreResults] = React.useState(initialEpisodes?.length < 9 || false);

  const session = React.useContext(SessionContext);
  if (error) {
    return (<Error statusCode={error} />) as React.ReactElement;
  }
  const handleFetchMoreEpisodes = async () => {
    if (areMoreEpisodesLoading) return;

    try {
      setAreMoreEpisodesLoading(true);
      const contentManager = session.getManager(ContentManager);
      const nextId = episodes[episodes.length - 1]?.id;
      const episodesResponse = await contentManager.getPodcastEpisodes({
        limit: 10,
        next_id: nextId as number,
        show: pageData.showId,
      });
      if (Array.isArray(episodesResponse?.ok)) {
        const formattedEpisodes = formatPodcastEpisodeToPodcastCardData(episodesResponse?.ok);
        setEpisodes([...episodes, ...formattedEpisodes]);
        setAreMoreEpisodesLoading(false);
        if (episodesResponse?.ok?.length < 10) {
          setNoMoreResults(true);
        }
      }
    } catch (error) {
      console.error('Error fetching more episodes', error);
      setAreMoreEpisodesLoading(false);
    } finally {
      setAreMoreEpisodesLoading(false);
    }
  };

  return (
    <Container className="podcasts-page">
      <Layout
        layoutBelow={
          <div className="mt-8 md:mt-2 w-full">
            <BenzingaProBannerAd />
          </div>
        }
        layoutFooter={<PodcastPageFooter />}
        layoutHeader={
          <section className="hero-section relative flex flex-col text-center text-white">
            <span className="z-[1] absolute contents">
              <BzImage
                alt=""
                layout="fill"
                loader={({ src }) => src}
                loading={'eager'}
                objectFit="cover"
                preload={true}
                preloadOptions={{ media: '(min-width: 750px)' }}
                src={'/next-assets/images/podcasts/podcasts-hero-section2.png'}
              />
            </span>
            <div className="z-[2] flex flex-col items-center px-4">
              <div className="mb-4 text-white mt-12">
                <Breadcrumbs data={breadcrumbs} />
              </div>
              <h1
                className="text-5xl text-white uppercase mt-4 max-w-4xl"
                dangerouslySetInnerHTML={{ __html: sanitizeHTML(pageData.title) }}
              />
              <div
                className="text-[#B8CBE5] mt-9 max-w-3xl"
                dangerouslySetInnerHTML={{ __html: sanitizeHTML(pageData.description) }}
              />
              {latestEpisode && (
                <div className="mt-8 mb-12 max-w-5xl rounded-md overflow-hidden">
                  <FeaturedPodcastCard episode={latestEpisode} />
                </div>
              )}
              <NoFirstRender>
                <React.Suspense>
                  <LayoutAdmin
                    className="mb-4"
                    post={null}
                    type="podcast"
                    wordpressUrl={`https://www.benzinga.com/wp-admin/edit.php?post_type=fintech_podcast&taxonomy=podcast_show&term=${pageData.slug}`}
                  />
                  <LayoutAdmin
                    className="mb-4"
                    post={null}
                    type="show"
                    wordpressUrl={`https://www.benzinga.com/wp-admin/term.php?taxonomy=podcast_show&tag_ID=${pageData.showId}&post_type=fintech_podcast`}
                  />
                </React.Suspense>
              </NoFirstRender>
            </div>
          </section>
        }
        layoutHeaderOptions={{ full_width: true }}
        layoutMain={
          <>
            <section className="podcast-list-section w-full flex items-center flex-col gap-2">
              {episodes.map((episode, index) => {
                return (
                  <React.Suspense fallback={<div className="h-8" />} key={`${episode.id}-${index}`}>
                    <PodcastCard
                      actionButtonClassname="uppercase h-8 w-24"
                      actionButtonText="Listen"
                      actionButtonVariant="flat-blue"
                      className="w-full"
                      data={episode}
                      showPodcastPlatformBadges={true}
                      variant="horizontal"
                    />
                  </React.Suspense>
                );
              })}
              {!noMoreResults && (
                <Button
                  className="md:w-48 mt-2 show-more-button"
                  disabled={areMoreEpisodesLoading}
                  isLoading={areMoreEpisodesLoading}
                  onClick={handleFetchMoreEpisodes}
                  variant="flat-blue"
                >
                  SHOW MORE
                </Button>
              )}
            </section>
          </>
        }
        layoutSidebar={
          <div>
            <React.Suspense fallback={<div />}>
              <CallToActionForm
                // hubspotFormId="************************************"
                beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
                styleOption="secondary"
                subtitle="A newsletter built for market enthusiasts, by market enthusiasts"
                title="Ding Dong, That’s The Sound of Money"
              />
            </React.Suspense>
            <React.Suspense fallback={<div />}>
              <ShowsAndPodcasts podcasts={episodes} shows={shows} />
            </React.Suspense>
          </div>
        }
      />
    </Container>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query, res }) => {
  const showSlug: string | null = sanitizeHTML(query?.showSlug as string)?.split('-id')?.[0] || null;
  const showId: number | null = parseInt(sanitizeHTML(query?.showSlug as string)?.split('-id')?.[1] as string) || null;

  if (!showSlug || !showId) {
    return {
      props: {
        error: 404,
      },
      // notFound: true,
    };
  }

  const contentManager = getGlobalSession().getManager(ContentManager);
  const showResponse = await contentManager.getShowById(showId);

  const responseCode = (showResponse?.err?.data as safeErrorStatus)?.status;

  if (responseCode && responseCode >= 400 && responseCode < 600 && showResponse?.ok === undefined) {
    res.statusCode = responseCode;
    return {
      props: {
        error: [404, 410].includes(responseCode) ? responseCode : 503,
      },
    };
  }

  if (!showResponse?.ok) {
    res.statusCode = 404;
    return {
      props: {
        error: 404,
      },
      // notFound: true,
    };
  }

  const showsResponse = await contentManager.getShows({ limit: 4 });
  const episodesResponse = await contentManager.getPodcastEpisodes({ limit: 10, show: showId });

  const shows = formatShowsToPodcastCardData(showsResponse?.ok ?? []);
  const formattedEpisodes = formatPodcastEpisodeToPodcastCardData(episodesResponse?.ok?.slice(1) ?? []);

  const latestEpisode = formatPodcastEpisodeToFeaturedPodcastCardProps(episodesResponse?.ok ?? [])?.[0] ?? null;

  const image = showResponse?.ok?.data?.image || null;
  const name = showResponse?.ok?.data?.name ? decode(showResponse?.ok?.data?.name) : '';
  const description = showResponse?.ok?.data?.description ?? '';
  const slug = showResponse?.ok?.data?.slug ?? '';
  const showThemeColor = showResponse?.ok?.data?.theme_color || null;

  const relativeUrl = `/podcasts/${showSlug}-id${showId}`;

  const metaProps: MetaProps = {
    canonical: `https://www.benzinga.com${relativeUrl}`,
    // dateCreated: '2025-06-27T03:29:22.000Z',
    dateUpdated: new Date().toISOString(),
    description: showResponse?.ok?.data?.description || '',
    image,
    ogImage: image,
    pageType: PageType.Tool,
    title: name ? `Benzinga Podcasts - ${name}` : 'Benzinga Podcasts',
    twitterCard: 'summary_large_image',
  };

  const breadcrumbs = [
    {
      href: '/podcasts',
      id: 'podcasts',
      name: 'Benzinga Podcasts',
    },
    {
      href: relativeUrl,
      id: `podcasts_${showSlug}_SHOW#${showId}`,
      name,
    },
  ];

  if (!latestEpisode?.theme_color && showThemeColor) {
    latestEpisode.theme_color = showThemeColor;
  } else if (!latestEpisode?.theme_color && !showThemeColor && image) {
    const palette = await Vibrant.from(image).getPalette();
    //palette.Muted.rgb
    //palette.DarkVibrant.rgb
    //palette.LightVibrant.rgb
    //palette.Vibrant.rgb
    if (palette.Vibrant) {
      latestEpisode.theme_color = rgbToHex(palette.Vibrant.rgb);
    }
  }

  const props: PodcastShowPageProps = {
    breadcrumbs,
    episodes: formattedEpisodes,
    latestEpisode,
    metaProps,
    pageData: {
      description,
      showId,
      slug,
      title: name,
    },
    pageTargeting: { BZ_CHANNEL: ['Podcasts'], BZ_PTYPE: 'podcasts' },
    shows,
  };

  return {
    props,
  };
};

export default PodcastShowPage;
