import React from 'react';
import { GetServerSideProps } from 'next';
import CalendarPage, { CalendarPageProps, metaInfo } from '../../../calendars/[calendar]';
import { Calendars, fetchRatingsAnalysts } from '@benzinga/calendars';
import { Breadcrumb, ContentManager, WordpressPage, WordpressSidebar } from '@benzinga/content-manager';
import { BasicNewsManager, StoryObject } from '@benzinga/basic-news-manager';
import { AnalystData } from '@benzinga/calendar-manager';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { Rating } from '@benzinga/calendar-ratings-manager';
import { getGlobalSession } from '../../../api/session';

const AnalystCalendarPage: React.FC<CalendarPageProps> = props => {
  return <CalendarPage {...props} isIndividualAnalystPage={true} />;
};

export default AnalystCalendarPage;

export const getServerSideProps: GetServerSideProps = async ({ query, req, res }) => {
  try {
    const calendarTab = sanitizeHTML(query?.tab as string);

    if (calendarTab) {
      const availableTabs = ['all', 'upgrades', 'downgrades', 'initiations'];

      if (!availableTabs.includes(calendarTab as string)) {
        return { notFound: true };
      }
    }

    const tab = Array.isArray(calendarTab) ? calendarTab[0] : calendarTab;
    const analystId = sanitizeHTML(query?.analystId as string);
    let analystName = sanitizeHTML(query?.analystName as string);

    const page = {} as WordpressPage;
    let news: StoryObject[] | null = null;
    let calendarDataSet: Rating[] | null = null;
    let analystData: AnalystData | null = null;
    let breadcrumbs: Breadcrumb[] = [];

    let calendarData = Calendars['analyst-predictions'];

    //page = await getMoneyPage(calendarData.pageId);

    if (calendarData.serverSide) {
      try {
        const data = await calendarData.serverSide.fetchData({
          params: {
            'parameters[analyst_id]': analystId,
            'parameters[date_from]': undefined,
            'parameters[date_to]': undefined,
          },
          tab: 'all',
        });
        if (Array.isArray(data)) {
          calendarDataSet = data;
        }

        analystData = await fetchRatingsAnalysts({ analyst: analystId }).then(res => {
          if (typeof res?.ok === 'object' && !Array.isArray(res.ok)) return res.ok;
          else return null;
        });
      } catch (error) {
        console.error(`Analyst Individual Ratings Calendar Data Set Fetch Fail - analystId: ${analystId}`, error);
      }
    }

    if (!analystData && Array.isArray(calendarDataSet) && calendarDataSet?.[0]) {
      const firstRating = calendarDataSet[0];
      analystData = {
        firm_id: '',
        firm_name: firstRating?.analyst ?? null,
        id: firstRating?.analyst_id ?? null,
        name_full: firstRating?.analyst_name ?? null,
        ratings_accuracy: firstRating?.ratings_accuracy ?? null,
        updated: firstRating?.updated ?? null,
      };
    }

    if (!analystData?.id) {
      return {
        props: {},
        redirect: {
          destination: '/analyst-stock-ratings',
          permanent: false,
        },
      };
    }

    if (calendarData?.routes?.[tab]) {
      calendarData = calendarData.routes[tab];
    }

    const session = getGlobalSession();

    if (analystData && analystData?.name_full) {
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const newsRes = await basicNewsManager.simplyQueryNews(
        {
          search: analystData?.name_full,
        },
        {
          limit: 10,
          type: 'story',
        },
      );
      news = newsRes.ok ?? [];
    }

    const formatPageTitle = (analystData: AnalystData) => {
      if (!analystData) return '';
      const result = analystData?.name_full ? `${analystData?.name_full}'s` : '';
      return result;
    };

    const contentManager = session.getManager(ContentManager);
    const brokerWidgetRes = await contentManager.getWordpressPost(154603);
    const brokerWidget = brokerWidgetRes.ok ?? null;
    const sidebarRes = await contentManager.getWordpressPost(213729);
    const sidebar = (sidebarRes.ok as WordpressSidebar) ?? null;

    if (sidebar) {
      page.sidebar = sidebar;
    }

    if (analystData) {
      breadcrumbs = [
        {
          href: '/calendars',
          id: 'calendars',
          name: 'Calendars',
        },
        {
          href: `/analyst-stock-ratings`,
          id: 'analyst-ratings',
          name: 'Analyst Ratings',
        },
        {
          href: `/analyst-stock-ratings/analyst/${analystData.id}`,
          id: analystData.id,
          name: analystData?.name_full,
        },
      ];
    }

    const convertToSlug = (value: string) => value.toLowerCase()?.replace(' ', '-');
    analystName = encodeURIComponent(convertToSlug(analystData?.name_full));

    const metaProps = metaInfo(calendarData, 'analyst-ratings', tab, true);
    let canonicalPath = `analyst-stock-ratings/analyst/${analystId}/${analystName}`;
    if (tab) {
      canonicalPath = `analyst-stock-ratings/${tab}/analyst/${analystId}/${analystName}`;
    }
    metaProps.canonical = 'https://www.benzinga.com/' + canonicalPath;

    if (req.url && !req.url.includes('_next/data') && !!canonicalPath?.trim() && `/${canonicalPath}` !== req.url) {
      res.setHeader('location', `/${canonicalPath}`);
      res.statusCode = 302;
      res.end();
    }

    metaProps.title = analystData ? formatPageTitle(analystData) : '';

    if (analystData) {
      breadcrumbs = [
        {
          href: '/calendars',
          id: 'calendars',
          name: 'Calendars',
        },
        {
          href: `/analyst-stock-ratings`,
          id: 'analyst-ratings',
          name: 'Analyst Ratings',
        },
        {
          href: `/calendars/analyst-stock-ratings/analyst/${analystData.id}`,
          id: analystData.id,
          name: analystData?.name_full,
        },
      ];
    }

    return {
      props: {
        analystData,
        breadcrumbs,
        brokerWidget,
        calendarDataSet: calendarDataSet,
        calendarSlug: 'analyst-predictions',
        includeAGGridStyles: true,
        initialDateRange: {},
        metaProps,
        news,
        page,
        tab: calendarTab || null,
      },
    };
  } catch {
    return {
      props: {
        analystData: null,
        breadcrumbs: null,
        brokerWidget: null,
        calendarDataSet: null,
        calendarSlug: 'analyst-ratings',
        includeAGGridStyles: true,
        initialDateRange: {},
        metaProps: null,
        news: [],
        page: null,
        tab: null,
      },
    };
  }
};
