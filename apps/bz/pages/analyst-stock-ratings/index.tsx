import { GetServerSideProps } from 'next';
import { getMoneyPage } from '@benzinga/money';
import { Calendars } from '@benzinga/calendars';
import { ContentManager, WordpressPage } from '@benzinga/content-manager';
import { BasicNewsManager, News, NodeQueryParams } from '@benzinga/basic-news-manager';
import CalendarPage, { metaInfo } from '../calendars/[calendar]';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { getGlobalSession } from '../api/session';
import { setParams } from '@benzinga/calendar-manager';
import { getURLParams } from '@benzinga/filter-ui';
import { SafeType } from '@benzinga/safe-await';

export default CalendarPage;

export const getServerSideProps: GetServerSideProps = async ({ query, resolvedUrl }) => {
  const breadcrumbs = [
    {
      href: '/calendars',
      id: 'calendars',
      name: 'Calendars',
    },
    {
      href: `/analyst-stock-ratings`,
      id: 'analyst-ratings',
      name: 'Analyst Ratings',
    },
  ];

  try {
    const calendarTab = sanitizeHTML(query?.tab as string);

    if (calendarTab) {
      const availableTabs = ['all', 'upgrades', 'downgrades', 'initiations'];

      if (!availableTabs.includes(calendarTab)) {
        return { notFound: true };
      }
    }

    const tab = Array.isArray(calendarTab) ? calendarTab[0] : calendarTab;

    let page: WordpressPage | null = null;
    let news: SafeType<News[]> | null = null;
    let calendarDataSet: any[] | null = null;

    let calendarData = Calendars['analyst-ratings'];

    const newsParams: NodeQueryParams = {
      displayOutput: 'abstract',
      page: 0,
      ...(calendarData?.news?.params ?? {}),
    };

    if (calendarData.serverSide) {
      try {
        const urlParams = getURLParams(resolvedUrl);
        const params = setParams({
          dateFrom: urlParams?.date_from,
          dateTo: urlParams?.date_to,
          symbols: urlParams?.tickers,
          ...(urlParams || {}),
        });

        const data = await calendarData.serverSide.fetchData({
          params,
          tab: 'all',
        });

        if (Array.isArray(data)) {
          calendarDataSet = data;
        }
      } catch (error) {
        console.error('Analyst Ratings Calendar Data Set Fetch Fail', error);
      }
    }

    if (calendarData?.routes?.[tab]) {
      calendarData = calendarData.routes[tab];
    }

    const session = getGlobalSession();

    if (calendarData) {
      page = await getMoneyPage(calendarData.pageId);
      const basicNewsManager = session.getManager(BasicNewsManager);
      news = await basicNewsManager.fetchNodes(newsParams);
    }

    const contentManager = session.getManager(ContentManager);
    const brokerWidgetRequest = await contentManager.getWordpressPost(154603);
    const brokerWidget = brokerWidgetRequest.ok ?? null;

    const metaProps = metaInfo(calendarData, 'analyst-ratings', tab, true);
    metaProps.canonical = 'https://www.benzinga.com/analyst-stock-ratings';
    if (tab) {
      metaProps.canonical = `${metaProps.canonical}/${tab}`;
    }

    const channelTargeting: string[] = [];
    calendarData?.news?.params?.channels?.forEach(channel => {
      channelTargeting.push(channel);
    });

    newsParams?.channels?.forEach(channel => {
      if (!channelTargeting.includes(channel)) {
        channelTargeting.push(channel);
      }
    });

    return {
      props: {
        breadcrumbs,
        brokerWidget,
        calendarDataSet: calendarDataSet,
        calendarSlug: 'analyst-ratings',
        campaignifyUTM: 'analyst-ratings',
        campaignifyVariant: '27-profit-every-20-days',
        includeAGGridStyles: true,
        metaProps: metaProps || null,
        news: news?.ok || [],
        page: page || null,
        pageTargeting: { BZ_CHANNEL: channelTargeting, BZ_PTYPE: 'calendar' },
        tab: calendarTab || null,
      },
    };
  } catch {
    return {
      props: {
        breadcrumbs,
        brokerWidget: null,
        calendarDataSet: null,
        calendarSlug: 'analyst-ratings',
        includeAGGridStyles: true,
        metaProps: null,
        news: [],
        page: null,
        tab: null,
      },
    };
  }
};
