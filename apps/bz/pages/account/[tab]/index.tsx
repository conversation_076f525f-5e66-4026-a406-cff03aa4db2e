import { GetServerSideProps } from 'next/types';
import { AccountSettings, AccountSettingsProps, getMetaProps } from '../index';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const AccountPageTabs = ['billing', 'chat', '', 'news-alerts', 'watchlist-alert'];

const AccountTabPage = ({ metaProps, tab }: AccountSettingsProps) => {
  return <AccountSettings metaProps={metaProps} tab={tab} />;
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  try {
    const selectedTab = sanitizeHTML(params?.tab as string);
    const isValidTab = AccountPageTabs.includes(selectedTab);

    if (!isValidTab) {
      return {
        redirect: {
          destination: '/account',
          permanent: false,
        },
      };
    }

    const metaProps = await getMetaProps(selectedTab);

    return {
      props: {
        headerProps: {
          hideQuoteBar: true,
        },
        metaProps: metaProps,
        tab: selectedTab,
      },
    };
  } catch (err) {
    console.log('Error fetching account settings', err);
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
};

export default AccountTabPage;
