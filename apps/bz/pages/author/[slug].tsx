import React from 'react';
import { GetServerSideProps } from 'next';
import { getGlobalSession } from '../api/session';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { AuthorTemplate, AuthorTemplateProps, getAuthorServerSideProps } from '@benzinga/templates';
import { ArticleManager } from '@benzinga/article-manager';

const AuthorPage: React.FC<AuthorTemplateProps> = props => {
  return <AuthorTemplate {...props} />;
};

export default AuthorPage;

export const getServerSideProps: GetServerSideProps = async ({ query: { slug }, res }) => {
  // ToDo: Update the content data source.  The current API has limited properties and is also limited to BZ (not PRO) content and will throw a 404 otherwise
  const session = getGlobalSession();
  const authorSlug = sanitizeHTML(slug as string);
  const excludeContentTypes = ['benzinga_wire_india', 'benzinga_partner'];

  try {
    const articleManager = session.getManager(ArticleManager);
    const isAuthorExpired = await articleManager.checkExpiredUrl('author', 'author/' + authorSlug);
    if (isAuthorExpired?.ok?.status === 'success') {
      res.statusCode = 410; // Gone
      return {
        props: {
          author: null,
          headerProps: {
            hideBanner: true,
            hideMenuBar: true,
            hideQuoteBar: true,
            hideSearchBar: true,
            isMainDrawerVisible: true,
          },
          layout: null,
          news: [],
        },
      };
    }

    return getAuthorServerSideProps(session, authorSlug, 'https://www.benzinga.com', excludeContentTypes);
  } catch (error) {
    res.statusCode = 404;
    console.error(`author page error: ${authorSlug}`, error);
    return {
      props: {
        author: null,
        headerProps: {
          hideBanner: true,
          hideMenuBar: true,
          hideQuoteBar: true,
          hideSearchBar: true,
          isMainDrawerVisible: true,
        },
        layout: null,
        news: [],
      },
    };
  }
};
