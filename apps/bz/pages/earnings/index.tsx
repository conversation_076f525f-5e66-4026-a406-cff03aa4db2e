import React from 'react';
import { NextPage } from 'next';

import { ShareButtons } from '@benzinga/ui';
import { useSponsoredContentArticle } from '@benzinga/content-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import { CalendarType } from '@benzinga/calendar-manager';
import styled from '@benzinga/themetron';

import {
  getServerSideProps as CalendarGetServerSideProps,
  CalendarPageProps,
  getCalendarData,
} from '../calendars/[calendar]';
import CalendarsList from '../../src/components/CalendarsList';
import { ToolsPageMain } from '../../app/tools/ToolsPageMain';

import { ContentFeed } from '@benzinga/news';
import { MoneyBlocksLayout, MoneyPageTemplate } from '@benzinga/money';
import { EarningsCalendar } from '@benzinga/calendars';

const EarningsPage: NextPage<CalendarPageProps> = ({
  brokerWidget,
  calendarDataSet,
  initialDateRange,
  metaProps,
  news,
  page,
}) => {
  const calendarData = getCalendarData('earnings');
  const session = React.useContext(SessionContext);
  const [sponsoredArticles] = useSponsoredContentArticle(session);

  const tabs = [
    {
      key: '/earnings',
      name: 'Overview',
    },
    {
      key: '/earnings/trading-ideas',
      name: 'Trading Ideas',
    },
  ];

  return (
    <EarningsPageContainer className="earnings-page">
      {page && (
        <MoneyPageTemplate
          layoutAboveArticle={
            <div className="mb-4">
              <ContentFeed
                contentId={'Recent Earning News'}
                excludeIds={[]}
                isInfinite={false}
                limit={20}
                loadMore={true}
                nodes={news}
                poolLatest={false}
                poolLatestInterval={30000}
                query={{ channels: ['Earnings'], pageSize: 10 }}
                realtime={false}
                showSponsoredContent={true}
                sponsoredNodes={sponsoredArticles}
                title={'Recent Earning News'}
              />
            </div>
          }
          layoutFooter={
            <div className="mx-4">
              <h2>Explore Benzinga&apos;s Financial Tools</h2>
              <ToolsPageMain brokerWidget={brokerWidget} />
              {page?.footer && <MoneyBlocksLayout post={page.footer} />}
            </div>
          }
          layoutHeader={
            <div
              className={`calendar-container ${
                calendarData?.separateTitle || calendarData?.description ? 'mt-4' : 'lg:mt-4'
              }`}
            >
              <CalendarsList hideLabels={['Earnings Calendar']} />
              <EarningsCalendar
                additionalFetchParams={{
                  'parameters[date_from]': undefined,
                  'parameters[date_to]': undefined,
                }}
                calendar={'earnings' as CalendarType}
                calendarData={calendarData}
                hasStockTableStyling={true}
                hiddenColumns={[]}
                hideFilters={false}
                initialData={calendarDataSet || null}
                initialDateRange={initialDateRange}
                leftSlot={
                  <ShareButtons
                    className="earnings-share"
                    title={calendarData?.title}
                    url={`https://benzinga.com/earnings`}
                  />
                }
              />
            </div>
          }
          layoutTabs={tabs}
          layoutTabsOptions={{ prefetchAllTabs: true }}
          post={page}
          tabOptions={{
            rightSideElement: (
              <div className="right-side-element text-gray-500 text-sm pb-1.5 hidden md:block">
                {metaProps.title.replace('Earnings Calendar', 'Data')}
              </div>
            ),
          }}
          tabsTitle="Earnings"
          width="wide"
        />
      )}
    </EarningsPageContainer>
  );
};

export const getServerSideProps = CalendarGetServerSideProps;

export default EarningsPage;

const EarningsPageContainer = styled.div`
  &.earnings-page {
    .tabs-container.lifted-variant {
      .tab-list-wrapper {
        border-bottom: none;
        margin-bottom: 0;
      }
    }
    .calendar-container {
      max-width: 1300px;
      margin: 1rem auto 2rem auto;
      position: relative;
      width: 100%;

      display: flex;
      flex-direction: row;
      gap: 2rem;

      .calendar-table {
        width: 100%;
        overflow-x: auto;
      }

      @media (max-width: 1300px) {
        padding-left: 1rem;
        padding-right: 1rem;
        max-width: 100vw;
      }

      @media (max-width: 800px) {
        flex-direction: column-reverse;
      }
    }
    .page-share-buttons {
      margin-bottom: 0px;
    }
  }
`;
