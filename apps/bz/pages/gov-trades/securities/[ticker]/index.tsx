import React from 'react';
import { useRouter } from 'next/router';
import styled from '@benzinga/themetron';
import classNames from 'classnames';

import { getQuoteProfile } from '../../../api/quote';
import { getGlobalSession } from '../../../../pages/api/session';
import { injectLogos } from '@benzinga/calendars';
import { TrackingManager } from '@benzinga/tracking-manager';

import { GovTradesManager } from '@benzinga/gov-trades';
import {
  GovernmentTradesHeader,
  GovernmentTable,
  SecurityRecentColumnsDef,
  SecurityTraderColumnsDef,
  TraderRow,
  GovSearchBarV2,
} from '../../../../src/components/GovTrades';
import { Meta, MetaProps, PageType } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { StockReportsManager } from '@benzinga/stock-reports-manager';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { AuthContainer } from '@benzinga/auth-ui';
import { NoFirstRender } from '@benzinga/hooks';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { formatTickerInURLToTicker, Nullable } from '@benzinga/utils';
import { QuoteProfile } from '../../../../src/entities/quoteEntity';

const getMetaProps = (profile: Partial<Nullable<QuoteProfile>>): MetaProps => {
  const name = profile?.richQuoteData?.issuerShortName;
  const symbol = profile?.symbol?.toUpperCase();
  const titleName = `${name ? name + ` (${symbol})` : symbol}`;

  return {
    canonical: `https://www.benzinga.com/gov-trades/securities/${symbol}`,
    description: `Track the latest Congressional trades of ${titleName} stock. See which US lawmakers are buying or selling ${symbol}, with dates, trade amounts, and disclosure details.`,
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: PageType.Tool,
    title: `${titleName} Government Trades - Latest Congressional Stock Transactions`,
    dateCreated: '2024-03-13T00:00:00Z',
    dateUpdated: null,
  };
};

const SecuritiesDrilldownPage = props => {
  const router = useRouter();
  const session = React.useContext(SessionContext);
  const [securitiesList, setSecuritiesList] = React.useState(props?.securitiesList ?? []);
  const [profile, setProfile] = React.useState(props?.profile ?? null);
  const [stats, setStats] = React.useState(
    props?.stats ?? {
      largest_traders: [],
      largest_trades: [],
      most_active_traders: [],
      recent_trades: [],
      top_performing: [],
    },
  );
  const [pageError, setPageError] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(!!props?.error || props?.profile === undefined);
  const [hasReport, setHasReport] = React.useState(props?.hasReport ?? false);
  const [meta, setMeta] = React.useState(props?.metaProps);
  const isLoggedIn = useIsUserLoggedIn();
  const [blurPage, setBlurPage] = React.useState(false);

  const paramTicker = Array.isArray(router?.query?.ticker) ? router.query.ticker[0] : router?.query?.ticker;
  const symbol = props?.symbol ?? paramTicker;
  const companyName = props?.profile?.richQuoteData?.issuerShortName;
  const titleName = `${companyName ? companyName + ` (${symbol})` : symbol}`;

  const tabsData = [
    {
      columnsDef: SecurityRecentColumnsDef,
      description: `See the most up-to-date ${companyName || symbol} trades disclosed by US lawmakers, including purchase and sale amounts, transaction dates, and reporting details. View the most recent ${companyName || symbol} trades made by congress members.`,
      key: 'recent_trades',
      title: `Recent ${titleName} Trades by Congress Members`,
      label: 'Recent Trades',
    },
    {
      columnsDef: SecurityRecentColumnsDef,
      description: `Explore the biggest ${companyName || symbol} transactions made by Congress members, showcasing trades with the highest reported values.`,
      key: 'largest_trades',
      title: `Largest Congressional Trades in ${titleName}`,
      label: 'Largest Trades',
    },
    {
      columnsDef: SecurityTraderColumnsDef,
      description: `Review the most profitable ${companyName || symbol} trades made by lawmakers, featuring transactions with the highest estimated returns to date.`,
      hideColumns: ['ttm_total_trades', 'ttm_total_volume'],
      key: 'top_performing',
      title: `Top Performing Congressional ${titleName} Trades`,
      label: 'Top Performing',
    },
    {
      columnsDef: SecurityTraderColumnsDef,
      description: `Discover which members of Congress have traded the highest total dollar volume in ${companyName || symbol} shares, highlighting the most significant individual traders.`,
      hideColumns: ['ttm_overall_return', 'ttm_total_trades'],
      key: 'largest_traders',
      title: `Congress Members Trading the Largest Volume of ${titleName}`,
      label: 'Largest Traders',
    },
    {
      columnsDef: SecurityTraderColumnsDef,
      description: `View the congress members with the most trades in ${companyName || symbol}. It highlights the
      officials that have been the most active in trading this stock.`,
      hideColumns: ['ttm_overall_return', 'ttm_total_volume'],
      key: 'most_active_traders',
      title: `Most Active Traders in ${titleName}`,
      label: 'Most Active Traders',
    },
  ];

  const [activeTab, setActiveTab] = React.useState(0);
  const [tableColumns, setTableColumns] = React.useState(tabsData[activeTab].columnsDef);
  const [tableData, setTableData] = React.useState(props?.stats?.recent_trades ?? []);

  React.useEffect(() => {
    const getClientSideProps = async (paramTicker: string) => {
      const securityData = await session.getManager(GovTradesManager).getSecurity(paramTicker);
      const profile = await getQuoteProfile(paramTicker);
      if (!securityData?.ok || profile?.notFound) {
        setPageError(true);
        setIsLoading(false);
        return;
      }
      setProfile(profile);
      setStats(securityData?.ok?.stats);
      setTableData(securityData?.ok?.stats?.recent_trades);
      const isSupported = await session.getManager(StockReportsManager).isSupportedTicker(paramTicker);
      setHasReport(isSupported);

      const securities = await session.getManager(GovTradesManager).getSecurities();
      if (securities?.ok && securities?.ok?.most_active && securities?.ok?.most_active?.length > 0) {
        const mostActive = await injectLogos(
          securities?.ok?.most_active?.map(trade => {
            trade.ticker = trade.symbol;
            return trade;
          }),
        );
        const securitiesList = mostActive.sort((a, b) => {
          return a.company_name.localeCompare(b.company_name);
        });
        setSecuritiesList(securitiesList);
      }
      setIsLoading(false);
    };

    if (props?.error || paramTicker !== props?.symbol) {
      getClientSideProps(paramTicker as string);
    }
  }, [session, props?.error, router?.query?.ticker, props?.symbol, paramTicker]);

  React.useEffect(() => {
    const meta = getMetaProps(profile ?? { symbol: symbol });
    if (props?.metaProps?.canonical !== meta.canonical) {
      setMeta(meta);
      session.getManager(TrackingManager).setMeta(meta);
    }
  }, [profile, props?.metaProps?.canonical, session, symbol, setMeta]);

  React.useEffect(() => {
    if (!isLoggedIn) {
      setBlurPage(true);
    }
  }, [isLoggedIn, symbol]);

  const handleTabClick = (index: number) => {
    const tabData = tabsData[index];
    setActiveTab(index);
    setTableColumns(tabData.columnsDef);
    setTableData(stats[tabData.key] ?? []);

    session.getManager(TrackingManager).trackPageEvent('view', {
      page_section: `gov-trades-securities-${symbol}`,
      page_tab: tabData.title,
    });
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      router.push('/gov-trades/securities');
    }
  };

  return (
    <>
      {isLoading ? (
        <div className="w-full h-96">
          <GovernmentTradesHeader activeTabTitle="Securities" />
          <div className="w-full h-full flex justify-center items-center">
            <div>Loading...</div>
          </div>
        </div>
      ) : pageError ? (
        <div className="w-full h-96">
          <GovernmentTradesHeader activeTabTitle="Securities" />
          <div className="w-full h-full flex flex-col justify-center items-center">
            <div>No congressional trades for ticker {symbol?.toUpperCase()}, please try another ticker.</div>
            <button className="px-4 py-2 bg-bzblue-600 text-white rounded-md mt-8" onClick={handleGoBack}>
              Go back
            </button>
          </div>
        </div>
      ) : (
        <SecuritiesDrilldownPageWrapper>
          <GovernmentTradesHeader activeTabTitle="Securities" />
          <div className={classNames({ 'blur-md pointer-events-none': blurPage, 'main-body gap-4': true })}>
            <div className="mb-4">
              <GovSearchBarV2 securities={securitiesList} members={props?.members?.congressional_members ?? []} />
            </div>
            {/* Company profile */}
            <div className="flex gap-4 lg:flex-row flex-col items-center">
              <div className="flex flex-col md:flex-row gap-4 overflow-hidden w-full">
                <img
                  alt={`${profile?.richQuoteData?.companyStandardName ?? symbol} logo`}
                  className="company-logo desktop max-h-48"
                  src={profile?.logoUrl}
                />
                <div className="flex flex-col">
                  <div className="header-wrapper">
                    <img
                      alt={`${profile?.richQuoteData?.companyStandardName ?? symbol} logo`}
                      className="company-logo mobile max-h-48"
                      src={profile?.logoUrl}
                    />
                    <div>
                      <h1 className="company-name">
                        {profile?.richQuoteData?.companyStandardName} ({symbol?.toUpperCase()}) Government Trades -
                        Latest Congressional Stock Transactions
                      </h1>
                      {/* <div className="company-symbol">
                    ({profile?.richQuoteData?.bzExchange})
                  </div> */}
                    </div>
                  </div>
                  <div className="mt-4 md:mt-2 mb-4">
                    <a className="company-link-button quote" href={`/quote/${symbol}`} target="_blank ">
                      View Quote Details
                    </a>
                    {hasReport && (
                      <a
                        className="company-link-button report"
                        href={`/quote/${symbol?.toUpperCase()}/report`}
                        target="_blank "
                      >
                        Get Stock Report
                      </a>
                    )}
                  </div>
                  <p className="company-description text-sm w-full inline-block">
                    {profile?.tickerDetails?.company?.longDescription || 'No description available'}
                  </p>
                </div>
              </div>
            </div>
            {/* Top trades */}
            <div className="mt-4">
              <h2 className="uppercase text-base pb-2">top performing ({symbol}) trades</h2>
              <div className="flex gap-4 overflow-y-hidden flex-wrap flex-row h-12">
                {stats?.top_performing?.map((trader, index) => <TraderRow key={index} {...trader} />)}
              </div>
            </div>
            {/* Tabs */}
            <div className="tabs-row flex items-center gap-4 wrap mt-4">
              {tabsData.map((tab, index) => (
                <button
                  className={`securities-tab ${activeTab === index ? 'active' : ''}`}
                  key={index}
                  onClick={() => handleTabClick(index)}
                >
                  <div className="flex items-center w-full justify-center">
                    <div className={`tab-title ${activeTab === index ? 'active' : ''}`}>{tab.label}</div>
                  </div>
                </button>
              ))}
            </div>
            <div className="tab-content">
              <div className="table-wrapper">
                <div className="flex flex-col w-full justify-between items-start">
                  <h3 className="table-title">{tabsData[activeTab].title}</h3>
                  <p>{tabsData[activeTab].description}</p>
                </div>
                <GovernmentTable
                  columnsDef={tableColumns}
                  height={500}
                  hiddenColumns={tabsData[activeTab].hideColumns}
                  hideTable={true}
                  rowData={tableData}
                />
              </div>
            </div>
          </div>
          <NoFirstRender>
            {!isLoggedIn && (
              <AuthContainer
                authMode="register"
                contentType="gov-trades"
                iterationStyle="edge-hard"
                placement="government-trades"
                preventRedirect={true}
              />
            )}
          </NoFirstRender>
        </SecuritiesDrilldownPageWrapper>
      )}
    </>
  );
};

const SecuritiesDrilldownPageWrapper = styled.div`
  font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
  background: #e1ebfa;
  .main-body {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;
    @media (max-width: 800px) {
      padding: 1rem;
    }

    .table-title {
      color: #192940;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px;
      padding-bottom: 4px;
    }

    .card {
      border-radius: 12px;
      background: #fff;
      border: 1px solid #ceddf2;
      padding: 24px;

      .card-title {
        font-size: 24px;
        font-weight: 700;
      }

      .light-text {
        color: #5b7292;
        font-size: 16px;
        font-weight: 400;
      }

      .divider {
        width: 100%;
        background: #e1ebfa;
        height: 1px;
      }
    }

    .show-more {
      cursor: pointer;
      color: #3f83f8;
      text-transform: uppercase;
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      padding: 8px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #ffffff;
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
      border: 1px solid #e1ebfa;
      margin-top: -1px;
    }

    .chart-label-wrapper {
      display: flex;
      align-items: center;
      padding-top: 16px;
      gap: 16px;

      .chart-key-label {
        display: flex;
        gap: 12px;
        align-items: center;

        .box {
          width: 32px;
          height: 16px;
          border-radius: 4px;
        }
        .label {
          color: #5b7292;
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }

  .company-logo {
    border-radius: 12px;
    object-fit: cover;
    background: #fff;
    border: 1px solid #ceddf2;

    &.desktop {
      height: 100%;
      width: 240px;
    }
    &.mobile {
      height: 100%;
      width: 60px;
      display: none;
    }
  }
  @media (max-width: 768px) {
    .company-logo.desktop {
      display: none;
    }
    .company-logo.mobile {
      display: block;
    }
  }

  .company-name {
    color: #1c2737;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    margin-bottom: 8px;
  }
  .company-symbol {
    color: #878787;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin-top: 12px;
  }
  .company-link-button {
    color: white;
    font-size: 14px;
    padding: 0.5rem 0.75rem;
    background-color: #3f83f8;
    width: fit-content;
    height: fit-content;
    border-radius: 4px;
    text-transform: 'capitalize';

    &.report {
      background-color: #5b7292;
      margin-left: 8px;
    }
  }

  .header-wrapper {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;

    > div {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 800px) {
      > div {
        flex-wrap: wrap;
        gap: 4px;
        .company-symbol {
          margin-top: 0;
        }
      }
    }
  }

  .table-wrapper {
    padding: 16px 32px;
    background: #f2f8ff;
    border-radius: 12px;
    border: 1px solid #e1ebfa;
    @media (max-width: 800px) {
      padding: 8px 16px;
    }
  }

  .tabs-row {
    overflow-x: auto;
  }

  .securities-tab {
    cursor: pointer;
    max-width: 240px;
    width: 100%;
    background: linear-gradient(180deg, rgba(225, 235, 250, 0) 0%, rgba(225, 235, 250, 0.4) 100%);
    display: flex;
    justify-content: center;
    height: 40px;
    padding: 12px 8px;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;

    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #ceddf2;

    &.active {
      background: #ffffff;
      border-bottom: none;
    }

    .tab-title {
      font-size: 16px;
      white-space: nowrap;
      font-weight: 700;
      color: #5b7292;
      line-height: normal;
      text-transform: uppercase;

      &.active {
        color: #3f83f8;
      }
    }

    .tab__value {
      font-size: 12px;
      color: #828282;
      font-weight: 700;
      line-height: normal;
    }
  }

  .tab-content {
    background: #ffffff;
    border: 1px solid #ceddf2;
    padding: 16px;
    margin: 0 -40px;
    margin-top: -1px;

    @media (max-width: 800px) {
      margin: 0;
      padding: 0px;

      .table-wrapper {
        background: white;
        border: none;

        .benzinga-core-table-container table {
          border: 1px solid #e1ebfa;
          border-radius: 8px;
        }
      }
    }
  }
`;

export const getServerSideProps = async ({ query }: { query: { ticker: string } }) => {
  try {
    let symbol = sanitizeHTML(query?.ticker as string);
    if (symbol.includes('-')) {
      symbol = formatTickerInURLToTicker(symbol);
    }

    const session = getGlobalSession();
    const securityData = await session.getManager(GovTradesManager).getSecurity(symbol);
    const profile = await getQuoteProfile(symbol, false, '');

    if (!securityData.ok || profile.notFound) {
      return {
        props: {
          error: true,
          symbol: symbol,
        },
      };
    }

    const securities = await session.getManager(GovTradesManager).getSecurities();
    let mostActive = securities.ok?.most_active?.map(trade => {
      trade.ticker = trade.symbol;
      return trade;
    });
    mostActive = mostActive ? await injectLogos(mostActive) : [];

    const securitiesList = mostActive.sort((a, b) => {
      return a.symbol.localeCompare(b.symbol);
    });

    const dateUpdated = securityData.ok?.updated ?? securityData.ok?.stats?.recent_trades?.[0]?.updated ?? null;
    const metaProps: MetaProps = getMetaProps(profile);
    metaProps.dateUpdated = dateUpdated ? new Date(dateUpdated * 1000).toISOString() : null;

    const hasReport = await session.getManager(StockReportsManager).isSupportedTicker(symbol);
    const members = await session.getManager(GovTradesManager).getMembers();

    return {
      props: {
        hasReport,
        metaProps,
        profile: profile,
        members: members?.ok ?? null,
        securitiesList: securitiesList,
        stats: securityData.ok.stats,
        symbol: symbol,
      },
    };
  } catch (err) {
    console.error(err);
    const metaProps: MetaProps = getMetaProps({ symbol: query?.ticker });
    return {
      props: {
        error: true,
        metaProps,
        symbol: query?.ticker?.toUpperCase(),
      },
    };
  }
};

export default SecuritiesDrilldownPage;
