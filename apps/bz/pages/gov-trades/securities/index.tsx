import React, { useState } from 'react';
import styled from '@benzinga/themetron';

import { toTitleCase } from '@benzinga/utils';
import { getGlobalSession } from '../../api/session';
import { injectLogos } from '@benzinga/calendars';
import { TrackingManager } from '@benzinga/tracking-manager';

import { GovTradesManager, GovernmentTrade, SecuritiesSymbol, SecurityTrader } from '@benzinga/gov-trades';
import {
  GovernmentTable,
  GovernmentTradesHeader,
  GovSearchBarV2,
  TradesColumnsDef,
  SecuritiesTradedColumnsDef,
  TraderRow,
  TradeRow,
} from '../../../src/components/GovTrades';
import { Meta, MetaProps, PageType } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { usePermission } from '@benzinga/user-context';
import { AiOutlineLock } from 'react-icons/ai';
import { AuthContainer } from '@benzinga/auth-ui';

const tabsData = [
  {
    columnsDef: TradesColumnsDef,
    description: 'View the most recent trades made by congress members.',
    key: 'recentTrades',
    title: 'Recent Trades',
  },
  {
    columnsDef: TradesColumnsDef,
    description: `View the largest trades made by congress members. It highlights the
      trades that have been made with the highest amount of money.`,
    key: 'largestTrades',
    title: 'Largest Trades',
  },
  {
    columnsDef: SecuritiesTradedColumnsDef,
    description: `View the most traded stocks by congress members. It highlights the
      stocks that have been traded the most frequently by these officials.`,
    hideColumns: ['total_traders'],
    key: 'mostTraded',
    title: 'Top Traded',
  },
  {
    columnsDef: SecuritiesTradedColumnsDef,
    description: `View the stocks with the most traders from congress. It highlights the
      stocks that have been traded by the highest number of officials.`,
    hideColumns: ['total_traded'],
    key: 'mostTraders',
    title: 'Congress Top Pick',
  },
];

interface mostType {
  company: string;
  exchange: string;
  symbol: string;
  ticker?: string;
  total_traded: number;
}
interface SecuritiesOverviewProps {
  largestTrades: GovernmentTrade[];
  mostActive: SecuritiesSymbol[];
  mostTraded: mostType[];
  mostTraders: mostType[];
  recentTrades: GovernmentTrade[];
  securitiesList: SecuritiesSymbol[];
  topGainers: SecurityTrader[];
  topLosers: SecurityTrader[];
}

const metaProps: MetaProps = {
  canonical: 'https://www.benzinga.com/gov-trades/securities',
  dateCreated: '2024-03-13T00:00:00Z',
  description:
    "Explore recent trades, largest transactions, top traded securities, and Congress's top picks in our Securities page. Stay updated on the latest financial activities of elected officials, identify trending stocks, and uncover insights into congressional investment strategies.",
  image:
    'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
  pageType: PageType.Tool,
  title: 'Securities - Government Trades',
};

const GovMembersPage = props => {
  const { metaProps: meta } = props;
  const [rightActiveTab, setRightActiveTab] = React.useState('gainers');
  const [activeTradeTab, setActiveTradeTab] = React.useState(0);
  const [tradersLimit, setTradersLimit] = useState(5);
  const [activeLimit, setActiveLimit] = useState(5);
  const [mostActive, setMostActive] = useState(props?.mostActive ?? []);
  const [topGainers, setTopGainers] = useState(props?.topGainers ?? []);
  const [topLosers, setTopLosers] = useState(props?.topLosers ?? []);
  const [securitiesList, setSecuritiesList] = useState(props?.securitiesList ?? []);

  const [tableData, setTableData] = React.useState({
    largestTrades: props?.largestTrades ?? [],
    mostTraded: props?.mostTraded ?? [],
    mostTraders: props?.mostTraders ?? [],
    recentTrades: props?.recentTrades ?? [],
  });
  const [tableColumns, setTableColumns] = React.useState(tabsData[0].columnsDef);
  const [tableRows, setTableRows] = React.useState(props?.recentTrades?.slice(0, 20) ?? []);
  const [recentTradesLimit, setRecentTradesLimit] = useState(20);
  const session = React.useContext(SessionContext);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const [showLoginModal, setShowLoginModal] = useState(false);

  React.useEffect(() => {
    session.getManager(TrackingManager).setMeta(meta);
  }, [session, meta]);

  React.useEffect(() => {
    const getClientSideProps = async () => {
      const securities = await session.getManager(GovTradesManager).getSecurities();
      const updatedData: SecuritiesOverviewProps = {
        largestTrades: [],
        mostActive: [],
        mostTraded: [],
        mostTraders: [],
        recentTrades: [],
        securitiesList: [],
        topGainers: [],
        topLosers: [],
      };
      if (securities?.ok?.recent_trades) {
        let recentTrades = securities.ok?.recent_trades?.map(trade => {
          return {
            ...trade,
            company_name: trade?.security?.name,
            ticker: trade?.security?.ticker,
            trade_amount: trade?.amount,
          };
        });
        recentTrades = recentTrades ? await injectLogos(recentTrades) : [];
        updatedData.recentTrades = recentTrades;
        setTableRows(recentTrades.slice(0, 20));
      }
      if (securities?.ok?.most_active) {
        let mostActive = securities.ok?.most_active?.map(trade => {
          trade.ticker = trade?.symbol;
          return trade;
        });
        mostActive = mostActive ? await injectLogos(mostActive) : [];
        updatedData.mostActive = mostActive;
        setMostActive(mostActive);
        setSecuritiesList(
          [...mostActive].sort((a, b) => {
            return a.company_name.localeCompare(b.company_name);
          }),
        );
      }
      if (securities?.ok?.most_traded) {
        let mostTraded = securities.ok?.most_traded?.map(trade => {
          trade.ticker = trade?.symbol;
          return trade;
        });
        mostTraded = mostTraded ? await injectLogos(mostTraded) : [];
        updatedData.mostTraded = mostTraded;
      }
      if (securities?.ok?.most_traders) {
        let mostTraders = securities.ok?.most_traders?.map(trade => {
          trade.ticker = trade?.symbol;
          return trade;
        });
        mostTraders = mostTraders ? await injectLogos(mostTraders) : [];
        updatedData.mostTraders = mostTraders;
      }
      if (securities?.ok?.largest_trades) {
        let largestTrades = securities.ok?.largest_trades?.map(trade => {
          return {
            ...trade,
            company_name: trade?.security?.name,
            ticker: trade?.security?.ticker,
            trade_amount: trade?.amount,
          };
        });
        largestTrades = largestTrades ? await injectLogos(largestTrades) : [];
        updatedData.largestTrades = largestTrades;
      }
      if (securities?.ok?.top_traders) {
        setTopGainers(securities.ok?.top_traders?.gainers ?? []);
        setTopLosers(securities.ok?.top_traders?.losers ?? []);
        updatedData.topGainers = securities.ok?.top_traders?.gainers ?? [];
        updatedData.topLosers = securities.ok?.top_traders?.losers ?? [];
      }
      setTableData(updatedData);
    };

    if (!props?.recentTrades || props?.recentTrades?.length === 0) {
      getClientSideProps();
    }
  }, [session, props?.recentTrades]);

  const handleRightTabClick = (tab: string) => {
    setRightActiveTab(tab);
  };

  const handleTradeTabClick = (index: number) => {
    setActiveTradeTab(index);
    const tabData = tabsData[index];

    session.getManager(TrackingManager).trackPageEvent('view', {
      page_section: 'gov-trades-securities',
      page_tab: tabData.title,
    });

    setTableColumns(tabData.columnsDef);
    if (tabData.key === 'recentTrades') {
      setTableRows(tableData.recentTrades?.slice(0, recentTradesLimit));
    } else {
      setTableRows(tableData[tabsData[index].key]);
    }
  };

  const showMoreRecent = () => {
    const newLimit = recentTradesLimit + 10;
    setRecentTradesLimit(newLimit);
    setTableRows(tableData.recentTrades?.slice(0, newLimit));
  };

  const moreToShow = () => {
    const limit = rightActiveTab === 'gainers' ? topGainers.length : topLosers.length;
    return limit > tradersLimit;
  };

  return (
    <GovernmentTradesWrapper>
      <GovernmentTradesHeader />
      <div className={`main-body flex flex-col md:flex-row gap-4 ${!hasPermission ? 'show-locks' : ''}`}>
        <div className="w-full lg:w-9/12">
          <GovSearchBarV2 securities={securitiesList} members={props?.members ?? []} />
          <div className="w-full gap-4 mt-4">
            <div className="tabs-row flex items-center gap-4 wrap mt-4">
              {tabsData.map((tab, index) => (
                <button
                  className={`securities-tab ${activeTradeTab === index ? 'active' : ''}`}
                  key={index}
                  onClick={() => handleTradeTabClick(index)}
                >
                  <div className="flex items-center w-full justify-center">
                    <div className={`tab-title ${activeTradeTab === index ? 'active' : ''}`}>{tab.title}</div>
                  </div>
                </button>
              ))}
            </div>
            <div className="tab-content">
              <div className="table-wrapper">
                <div className="flex flex-col w-full justify-between items-start">
                  <h2 className="table-title">{tabsData[activeTradeTab].title}</h2>
                  <p>{tabsData[activeTradeTab].description}</p>
                </div>
                <GovernmentTable
                  columnsDef={tableColumns}
                  height={500}
                  hiddenColumns={tabsData[activeTradeTab].hideColumns}
                  hideMostRows={true}
                  openLoginModal={() => setShowLoginModal(true)}
                  rowData={tableRows}
                />
              </div>
              {activeTradeTab === 0 && (
                <button
                  className="show-more"
                  disabled={(!hasPermission || recentTradesLimit >= tableData.recentTrades?.length) ?? 0}
                  onClick={showMoreRecent}
                >
                  {!hasPermission && <AiOutlineLock size={16} />}
                  {recentTradesLimit >= tableData.recentTrades?.length ? 'Showing All' : 'Show More'}
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="w-full lg:w-3/12 flex flex-col gap-4">
          {/* Top 5 Trades */}
          <div className="card">
            <div className="card-header">Most Active</div>
            <div className="card-body">
              {mostActive.slice(0, activeLimit).map((trade, index) => (
                <TradeRow key={index} {...trade} />
              ))}
            </div>
            <button
              className="show-more"
              disabled={!hasPermission || activeLimit >= mostActive.length}
              onClick={() => setActiveLimit(prev => prev + 5)}
            >
              {!hasPermission && <AiOutlineLock size={16} />}
              {activeLimit <= mostActive.length ? 'Show More' : 'Showing all'}
            </button>
          </div>
          {/* Top Traders */}
          <div className="card">
            <div className="card-header">Top {toTitleCase(rightActiveTab)}</div>
            <div className="tab-group ">
              <button
                className={`tab ${rightActiveTab === 'gainers' ? 'active' : ''}`}
                onClick={() => handleRightTabClick('gainers')}
              >
                Bullish
              </button>
              <button
                className={`tab ${rightActiveTab === 'losers' ? 'active' : ''}`}
                onClick={() => handleRightTabClick('losers')}
              >
                Bearish
              </button>
            </div>
            <div className="card-body">
              {rightActiveTab === 'gainers' ? (
                <>
                  {topGainers.slice(0, tradersLimit).map((trade, index) => (
                    <TraderRow key={index} {...trade} type={'gainer'} />
                  ))}
                </>
              ) : (
                <>
                  {topLosers.slice(0, tradersLimit).map((trade, index) => (
                    <TraderRow key={index} {...trade} type={'loser'} />
                  ))}
                </>
              )}
            </div>
            <button
              className="show-more"
              disabled={!hasPermission || !moreToShow()}
              onClick={() => setTradersLimit(prev => prev + 5)}
            >
              {!hasPermission && <AiOutlineLock size={16} />}
              {moreToShow() ? 'Show More' : 'Showing all'}
            </button>
          </div>
        </div>
      </div>
      {showLoginModal && (
        <AuthContainer
          authMode="register"
          contentType="gov-trades"
          iterationStyle="edge-hard"
          placement="government-trades"
          preventRedirect={true}
        />
      )}
    </GovernmentTradesWrapper>
  );
};

export async function getServerSideProps() {
  try {
    const session = getGlobalSession();
    const securities = await session.getManager(GovTradesManager).getSecurities();
    const members = await session.getManager(GovTradesManager).getMembers();

    let recentTrades = securities.ok?.recent_trades?.map(trade => {
      return {
        ...trade,
        company_name: trade.security.name,
        ticker: trade.security.ticker,
        trade_amount: trade.amount,
      };
    });
    recentTrades = recentTrades ? await injectLogos(recentTrades) : [];

    let mostTraded = securities.ok?.most_traded?.map(trade => {
      trade.ticker = trade.symbol;
      return trade;
    });
    mostTraded = mostTraded ? await injectLogos(mostTraded) : [];

    let mostTraders = securities.ok?.most_traders?.map(trade => {
      trade.ticker = trade.symbol;
      return trade;
    });
    mostTraders = mostTraders ? await injectLogos(mostTraders) : [];

    let largestTrades = securities.ok?.largest_trades?.map(trade => {
      return {
        ...trade,
        company_name: trade.security.name,
        ticker: trade.security.ticker,
        trade_amount: trade.amount,
      };
    });
    largestTrades = largestTrades ? await injectLogos(largestTrades) : [];

    let mostActive = securities.ok?.most_active?.map(trade => {
      trade.ticker = trade.symbol;
      return trade;
    });
    mostActive = mostActive ? await injectLogos(mostActive) : [];

    const securitiesList = [...mostActive].sort((a, b) => {
      return a.symbol.localeCompare(b.symbol);
    });

    const dateUpdated = securities?.ok?.updated ?? recentTrades?.[0]?.updated ?? null;
    metaProps.dateUpdated = dateUpdated ? new Date(dateUpdated * 1000).toISOString() : null;

    return {
      props: {
        largestTrades,
        members: members.ok?.congressional_members ?? [],
        metaProps,
        mostActive,
        mostTraded,
        mostTraders,
        recentTrades,
        securitiesList,
        topGainers: securities.ok?.top_traders?.gainers ?? [],
        topLosers: securities.ok?.top_traders?.losers ?? [],
      },
    };
  } catch (err) {
    console.log(err);
    return {
      props: {
        largestTrades: [],
        metaProps,
        mostActive: [],
        mostTraded: [],
        mostTraders: [],
        recentTrades: [],
        securitiesList: [],
        topGainers: [],
        topLosers: [],
      },
    };
  }
}

export default GovMembersPage;

const GovernmentTradesWrapper = styled.div`
  font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
  background: #e1ebfa;

  .tabs-row {
    overflow-x: auto;
  }

  .securities-tab {
    cursor: pointer;
    max-width: 240px;
    width: 100%;
    background: linear-gradient(180deg, rgba(225, 235, 250, 0) 0%, rgba(225, 235, 250, 0.4) 100%);
    display: flex;
    justify-content: center;
    height: 40px;
    padding: 12px 8px;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;

    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #ceddf2;

    &.active {
      background: #ffffff;
      border-bottom: none;
    }

    .tab-title {
      font-size: 16px;
      white-space: nowrap;
      font-weight: 700;
      color: #5b7292;
      line-height: normal;
      text-transform: uppercase;

      &.active {
        color: #3f83f8;
      }
    }

    .tab__value {
      font-size: 12px;
      color: #828282;
      font-weight: 700;
      line-height: normal;
    }
  }

  .tab-content {
    background: #ffffff;
    border: 1px solid #ceddf2;
    padding: 16px;

    margin-top: -1px;

    .table-wrapper {
      padding: 16px 32px 0px;
      background: #f2f8ff;
      border-radius: 12px;
      border: 1px solid #e1ebfa;
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;

      .benzinga-core-table-container table {
        margin-bottom: 0px;
      }

      @media (max-width: 800px) {
        padding: 8px 16px 0px;
      }
    }

    .show-more {
      color: #3f83f8;
      text-transform: uppercase;
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      padding: 8px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #ffffff;
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
      border: 1px solid #e1ebfa;
      margin-top: -1px;
    }

    @media (max-width: 800px) {
      padding: 0px;

      .table-wrapper {
        background: white;
        border: none;

        .benzinga-core-table-container table {
          border: 1px solid #e1ebfa;
          border-radius: 8px;
        }
      }
    }
  }

  .card {
    padding-top: 16px;
    border-radius: 12px;
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .card-header {
      text-align: center;
      font-size: 18px;
      font-weight: 700;
      text-transform: uppercase;
      color: #192940;
    }

    .tab-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      padding: 0 8px;

      .tab {
        cursor: pointer;
        display: flex;
        height: 28px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 1 0 0;
        border-radius: 4px;
        color: #5b7292;
        font-size: 14px;
        text-transform: uppercase;
        font-weight: 700;
        border: 1px solid #ecf3ff;

        &.active {
          color: #3f83f8;
          background: #ecf3ff;
        }
      }
    }

    .card-body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 0 8px;
      max-height: 280px;
      overflow-y: scroll;
    }

    .show-more {
      color: #3f83f8;
      text-transform: uppercase;
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      padding: 8px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f2f8ff;
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
    }
  }

  .main-body {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;
    @media (max-width: 800px) {
      padding: 1rem;
    }

    &.show-locks {
      .show-more {
        cursor: not-allowed;
      }
    }

    .table-title {
      color: #192940;
      /* 24/Bold */
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px;
      padding-bottom: 4px;
    }

    .member-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      flex: 1 0 0;
      background: #f2f8ff;
      border-radius: 12px;

      .picture {
        width: 100%;
        height: 168px;
        border-top-right-radius: 12px;
        border-top-left-radius: 12px;
        object-fit: cover;
      }

      .content {
        display: flex;
        height: 148px;
        padding: 0px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        align-self: stretch;

        .name {
          color: #192940;
          font-size: 18px;
          font-weight: 700;
          line-height: 28px;
        }

        .description {
          color: #5b7292;
          font-size: 14px;
          font-weight: 500;
        }

        .stat-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          width: 100%;

          .label {
            color: #5b7292;
          }

          .value {
            color: #192940;
            font-weight: 700;
          }
        }
      }
    }
  }
`;
