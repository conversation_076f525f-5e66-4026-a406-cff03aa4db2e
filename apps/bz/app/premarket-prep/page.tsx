import React from 'react';
import { Layout } from '@benzinga/core-ui';

import { LayoutMain, PremarketPrepPageProps } from './components/LayoutMain';
import { getPremarketProps } from './data';
import PageLayout from '../_components/PageLayout';
import { BENZINGA_LOGO_URL } from '@benzinga/seo';
import { Metadata } from 'next';
import { PageMeta } from './meta';

const title = 'Premarket Movers & News';
const canonical = 'https://www.benzinga.com/premarket-prep';
const description =
  'Premarket trading coverage for US stocks including news, movers, losers and gainers, upcoming earnings, analyst ratings, economic calendars and futures.';

export const metadata: Metadata = {
  alternates: {
    canonical,
  },
  authors: [{ name: '<PERSON><PERSON>' }],
  description,
  openGraph: {
    description,
    images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
    title,
    type: 'website',
    url: canonical,
  },
  title,
};

export default async function Page() {
  const props = (await getPremarketProps()) as PremarketPrepPageProps;
  return (
    <PageLayout pageProps={props}>
      <PageMeta />
      <Layout layoutMain={<LayoutMain {...props} />} width="full" />
    </PageLayout>
  );
}
