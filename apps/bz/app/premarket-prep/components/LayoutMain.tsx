'use client';

import React from 'react';
import LiteYouTubeEmbed from 'react-lite-youtube-embed';
import 'react-lite-youtube-embed/dist/LiteYouTubeEmbed.css';

import { faYoutube } from '@fortawesome/free-brands-svg-icons';

import styled from '@benzinga/themetron';
import { Button, Icon } from '@benzinga/core-ui';
import { MoneyPageTemplate } from '@benzinga/money';
import { MetaProps } from '@benzinga/seo';
import { WordpressPost } from '@benzinga/content-manager';
import { NextPage } from 'next';

export interface PremarketPrepPageProps {
  metaProps?: MetaProps;
  pageTargeting?: Record<string, string | string[]>;
  post: WordpressPost | null;
  // livestreamData: {
  //   data: any;
  //   message?: string;
  // };
  todayStream: { id: string; title: string } | null;
  latestStream: { id: string; title: string } | null;
  //upcomingStream: Item | null;
}

export const LayoutMain: NextPage<PremarketPrepPageProps> = ({ latestStream, post, todayStream }) => {
  const tabs = [
    {
      key: '/premarket',
      name: 'Overview',
    },
    {
      key: '/premarket-prep',
      name: 'PreMarket Playbook',
    },
  ];

  const videoId = todayStream?.id || latestStream?.id;
  const videoTitle = todayStream?.title ?? latestStream?.title ?? '';

  return (
    <PremarketPageContainer className="premarket-prep-page">
      {post && (
        <MoneyPageTemplate
          layoutAboveArticle={
            <div className="flex flex-col items-start w-full text-gray-900 gap-8 mb-8">
              <div className="w-full bg-white">
                <div className="hero-video-section flex w-full gap-6 mt-4">
                  <div className="w-full">
                    {videoId ? (
                      <LiteYouTubeEmbed id={videoId} title={videoTitle} />
                    ) : (
                      <div className="youtube-iframe-wrapper w-full">
                        <iframe
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          className="ratio-content"
                          data-gtm-yt-inspected-11="true"
                          frameBorder="0"
                          id="iframe-livestream"
                          loading="lazy"
                          scrolling="no"
                          src="https://www.youtube.com/embed/live_stream?channel=UCqQs28K2zj2dOsc5NfXUKEg&enablejsapi=1"
                          title="YouTube"
                        ></iframe>
                      </div>
                    )}
                  </div>
                  <div className="w-full md:max-w-[620px]">
                    <h2 className="text-gray-900 text-3xl mb-2">PreMarket Playbook • M-F 8-9am ET</h2>
                    <p className="text-gray-500 mb-2">
                      <span className="text-black font-semibold">
                        🚀 Welcome to Benzinga&apos;s PreMarket Playbook! 🚀
                      </span>
                      <br />
                      <span className="text-black font-semibold">
                        Start Your Trading Day Right – Every Weekday at 8AM ET
                      </span>
                      <br />
                      <br />
                      Join us as we break down key pre-market trends, top indicators, and actionable trading insights to
                      help you stay ahead of the markets. Whether you&apos;re a day trader, diving into options or
                      futures, chasing penny stocks, or focused on blue chips, we&apos;ve got real-time analysis and
                      live discussions tailored to you!
                      <br />
                      <br />
                      <span className="text-black font-semibold">📊 What to Expect:</span>
                      <br />
                      • Pre-Market Breakdown 🔍
                      <br />
                      • Actionable Trading Ideas 💡
                      <br />• Expert Analysis & Insights 📈
                    </p>
                    <p className="font-bold mb-2">Come join us and talk about stocks on your radar.</p>
                    <div>
                      <div className="flex flex-col md:flex-row gap-2">
                        <Button
                          aria-label="This opens the Benzinga YouTube channel in a new tab"
                          as="a"
                          className="subscribe-button md:mt-2"
                          href="https://www.youtube.com/user/BenzingaTV?sub_confirmation=1"
                          rel="noreferrer"
                          target="_blank"
                        >
                          <Icon className="mr-2 text-2xl" icon={faYoutube} /> Subscribe
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          }
          layoutTabs={tabs}
          layoutTabsOptions={{ prefetchAllTabs: true }}
          post={post}
          tabsTitle="Premarket Movers & News"
          width="wide"
        />
      )}
    </PremarketPageContainer>
  );
};

const PremarketPageContainer = styled.div`
  &.premarket-prep-page {
    .hero-video-section {
      flex-direction: column;

      @media (min-width: 1020px) {
        flex-direction: row;
      }
    }

    .youtube-iframe-wrapper {
      position: relative;
      overflow: hidden;
      width: 100%;
      padding-top: 56.25%;

      > iframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
      }
    }
    .subscribe-button {
      background: rgba(255, 81, 81, 0.15);
      border: none;
      box-shadow: none;
      color: #e62117;
      font-size: 18px;
      padding: 0.5rem 2rem;
      height: 46px;
      border: 1px solid #e62117;

      &:hover {
        background: rgba(255, 95, 95, 0.36);
      }
    }
  }
`;
