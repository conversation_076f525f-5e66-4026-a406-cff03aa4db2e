import React from 'react';
import { cookies, headers } from 'next/headers';
import { ErrorBoundary } from '@benzinga/core-ui';
import localFont from 'next/font/local';

import { fetchPageProps } from '../utils/getServerProps';
import WrapProviders from './_components/root/WrapProviders';
import { getGlobalSession } from '../pages/api/session';
import { AuthenticationManager } from '@benzinga/session';
import RootHeadTag from './_components/root/RootHeadTag';

import './styles/theme.scss';
import StyledComponentsRegistry from './_components/registry';

async function getPageProps() {
  const headersList = await headers();
  const cookieStore = await cookies();

  const query = {}; // replace with actual query if needed
  const req = { cookies: cookieStore.toString(), headers: { host: headersList.get('host') } };
  const result = await fetchPageProps(query, req);
  return result;
}

const manrope = localFont({
  display: 'optional',
  preload: true,
  src: [
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-300.woff2',
      style: 'normal',
      weight: '300',
    },
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-regular.woff2',
      style: 'normal',
      weight: '400',
    },
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-500.woff2',
      style: 'normal',
      weight: '500',
    },
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-600.woff2',
      style: 'normal',
      weight: '600',
    },
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-700.woff2',
      style: 'normal',
      weight: '700',
    },
    {
      path: '../public/next-assets/fonts/manrope-v15-latin/manrope-v15-latin-800.woff2',
      style: 'normal',
      weight: '800',
    },
  ],
});

import { Bebas_Neue, Inter } from 'next/font/google';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-bebas-neue',
});

const inter = Inter({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  variable: '--font-inter',
});

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const { props: pageProps } = await getPageProps();
  const session = getGlobalSession();
  const authManager = session.getManager(AuthenticationManager);
  const languageFlag = pageProps?.metaProps?.hrefLanguage || 'en';
  const isLoggedIn = authManager.isLoggedIn();
  return (
    <html className={`${manrope.className} ${inter.className} ${bebasNeue.className}`} lang={languageFlag}>
      <RootHeadTag language={pageProps.metaProps?.language} />
      <body>
        <div>
          <ErrorBoundary disableFallback={true} name="global">
            <StyledComponentsRegistry>
              <WrapProviders isLoggedIn={isLoggedIn} pageProps={pageProps}>
                {children}
              </WrapProviders>
            </StyledComponentsRegistry>
          </ErrorBoundary>
        </div>
      </body>
    </html>
  );
}
