$colorPalette: (
  black: '#000000',

  blue50: '#ebf5ff',
  blue100: '#e1effe',
  blue200: '#c3ddfd',
  blue300: '#a4cafe',
  blue400: '#76a9fa',
  blue500: '#3f83f8',
  blue600: '#1c64f2',
  blue700: '#1a56db',
  blue800: '#1e429f',
  blue900: '#233876',

  current: 'currentColor',

  gray50: '#F2F8FF',
  gray100: '#E1EBFA',
  gray200: '#CEDDF2',
  gray300: '#B8CBE6',
  gray400: '#99AECC',
  gray500: '#5B7292',
  gray600: '#395173',
  gray700: '#192940',
  gray800: '#17253A',
  gray900: '#142133',
  green100: '#def7ec',
  green200: '#bcf0da',

  green50: '#f3faf7',
  green300: '#84e1bc',
  green400: '#31c48d',
  green500: '#0e9f6e',
  green600: '#057a55',
  green700: '#046c4e',
  green800: '#03543f',
  green900: '#014737',

  indigo100: '#e5edff',
  indigo200: '#cddbfe',
  indigo300: '#b4c6fc',
  indigo400: '#8da2fb',
  indigo50: '#f0f5ff',
  indigo500: '#6875f5',
  indigo600: '#5850ec',
  indigo700: '#5145cd',
  indigo800: '#42389d',
  indigo900: '#362f78',

  inherit: 'inherit',
  initial: 'initial',

  neutral50: '#F9FCFF',
  neutral100: '#F2F8FF',
  neutral200: '#E1EBFA',
  neutral300: '#CEDDF2',
  neutral400: '#B8CBE6',
  neutral500: '#99AECC',
  neutral600: '#5B7292',
  neutral700: '#395173',
  neutral800: '#192940',
  neutral900: '#17253A',

  orange50: '#fff8f1',
  orange100: '#feecdc',
  orange200: '#fcd9bd',
  orange300: '#fdba8c',
  orange400: '#ff8a4c',
  orange500: '#ff5a1f',
  orange600: '#d03801',
  orange700: '#b43403',
  orange800: '#8a2c0d',
  orange900: '#73230d',

  pink50: '#fdf2f8',
  pink100: '#fce8f3',
  pink200: '#fad1e8',
  pink300: '#f8b4d9',
  pink400: '#f17eb8',
  pink500: '#e74694',
  pink600: '#d61f69',
  pink700: '#bf125d',
  pink800: '#99154b',
  pink900: '#751a3d',

  purple50: '#f6f5ff',
  purple100: '#edebfe',
  purple200: '#dcd7fe',
  purple300: '#cabffd',
  purple400: '#ac94fa',
  purple500: '#9061f9',
  purple600: '#7e3af2',
  purple700: '#6c2bd9',
  purple800: '#5521b5',
  purple900: '#4a1d96',

  red50: '#fdf2f2',
  red100: '#fde8e8',
  red200: '#fbd5d5',
  red300: '#f8b4b4',
  red400: '#f98080',
  red500: '#f05252',
  red600: '#e02424',
  red700: '#c81e1e',
  red800: '#9b1c1c',
  red900: '#771d1d',

  teal50: '#edfafa',
  teal100: '#d5f5f6',
  teal200: '#afecef',
  teal300: '#7edce2',
  teal400: '#16bdca',
  teal500: '#0694a2',
  teal600: '#047481',
  teal700: '#036672',
  teal800: '#05505c',
  teal900: '#014451',

  transparent: 'transparent',

  yellow50: '#fdfdea',
  yellow100: '#fdf6b2',
  yellow200: '#fce96a',
  yellow300: '#faca15',
  yellow400: '#e3a008',
  yellow500: '#c27803',
  yellow600: '#9f580a',
  yellow700: '#8e4b10',
  yellow800: '#723b13',
  yellow900: '#633112',

  white: '#ffffff',

  bzWhite: '#FFFFFF',
  bzBlack: '#212731',
  bzBlackNormal: '#d9d9d9',
  bzBlackDarkest: '#000000',
  bzGreyDarker: '#28313C',
  bzGreyDark: '#373f49',
  bzGrey: '#818b97',
  bzGreyLight: '#C5CEDA',
  bzGreyLighter: '#D4DBE7',
  bzGreyLightest: '#EFF1F6',
  bzGreyNormal: '#4d5866',
  bzGreen: '#30BFA3',
  bzGreenLight: '#18be39',
  bzRed: '#E5594E',
  bzYellow: '#FFD373',
  bzBlue: '#008FD9',
  bzOrange: '#E3A542',
  bzOrangeLight: '#ff9c29',
  bzOrangeNormal: '#373f49',

  bzBlueDark: '#0076CD',
  bzOrangeDark: '#D78E31',
  bzOrangeDarker: '#a56300',
);

$fontSize : (
  '2xl': '24px',
  '3xl': '30px',
  '4xl': '36px',
  '5xl': '48px',
  '6xl': '64px',
  base: '14px',
  inherit: 'inherit',
  initial: 'initial',
  lg: '16px',
  md: '14px',
  sm: '12px',

  xl: '18px',
  xs: '10px',
);

$fontWeight : (
  bold: '700',
  extrabold: '800',
  inherit: 'inherit',
  initial: 'initial',
  light: '300',
  medium: '500',

  normal: '400',
  semibold: '600',
);

$borderRadius : (
  '2xl': '16px',
  '3xl': '24px',
  default: '4px',
  full: '9999px',
  inherit: 'inherit',
  initial: 'initial',
  lg: '8px',
  md: '6px',
  none: 'none',

  sm: '2px',
  xl: '12px',
);

html {
  @each $name, $value in $colorPalette {
    --color-#{'' + $name}: #{$value};
  }

  @each $name, $value in $fontSize {
    --font-size-#{'' + $name}: #{$value};
  }

  @each $name, $value in $fontWeight {
    --font-weight-#{'' + $name}: #{$value};
  }

  @each $name, $value in $borderRadius {
    --border-radius-#{'' + $name}: #{$value};
  }
}
