// NavigationHeader
.header-container {
  .searchbar-wrapper {
    box-shadow: 0px 0px 2px 0 rgb(0 0 0 / 15%);
    padding: 4px 12px;
    padding-right: 10px;
    flex-grow: 1;
    max-width: 290px;
    position: relative;
    max-width: none;

    @media screen and (max-width: 800px) {
      max-width: none;
    }

    a {
      text-decoration: none;
    }

    &.searchbar-box,
    &.searchbar-inline {
      padding: 0;
      // border: solid 1px ${props => props.theme.colors.border};
      box-shadow: none;
      .search-results-wrapper {
        padding: 0;
      }
      .search-result-wrapper {
        padding: 0;
      }
      .search-icon {
        height: 36px;
      }
      input {
        ::placeholder {
          color: var(--color-gray400);
        }
      }
    }

    &.searchbar-inline {
      box-shadow: none;
      .search-dropdown {
        position: relative;
        box-shadow: none;
        border: none;
      }
    }

    input {
      &:focus {
        box-shadow: none;
      }
      ::placeholder {
        // color: ${({ theme }) => theme.colorPalette.gray600};
        color: var(--color-gray600);
      }
    }

    .search-input-wrapper {
      display: flex;
      position: relative;
      width: 100%;
      // padding-left: ${({ showSearchIcon }) => (showSearchIcon ? 'unset' : '10px')};
      /* border-radius: ${({ showSearchButton, theme }) => (showSearchButton ? 'unset' : theme.borderRadius.default)}; */

      .search-icon {
        align-items: center;
        color: #378aff;
        display: flex;
        // height: 44px;
        justify-content: center;
        // width: 44px;
      }

      .cancel-button {
        font-size: 1.2rem;
        padding: 0 10px;
        color: #378aff;
      }

      .search-button {
        border-radius: 0;
        // width: 50px;
      }

      input {
        background-color: transparent;
        border-width: 0;
        outline: none;
        width: 100%;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }

  // .mobile-header-wrapper {
  //   display: block;
  //   @media screen and (min-width: 800px) {
  //     display: none;
  //   }
  // }

  .top-banner-fallback {
    height: 80px;
    width: 100%;
    padding: 1rem 0rem;
    position: relative;
    background-color: #0b131d;
  }
}

.desktop-menu-wrapper {
  display: block;
  z-index: 999;

  @media (max-width: 800px) {
    display: none;
  }

  .logo-wrapper {
    display: flex;
    align-items: center;
    margin: 0 4px;
    padding-right: 0px;
    min-width: 144px;
    width: 192px;
    a {
      display: flex;
    }
  }

  .breaking-news-banner {
    position: absolute;
    top: -50px;
    transition: all ease-in 0.5s;
    z-index: -1;
  }

  .breaking-news-wrapper {
    position: relative;

    &--visible {
      .breaking-news-banner {
        // position: ${({ $isFixed }) => ($isFixed ? 'absolute' : 'unset')};
        top: 0;
      }
    }
  }

  .more-nav-tabs-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    margin-right: 5px;
    cursor: pointer;

    &:hover {
      > div {
        display: flex;
      }
    }

    @media screen and (min-width: 1315px) {
      display: none !important;
    }
  }

  .more-menu-items-dropdown-container {
    position: absolute;
    //right: 0;
    width: max-content;
    top: 48px;
    z-index: 2000;
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: none; // will get override by client side on dynamic var isVisible

    &:hover {
      display: flex;
    }

    .subnav {
      right: 0;
    }

    .menu-wrapper {
      a {
        width: 100%;
        justify-content: left;
      }
    }

    @media screen and (min-width: 910px) {
      min-width: initial;
    }

    > div {
      @media screen and (min-width: 950px) {
        &:nth-of-type(1) {
          display: none;
        }
      }

      @media screen and (min-width: 1000px) {
        &:nth-of-type(2) {
          display: none;
        }
      }

      @media screen and (min-width: 1100px) {
        &:nth-of-type(3) {
          display: none;
        }
      }

      @media screen and (min-width: 1130px) {
        &:nth-of-type(4) {
          display: none;
        }
      }

      @media screen and (min-width: 1190px) {
        &:nth-of-type(5) {
          display: none;
        }
      }

      @media screen and (min-width: 1250px) {
        &:nth-of-type(6) {
          display: none;
        }
      }

      @media screen and (min-width: 1315px) {
        &:nth-of-type(7) {
          display: none;
        }
      }
    }
  }
}

.mobile-header-wrapper {
  // position: fixed;
  // z-index: 100;
  // left: 0;
  // right: 0;
  // width: 100%;
  // transition: all linear 0.12s;

  .mobile-header,
  .mobile-menu-wrapper,
  .mobile-quote-bar {
    background-color: #08244d;
  }

  .mobile-logo-wrapper {
    // display: flex;
    // align-items: center;
    margin: 0;
    padding: 0;
    // width: 205px;
    height: 18.2px;
    width: 134.35px;
    margin-right: auto;
  }

  .mobile-header {
    border-bottom: 1px solid #36537e;
  }

  .menu-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    color: var(--color-gray200, #99AECC);
    // color: ${({ theme }) => theme.colorPalette.gray200};
    // font-size: ${({ theme }) => theme.fontSize.xl};
  }

  &.fullscreen {
    .account-menu {
      pointer-events: none;
    }
  }

  .search-wrapper {
    background: linear-gradient(180deg, #082132 0%, #1d446a 100%);
    width: 100%;
    z-index: 30;

    &.fixed {
      position: fixed;
      // top: ${props => (props.$offset ? `${props.$offset}px` : '48px')};
    }

    &.fullscreen {
      position: fixed;
      background: #242424e0;
      z-index: 999;
      height: 100vh;

      .searchbar-wrapper {
        /* background-color: #ffffff; */
        padding: 0;

        .search-input-container {
          border-bottom: 1px solid #a4a4a4;
          padding: 8px 12px;
          background-color: #1d446a;
        }
        .search-input-wrapper {
          background-color: white;
          border-radius: 4px;
        }
      }

      .search-dropdown {
        border-radius: unset;
        border: none;
        box-shadow: none;
        position: unset;
      }

      .search-result-wrapper {
        border-radius: unset;
      }
    }

    .searchbar-wrapper {
      padding: 8px 12px;
      background-color: transparent;
      font-weight: bold;
      line-height: 15px;
      .search-icon {
        width: 32px;
      }
      input {
        // font-size: ${({ theme }) => theme.fontSize.base};
        height: 45px;
      }
      .searchbar-container {
        background-color: white;
        border-radius: 2px;
      }
      @media screen and (max-width: 800px) {
        input {
          font-size: 16px;
        }
      }
    }
  }

  .mobile-menu-wrapper {
    z-index: 999999;
    .premium-button {
      color: white;
      line-height: 40px;
      padding: 4px 10px;
      position: fixed;
      height: min-content;
      bottom: 0px;
      z-index: 10000;
      background-color: #062b4c;

      div {
        // background-color: ${({ theme }) => theme.colorPalette.orange500};
        border-radius: 10px;
        padding: 0px 10px;
        width: 100%;
        border-radius: 5px;
        margin: 2px 0px;
        text-align: center;
      }

      svg {
        font-size: 20px;
        padding-right: 5px;
      }
    }
  }

  .bz-pro-logo {
    height: 60px;
    background: #062b4c;
    margin-bottom: 60px;
    a {
      border-top: solid 1px #666;
      padding: 1rem;
      width: 100%;
    }
  }
}

// White Bar
.main-menu-bar {
  background-color: white;
  // border-bottom: ${props => (props.hideQuoteBar && !props.isIndiaApp ? '1px' : '0px')} solid
    // ${({ theme }) => theme.colorPalette.gray300};
  width: 100%;
  padding: 0;

  .main-menu-wrapper {
    height: 48px;
    // max-width: ${({ isIndiaApp }) => (isIndiaApp ? '1250px' : '1400px')};
    max-width: '1400px';
    margin: 0 auto;
  }

  .bar-content-layout {
    display: flex;
    position: relative;
    height: 100%;
    justify-content: center;
  }

  .primary-menu-wrapper {
    flex-grow: 1;
    margin-left: 20px;
  }

  .secondary-menu-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    min-width: 244px;
  }
}

.menu-item-container {
  display: flex;
  overflow-x: scroll;
  scrollbar-width: none;
  ::-webkit-scrollbar {
    display: none;
  }
  // justify-content: ${({ isIndiaApp }) => (isIndiaApp ? 'flex-end' : 'flex-start')};

  li {
    flex-shrink: 0;

    &:nth-of-type(3),
    &:nth-of-type(4),
    &:nth-of-type(5),
    &:nth-of-type(6),
    &:nth-of-type(7),
    &:nth-of-type(8),
    &:nth-of-type(9),
    &:nth-of-type(10),
    &:nth-of-type(11),
    &:nth-of-type(12) {
      display: none;
    }

    @media screen and (min-width: 850px) {
      &:nth-of-type(3) {
        display: initial;
      }
    }

    @media screen and (min-width: 900px) {
      &:nth-of-type(4) {
        display: initial;
      }
    }

    @media screen and (min-width: 950px) {
      &:nth-of-type(5) {
        display: initial;
      }
    }

    @media screen and (min-width: 1000px) {
      &:nth-of-type(6) {
        display: initial;
      }
    }

    @media screen and (min-width: 1100px) {
      &:nth-of-type(7) {
        display: initial;
      }
    }

    @media screen and (min-width: 1130px) {
      &:nth-of-type(8) {
        display: initial;
      }
    }

    @media screen and (min-width: 1190px) {
      &:nth-of-type(9) {
        display: initial;
      }
    }

    @media screen and (min-width: 1250px) {
      &:nth-of-type(10) {
        display: initial;
      }
    }

    @media screen and (min-width: 1315px) {
      &:nth-of-type(11) {
        display: initial;
      }
    }

    @media screen and (min-width: 1360px) {
      &:nth-of-type(12) {
        display: initial;
      }
    }
  }
}

.menu-wrapper {
  &.secondary {
    position: relative;
    .subnav {
      position: absolute;
      right: 0;
      top: 48px;
    }
  }
  .subnav {
    display: none;
    position: absolute;
    z-index: 2000;
    .third-level-menu {
      display: none;
      position: absolute;
      left: 100%;
      background: #ffffff;
      // border: 1px solid ${({ theme }) => theme.colorPalette.gray200};
      // border-bottom-left-radius: ${({ theme }) => theme.borderRadius.sm};
      // border-bottom-right-radius: ${({ theme }) => theme.borderRadius.sm};
      height: 100%;
      z-index: 999999;
      overflow: auto;
      top: 0;
      height: auto;
      min-width: 160px;
    }
    .more-nav-menu {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
  }
  &:hover {
    .subnav {
      display: block;
    }
  }
  .menu-items {
    padding: 4px;
    .dropdown-menu-item {
      position: relative;
      &:hover {
        .third-level-menu {
          display: block;
        }
      }
    }
  }
  @media screen and (max-width: 800px) {
    .subnav {
      display: none !important;
    }
  }

  .menu-item-link {
    line-height: 48px;
    padding: 0 10px;
    font-family: Manrope, Manrope-fallback, sans-serif;
    font-weight: 700;
    font-size: 14px;
    color: black;
    height: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #e1effe;
      color: unset;
    }

    &.trading-school {
      color: #298ccd !important;
    }

    @media screen and (min-width: 1292px) {
      &.trading-school {
        svg {
          display: none;
        }
      }
    }

    @media screen and (max-width: 800px) {
      color: white;
      width: 100%;
      text-align: left;
      display: inline-flex;
      justify-content: start;
      &:hover {
        background-color: inherit;
      }
    }
  }

  .primary-toolbar-button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px; // fontSize="sm"
    font-weight: 700; // fontWeight="bold"
    white-space: nowrap;

    &.others-btn {
      height: 48px;
      color: #192940;
      text-transform: capitalize;
      padding: 0 8px;
      &:hover {
        color: #1a56db;
      }
    }

    &.research-btn {
      height: 34px;
      color: white;
      text-transform: uppercase;
      border-radius: 5px;
      letter-spacing: 1px;
      padding: 0 14px;
      &:hover {
        opacity: 100;
        color: white;
      }
    }
    &.invest-in-art-btn {
      height: 34px;
      color: #000000;
      border-radius: 5px;
      letter-spacing: 1px;
      padding: 0 10px;
      margin-right: 8px;
      &:hover {
        background-color: #a4cafe;
      }
    }
  }
}

.menu-item-wrapper {
  line-height: 2em;
  a {
    color: black;
    &:hover {
      color: var(--color-blue-700, #1a56db);
    }
  }

  .dropdown-menu-item {
    line-height: 2em;

    a {
      display: block;
      padding: 6px 12px;
      line-height: 1.4rem;
    }

    &:hover {
      color: #1a79ff;
    }

    &.highlight {
      padding: 0 5px;
      margin-bottom: 5px;

      a {
        color: #ffffff;
        background-color: 'unset';
        font-weight: 'unset';
        text-align: center;
        // only cannabis using highlight
        background-color: #63bc47;
      }
    }

  }
}

.sub-group-wrapper {
  background-color: white;
  border-width: 1px;
  border-top-width: 1px;
  border-color: #CEDDF2;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  padding: 8px 0;
  scrollbar-width: thin;
  max-height: calc(90vh - 100px);
  // overflow: auto;
  ::-webkit-scrollbar {
    width: 6px !important;
  }
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  ::-webkit-scrollbar-thumb {
    background: #888;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.sub-nav-wrapper {
  text-align: left;

  &.has-groups {
    width: 480px;
    &.money-app {
      max-width: 480px;
      width: auto;
      min-width: 180px;
    }
  }
}

.group-list {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(2, minmax(0px, 1fr));
}

.group-list-item-label {
  border-bottom: var(--color-gray-300, #B8CBE6);
  color: var(--color-gray-600, #395173);
  font-weight: bold;
  margin-top: 4px;
  margin-bottom: 4px;
  margin-left: 16px;
  margin-right: 16px;
  padding-bottom: 4px;
}

.bar-content-layout {
  height: 48px;
  max-width: 1400px;
  margin: 0 auto;
}

// TopBar
.top-bar-wrapper {
  background-color: #072232;
  height: 32px;
  .bar-layout-width {
    max-width: 1400px;
    margin: 0 auto;
  }
  .top-bar-inner-wrapper {
    display: flex;
    justify-content: space-between;
  }
  .top-bar-list {
    list-style: none;
    display: flex;
    margin: 0;
  }
  .auth-buttons {
    display: flex;
    height: 32px;
    min-width: 234px;
  }
  .auth-link-button {
    color: white; // color: ${({ theme }) => theme.colorPalette.white};
    cursor: pointer;
    line-height: 32px;
    padding: 0 12px;
    text-transform: uppercase;
    font-size: 14px; // font-size: ${({ theme }) => theme.fontSize.base};
    font-weight: 700;
  }
  .bz-pro-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    border-radius: 3px;
    height: 26px;
    min-width: 160px;
    color: white;
    font-weight: semi-bold;
    background-color: #0075cd;
    margin: 3px 3px 0px 0px;
  }
  .bz-top-bar-list-item-label {
    color: white;
    font-size: 12px;
    line-height: 30px;
    margin: 0 8px;
    white-space: nowrap;
  }
  .contributor-link {
    align-items: center;
    color: white;
    display: inline-flex;
    font-size: 12px;;
    height: 32px;
    margin: 0 12px;
    a {
      color: white;
    }
  }

  .bz-top-bar-list-item {
    border-top: 2px solid var(--color-gray500, #3f83f8);
    color: white;
    text-transform: uppercase;
    line-height: 28px;
    font-weight: 500;
    font-size: 14px;
    // font-size: ${({ theme }) => theme.fontSize.base};
    margin: 0;
    text-align: center;

    &.ProLabel {
      border-top: none;
      text-transform: none;
      min-width: 140px;
    }

    &:nth-child(2) {
      border-top-color: #f05252;
      min-width: 84px;
    }

    &:nth-child(3) {
      border-top-color: #c27803;
      min-width: 66px;
    }

    &:nth-child(4) {
      border-top-color: #046c4e;
      min-width: 83px;
    }

    &:nth-child(5) {
      border-top-color: #c81e1e;
      min-width: 93px;
    }

    &:nth-child(6) {
      border-top-color: #0e9f6e;
      min-width: 59px;
    }

    &:nth-child(7) {
      border-top-color: #427ff8;
      min-width: 86px;
    }
  }
}

.global-menu {
  background-color: #033251;
  position: relative;
  min-width: 65px;
  &:hover {
    .global-menu-dropdown {
      display: block;
    }
  }
  @media screen and (max-width: 800px) {
    background-color: transparent;
  }
  .menu-button {
    align-items: center;
    color: white;
    cursor: pointer;
    display: inline-flex;
    font-size: 12px;
    height: 32px;
    padding: 0px 12px;
    .anticon-user {
      color: #1a79ff;
      margin-right: 12px;
    }
    @media screen and (max-width: 800px) {
      padding: 0px 6px;
      .anticon-user {
        color: #a4cafe;
        font-size: 18px;
      }
      .anticon-down {
        display: none;
      }
    }
  }
  .global-menu-dropdown {
    background-color: #033251;
    display: none;
    position: absolute;
    width: 110px;
    width: max-content;
    z-index: 100;
    @media screen and (max-width: 800px) {
      right: 0px;
    }
  }
  .global-menu-item {
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 8px 12px;
    a {
      color: white;
      display: flex;
      align-items: center;
    }
    display: block;
    margin: 0;
    span {
      display: inline-block;
      vertical-align: middle;
      line-height: 1;
      margin-right: 10px;
    }
    img {
      width: 30px;
      display: inline-block;
      vertical-align: middle;
    }
  }
  .global-text {
    margin-right: 14px;
    @media screen and (max-width: 800px) {
      margin-right: 0;
    }
  }
  @media screen and (max-width: 800px) {
    width: 3rem;
    text-align: center;
  }
}

.account-menu {
  .menu-button {
    align-items: center;
    color: white;
    cursor: pointer;
    display: inline-flex;
    font-size: 12px;
    height: 32px;
    padding: 0px 12px;
    .anticon-user {
      color: #1a79ff;
      margin-right: 12px;
    }
    @media screen and (max-width: 800px) {
      .anticon-user {
        color: rgb(164, 202, 254);
        font-size: 18px;
        margin-right: 0;
      }
      .anticon-down {
        display: none;
      }
    }
  }
  .account-menu-dropdown {
    background-color: #ffffff;
    display: none;
    position: absolute;
    width: 140px;
    z-index: 100;
    @media screen and (max-width: 800px) {
      right: 0px;
    }

    &.open {
      display: block;
    }

    .checkbox-container {
      label {
        display: flex;
        font-size: 12px;;
      }
    }
  }
  .account-menu-item {
    color: black;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    padding: 8px 12px;
    a {
      color: black;
      display: inline-flex;
    }
    &.benzinga-plus a {
      // color: ${({ theme }) => theme.colorPalette.orange500};
    }
    &.benzinga-pro a {
      // color: ${({ theme }) => theme.colors.brand};
    }
    &.benzinga-edge a {
      color: #3f83f8;
    }
  }
  .divider {
    margin: 0 auto;
    width: 85%;
    // border-color: ${({ theme }) => theme.colorPalette.gray200};
  }
  .my-account-text {
    margin-right: 14px;
    @media screen and (max-width: 800px) {
      display: none;
    }
  }
}

// QuoteBar
.quote-bar-container {
  background: linear-gradient(180deg, #082132 0%, #1d446a 100%);

  .quote-bar-layout-width {
    max-width: 1400px;
    margin: 0 auto;
    height: 52px;
  }

  .quote-list-container {
    flex-grow: 1;
    overflow-x: auto;
    scrollbar-width: none;
    ::-webkit-scrollbar {
      display: none;
    }
  }

  .search-wrapper {
    width: 100%;
    max-width: 288px;
    .searchbar-wrapper {
      padding: 8px 12px;
      background-color: transparent;
      font-weight: bold;
      font-size: 16px;
      line-height: 15px;
      .search-icon {
        width: 32px;
      }
      input {
        font-size: 12px;
      }
      .searchbar-container {
        background-color: white;
        border-radius: 2px;
      }
      @media (min-width: 1300px) {
        padding-right: 0;
        .search-icon {
          width: 36px;
        }
      }
    }
    .search-icon {
      height: 36px;
    }
  }

  .quote-switcher-wrapper {
    overflow-y: auto;
    height: 52px;
    width: 70px;
    scrollbar-width: none;
    flex: none;
    ::-webkit-scrollbar {
      display: none;
    }
  }
}

.quote-bar-wrapper,
.quote-list-wrapper {
  display: flex;
  flex-grow: 1;
  height: 52px;
}

.quote-switcher-wrapper {
  overflow-y: auto;
  height: 52px;
  width: 70px;
  scrollbar-width: none;
  flex: none;
  ::-webkit-scrollbar {
    display: none;
  }
}

.quote-switcher-container {
  display: flex;
  flex-direction: column;

  .quote-switcher-button {
    max-width: 73px;
    width: 100%;
    border-left: 1px solid;
    color: var(--color-gray400);
    text-align: left;
    font-size: 12px;
    line-height: 18px;
    padding: 4px 9px;
    flex-grow: 1;
    :focus {
      outline: none;
    }
  }
}

// QuoteBox.tsx
.quote-block {
  display: block;
  width: 100%;
  max-width: 145px;
  min-width: 132px;
  padding: 8px;

  &.positive {
    background: linear-gradient(90deg, #0e9f6e00 0%, #0e9f6e0d 100%)
  }

  &.negative {
    background: linear-gradient(90deg, #f0525200 0%, #f052520d 100%)
  }

  .dark {
    color: #99AECC !important;
  }
  .light {
    color: #395173 !important;
  }

  .quotebox-quote-name {
    color: #CEDDF2;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
  }

  .quotebox-quote-data {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }

  .quotebox-quote-price {
    color: white;
    font-size: 12px;
    line-height: 16px;
  }

  .quotebox-quote-change {
    color: #99AECC;
    font-size: 12px;

    &.positive {
      color: #0e9f6e;
    }
    &.negative {
      color: #f05252;
    }

    font-weight: 700;
    display: flex;
    align-items: center;

    .quote-icon {
      margin: 0 4px;
      font-size: 12px;
    }
  }
}
