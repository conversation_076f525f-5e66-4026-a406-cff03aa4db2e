import React from 'react';
import HomePageWrapper from './_components/HomePageWrapper';
import { NoFirstRender } from '@benzinga/hooks';
import { ErrorBoundary } from '@benzinga/core-ui';
import { BlocksDataContext } from '@benzinga/blocks';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { MetaProps, PageType } from '@benzinga/seo';
import { redirect } from 'next/navigation';

import PageLayout from './_components/PageLayout';
import { getGlobalSession } from '../pages/api/session';
import { getRequestInfo } from './_utils/serverUtils';
import { setMetadata } from './_libs/setMetadata';
import { homeData } from '../pages/api/home';
import { HOME_PAGE_TABS } from '../src/components/Home/components/HomeTabs';
import HomePageTemplate from '../src/components/Home/components/HomePageTemplate';
import type { HomeProps } from '../src/components/Home/interface';
import CustomError from './_components/CustomError';

const FloatingWNSTNWidget = React.lazy(() =>
  import('@benzinga/ads').then(module => ({ default: module.FloatingWNSTNWidget })),
);

export const dynamic = 'force-dynamic';

export async function generateMetadata() {
  const props = (await homeData()) as unknown as HomeProps;

  const metaProps: MetaProps = {
    canonical: 'https://www.benzinga.com/',
    description:
      props?.post?.meta?.description ||
      'Stock Market Quotes, Business News, Financial News, Trading Ideas, and Stock Research by Professionals.',
    image: props?.post?.meta?.image ?? null,
    pageType: PageType.Front,
    title: props?.post?.meta?.title || 'Actionable Trading Ideas, Real-Time News, Financial Insight',
  };

  if (props?.featuredNews?.[0]) {
    metaProps.dateCreated = props.featuredNews?.[0]?.created ?? null;
    metaProps.dateUpdated = props.featuredNews?.[0]?.updated ?? null;
  }

  return setMetadata(metaProps);
}

export default async function Page({
  searchParams,
}: {
  searchParams?: Promise<Record<string, string | string[] | undefined>>;
}) {
  const sp = (await searchParams) ?? {};
  const previewParam = sp.p;
  const previewId = (Array.isArray(previewParam) ? previewParam[0] : previewParam) as string | undefined;
  if (previewId) {
    redirect(`/money/preview/${previewId}`);
  }

  const session = getGlobalSession();
  const { headers, cookies } = await getRequestInfo();

  const props = (await homeData()) as unknown as HomeProps;

  if (!props?.post) {
    return (
      <PageLayout pageProps={{ isSSR: true }}>
        <CustomError statusCode={503} />
      </PageLayout>
    );
  }

  // Set url and defaults
  const host = (headers?.host as string) || 'www.benzinga.com';
  props.url = `https://${host}`;
  props.activeTab = HOME_PAGE_TABS.TRENDING;

  // Load server-side block data for main blocks
  if (Array.isArray(props?.post?.blocks)) {
    props.post.blocks = await loadServerSideBlockData(session, props.post.blocks, headers, cookies);

    // Inject Top Stories feed into the Top Stories block
    const topStoriesBlock = props.post.blocks.find(
      block => block?.blockName === 'acf/news-list-group' && block?.attrs?.data?.groups?.[0]?.title === 'Top Stories',
    );
    if (topStoriesBlock) {
      topStoriesBlock.attrs.data.groups[0].topStoriesFeed = props.topStoriesFeed;
    }

    // Disperse partner content across matching groups
    const partnerLength = props?.partnerContentDisperseMap?.length ?? 0;
    if (props.partnerContentDisperseMap && partnerLength > 0) {
      for (const feed of props.partnerContentDisperseMap) {
        const data = findMatchingBlockGroup(props.post.blocks, feed.queryKey, feed.queryValues);
        if (data) {
          data.nodes?.pop();
          data.nodes?.push(feed.posts[0]);
        }
      }
    }
  }

  // Load server-side block data for sidebar
  if (Array.isArray(props?.post?.sidebar?.blocks)) {
    props.post.sidebar.blocks = await loadServerSideBlockData(session, props.post.sidebar.blocks, headers, cookies);
  }

  const metaProps: MetaProps = {
    canonical: 'https://www.benzinga.com/',
    description:
      props.post?.meta?.description ||
      'Stock Market Quotes, Business News, Financial News, Trading Ideas, and Stock Research by Professionals.',
    image: props.post?.meta?.image ?? null,
    pageType: PageType.Front,
    title: props.post?.meta?.title || 'Actionable Trading Ideas, Real-Time News, Financial Insight',
  };

  if (props?.featuredNews?.[0]) {
    metaProps.dateCreated = props.featuredNews?.[0]?.created ?? null;
    metaProps.dateUpdated = props.featuredNews?.[0]?.updated ?? null;
  }

  return (
    <PageLayout pageProps={{ ...props, metaProps, pageTargeting: { BZ_PTYPE: 'front' }, isSSR: true }}>
      <BlocksDataContext.Provider
        value={{
          extraStories: props?.extraStories ?? [],
          featuredNews: props?.featuredNews,
          initialBriefs: props?.briefs ?? [],
        }}
      >
        <HomePageWrapper>
          <HomePageTemplate featuredNews={props?.featuredNews ?? []} {...props} />
        </HomePageWrapper>
        <ErrorBoundary name="home-page-floating-wnstn-widget">
          <React.Suspense fallback={<div />}>
            <NoFirstRender>
              <FloatingWNSTNWidget />
            </NoFirstRender>
          </React.Suspense>
        </ErrorBoundary>
      </BlocksDataContext.Provider>
    </PageLayout>
  );
}

function findMatchingBlockGroup(blocks: any[], queryKey: string, queryValues: number[]) {
  let index = 0;
  const block = blocks.find(block => {
    if (block.blockName === 'acf/news-list-group') {
      return block.attrs.data.groups.find(group =>
        group.query_and_options.query[queryKey]?.some((value: number) => {
          if (queryValues.includes(value)) {
            index = block.attrs.data.groups.indexOf(group);
            return true;
          }
          return false;
        }),
      );
    }
    return false;
  });
  return block ? block.attrs.data.groups[index] : null;
}

// wrapper moved to CSS Modules in ./_components/HomePageWrapper
