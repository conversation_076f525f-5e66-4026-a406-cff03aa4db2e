'use server';
import dayjs from 'dayjs';
import { EChartsOption } from 'echarts';

import { MetaProps, PageType } from '@benzinga/seo';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { safeTimeout } from '@benzinga/safe-await';
import { GetReportDataResponse, StockReportsManager, General } from '@benzinga/stock-reports-manager';
import { getGlobalSession } from '../../../pages/api/session';

import {
  getCandle,
  fetchCandles,
  fetchCompanyLogo,
  Candle,
  getCandleColor,
  generateChartOption,
} from '../../../src/components/Report';

export type Stats = {
  chartColor: string[];
  chartOptions: EChartsOption;
  companyInfo: General | null;
  todayCandle: Candle | null;
  updatedDate: number;
  yearHi: number;
  yearLo: number;
  yesterdayCandle: Candle | null;
};

type Props = {
  candles5Year?: Candle[];
  candles60Days?: Candle[];
  candles7day?: Candle[];
  logoUrl?: string;
  metaProps?: MetaProps;
  symbol: string;
  reportData?: GetReportDataResponse | null;
  notSupported?: boolean;
  stats?: Stats;
};

const getReportData = async (symbol: string): Promise<GetReportDataResponse | null> => {
  try {
    const reportResponse = await safeTimeout(
      getGlobalSession().getManager(StockReportsManager).getReportData(symbol),
      1000,
    );
    const reportDataResponse: GetReportDataResponse | null = reportResponse?.ok ?? null;
    return reportDataResponse;
  } catch (error) {
    console.error('Error getting report data:', error);
    return null;
  }
};

export const getReportTickerPageProps = async (symbolParam: string): Promise<{ props: Props }> => {
  const symbol = symbolParam ? sanitizeHTML(symbolParam as string).toUpperCase() : '';

  const metaProps: MetaProps = {
    canonical: `https://www.benzinga.com/quote/${symbol}/report`,
    description: '',
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: PageType.Tool,
    title: `(${symbol}) Stock Analysis Report`,
  };

  try {
    const [candles7day, candles5Year, candles60Days, logoUrl, reportData] = await Promise.all([
      fetchCandles(symbol, -7, 'day', '5M'),
      fetchCandles(symbol, -5, 'year', '1W'),
      fetchCandles(symbol, -60, 'day', '1D'),
      fetchCompanyLogo(symbol),
      getReportData(symbol),
    ]);

    if (!reportData) {
      return {
        props: {
          symbol,
          notSupported: true,
        },
      };
    }

    const companyName = reportData?.data?.general?.company_name;
    if (reportData.status) {
      metaProps.title = `${companyName ? companyName + ` (${symbol})` : symbol} Stock Analysis Report | Ratings, Financials & Performance`;
      metaProps.description = `Get the latest ${companyName ? companyName + ` (${symbol})` : symbol} stock analysis report: in-depth insights, analyst ratings, financials, and historical performance. Updated ${dayjs().format('MMMM YYYY')} for smarter investing decisions.`;
    } else {
      metaProps.title = `${companyName ? companyName + ` (${symbol})` : symbol} Stock Analysis Report | Financials & Insights`;
      metaProps.description = `Explore ${companyName ? companyName + ` (${symbol})` : symbol} stock analysis with comprehensive data: access historical performance, financials, and insights. Updated ${dayjs().format('MMMM YYYY')}`;
    }

    const yesterdayCandle = getCandle(candles60Days, candles60Days.length - 2);
    const todayCandle = getCandle(candles60Days, candles60Days.length - 1);

    let yearHi = 0;
    let yearLo = Infinity;

    candles5Year.slice(-52).forEach((candle: Candle) => {
      yearHi = Math.max(yearHi, candle.high);
      yearLo = Math.min(yearLo, candle.low);
    });

    const companyInfo = reportData?.data?.general;
    const chartColor = getCandleColor(candles60Days[candles60Days.length - 2], candles60Days[candles60Days.length - 1]);
    const chartOptions = generateChartOption(candles7day, chartColor);
    const updatedDate = reportData?.data?.updated ? reportData.data?.updated * 1000 : new Date().getTime();

    const stats = {
      chartColor,
      chartOptions,
      companyInfo,
      todayCandle,
      updatedDate,
      yearHi,
      yearLo,
      yesterdayCandle,
    };
    return {
      props: {
        candles5Year,
        candles60Days,
        candles7day,
        logoUrl,
        metaProps,
        reportData,
        stats,
        symbol,
      },
    };
  } catch (error) {
    console.error('Error getting candles data:', error);
  }

  return {
    props: {
      metaProps,
      symbol,
    },
  };
};
