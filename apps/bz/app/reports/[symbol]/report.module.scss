
.header {
  position: relative;
  width: 100%;
  padding-top: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #1d456b;
  padding-left: 0.5rem;
  padding-right: 0.5rem;

  .headerContent {
    max-width: 780px;
    margin: 1rem auto;
    text-align: center;
    color: white;
    z-index: 10;

    h1 {
      font-size: 30px;
      font-weight: bold;
      color: white;
      margin-bottom: 0;
      text-transform: uppercase;
      @media (min-width: 1024px) {
        font-size: 60px;
      }

      &.notFoundHeading {
        font-size: 36px;
        font-weight: bold;
        color: white;
        margin-bottom: 0.5rem;
      }
    }

  }
}

.contentWrapper {
  padding: 0.5rem;
  background-color: #f1f8fe;
  color: #1d456b;
  position: relative;

  &.stockReport{
    margin: 0;
    @media(min-width: 1300px) {
      margin-left: 200px;
    }
  }
}
