import Image from 'next/image';
import { PageType } from '@benzinga/seo';
import PageLayout from '../../_components/PageLayout';
import ReportsList from '../components/ReportsList';
import { getSupportedTickers } from '../data';

import styles from './report.module.scss';

export const generateMetadata = async props => {
  const params = await props.params;
  const symbol = params?.symbol ? params.symbol.toUpperCase() : '';

  return {
    description:
      'The stock analysis report you requested could not be found. The ticker may be invalid or unavailable. Please check your entry or search for another stock.',
    openGraph: {
      description:
        'The stock analysis report you requested could not be found. The ticker may be invalid or unavailable. Please check your entry or search for another stock.',
      title: 'Stock Analysis Report Not Found | Invalid Ticker',
      url: 'https://www.benzinga.com/reports/' + symbol,
    },
    pageType: PageType.Tool,
    title: 'Stock Analysis Report Not Found | Invalid Ticker',
  };
};

export const ReportNotFound = async () => {
  const props = await getSupportedTickers();

  return (
    <PageLayout pageProps={{ isSSR: true }}>
      <div className={styles.header}>
        <Image
          alt={'reports hero background'}
          fetchPriority="high"
          layout="fill"
          objectFit="cover"
          src="/next-assets/images/headerbg.jpg"
        />
        <div className={styles.headerContent}>
          <h1 className={styles.notFoundHeading}>Stock Analysis Report Not Found</h1>
          <p>The ticker you entered is invalid or no longer available. Please try another search.</p>
        </div>
      </div>
      <div className={styles.contentWrapper}>
        <ReportsList allTickers={props.allTickers} />
      </div>
    </PageLayout>
  );
};

export default ReportNotFound;
