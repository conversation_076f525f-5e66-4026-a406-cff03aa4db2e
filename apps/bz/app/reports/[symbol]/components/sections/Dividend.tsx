import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';
import { EChartsOption } from 'echarts-for-react';

import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';
import { calculatePercentChange } from '../../../../../src/components/Report';

export const DividendSection = ({ data, index, name, symbol }) => {
  const buildDividendSection = (
    data: Stock,
  ): {
    change_percent: number;
    first_year: string;
    options: EChartsOption;
    title: string;
    type: string;
  } => {
    const dividendData = data?.dividend.dividend_yields.map(e => {
      return Object.values(e)[0] * 100;
    });
    const dividendYears = data?.dividend.dividend_yields.map(e => Object.keys(e)[0]);
    return {
      change_percent: calculatePercentChange(dividendData[0], data?.dividend.current_yield * 100),
      first_year: dividendYears[0],
      options: {
        graphic: [
          {
            bottom: '20%',
            left: '8%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '5%',
        },
        series: [
          {
            areaStyle: {
              color: {
                colorStops: [
                  { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                  { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                ],
                global: false,
                type: 'linear',
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
              },
            },
            color: 'rgb(16, 96, 255)',
            data: dividendData,
            lineStyle: {
              color: 'rgb(16, 96, 255)',
              type: 'dashed',
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 20,
            type: 'line',
          },
        ],
        xAxis: {
          boundaryGap: false,
          data: dividendYears,
          type: 'category',
        },
        yAxis: {
          type: 'value',
        },
      },
      title: 'Dividend',
      type: 'dividend',
    };
  };

  const section = buildDividendSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            There are few things we like to see when evaluating the quality of a company&apos;s dividend history and
            future
          </Balancer>
          <p>
            Ideally, we would like to see a company have a long history of consistently high dividend payouts that have
            grown at a consistent rate. From here we want to be confident that this sort of dividend growth and
            consistency will persist into the future.
          </p>
          <p className="font-bold">
            The chart below shows the historical trend in {name || symbol} ({symbol}) dividend yield on an annual basis.
          </p>
        </div>
        <div className="w-full h-[300px] p-4">
          <EChart chartOptions={section.options} />
        </div>
        {section.change_percent && (
          <div
            className={`flex items-center bg-white bg-gradient-to-r ${
              section.change_percent >= 0 ? 'from-green-100' : 'from-red-100'
            }`}
          >
            <div className={`text-4xl p-4 ${section.change_percent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {numeral(section.change_percent / 100).format('0,0.00%')}
            </div>
            <div>
              <div className="font-bold">
                {name || symbol} ({symbol}) saw a {section.change_percent >= 0 ? 'increase' : 'decrease'} in it&apos;s
                dividend yield since {section.first_year}
              </div>
            </div>
          </div>
        )}
      </div>
    </ReportSection>
  );
};
