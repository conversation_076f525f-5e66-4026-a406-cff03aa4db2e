import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';
import dayjs from 'dayjs';
import { EChartsOption } from 'echarts-for-react';

import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';
import { Candle } from '../../../../../src/components/Report';

export const PastSection = ({ data, name, candles, index, symbol }) => {
  const buildPastSection = (
    data: Stock,
  ): {
    options: EChartsOption;
    peer_sharpe: number;
    sharpe: number;
    title: string;
    type: string;
  } => {
    return {
      options: {
        graphic: [
          {
            bottom: '20%',
            right: '5%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '5%',
        },
        series: [
          {
            areaStyle: {
              color: {
                colorStops: [
                  { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                  { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                ],
                global: false,
                type: 'linear',
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
              },
            },
            color: 'rgb(16, 96, 255)',
            data: candles.map((candle: Candle) => {
              return candle.close;
            }),
            lineStyle: {
              color: 'rgb(16, 96, 255)',
              width: 2,
            },
            symbol: 'none',
            type: 'line',
          },
        ],
        xAxis: {
          boundaryGap: false,
          data: candles.map((candle: Candle) => {
            const date = dayjs(candle.dateTime);
            return date.format('MMM');
          }),
          type: 'category',
        },
        yAxis: {
          type: 'value',
        },
      },
      peer_sharpe: data?.peer_info?.sharpe_avg,
      sharpe: data?.past?.sharpe,
      title: 'Past Performance',
      type: 'past',
    };
  };

  const section = buildPastSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            How has {name || symbol} ({symbol}) performed over the past 5 years?
          </Balancer>
          <p>The two main factors that we consider when analyzing past performance is overall return and volatility</p>
        </div>
        <div className="w-full h-[300px] p-4">
          <EChart chartOptions={section.options} />
        </div>
        <div className="flex flex-col w-full h-full p-6">
          <p>
            Using these two metrics, we can determine if this stock gave its investors enough return for the risk that
            they took on by owning it. This is measured by the sharpe ratio, which has been used as a primary measure of
            risk/reward trade-off for almost 60 years.
          </p>
          <p>
            This ratio can be interpreted as the amount of return an investor has received for the amount of risk that
            they took on by owning the stock over that timeframe.{' '}
          </p>
        </div>
        <div className="border-t border-bzblue-200 flex items-center">
          <div className="p-6">
            <p className="font-bold">
              {name || symbol} ({symbol}) sharpe ratio over the past 5 years is{' '}
              {numeral(section.sharpe).format('0,0.0000')} which is considered to be{' '}
              {section?.sharpe && section.peer_sharpe && section.sharpe > section.peer_sharpe ? 'above ' : 'below '}
              average compared to the peer average of {numeral(section.peer_sharpe).format('0,0.0000')}
            </p>
          </div>
        </div>
      </div>
    </ReportSection>
  );
};
