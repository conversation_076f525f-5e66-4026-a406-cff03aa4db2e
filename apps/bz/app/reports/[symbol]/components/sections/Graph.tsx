import dayjs from 'dayjs';
import { EChartsOption } from 'echarts-for-react';
import { EChart } from '../EChart';
import { Candle } from '../../../../../src/components/Report';
import ReportSection from '../../../../../src/components/ReportSection';

export const GraphSection = ({ candles, index, name, symbol }) => {
  const buildGraphSection = (
    candles: Candle[],
  ): {
    options: EChartsOption;
    title: string;
    type: string;
  } => {
    return {
      options: {
        graphic: [
          {
            bottom: '20%',
            right: '5%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '12%',
        },
        series: [
          {
            areaStyle: {
              color: {
                colorStops: [
                  { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                  { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                ],
                global: false,
                type: 'linear',
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
              },
            },
            data: candles.map((candle: Candle) => {
              return candle.close;
            }),
            lineStyle: {
              color: 'rgb(16, 96, 255)',
              width: 2,
            },
            symbol: 'none',
            type: 'line',
          },
        ],
        xAxis: {
          axisLabel: {
            interval: 5,
          },
          boundaryGap: false,
          data: candles.map((candle: Candle) => {
            const date = dayjs(candle.dateTime);
            return date.format('MMM D');
          }),
          min: 'dataMin',
          splitLine: {
            interval: 5,
            show: true,
          },
          type: 'category',
        },
        yAxis: {
          min: 'dataMin',
          splitLine: {
            show: false,
          },
          type: 'value',
        },
      },
      title: `${name || symbol} (${symbol}) Stock Graph`,
      type: 'graph',
    };
  };

  const section = buildGraphSection(candles);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="flex bg-white">
        <div className="w-full h-[300px] p-4">
          <EChart chartOptions={section.options} />
        </div>
      </div>
    </ReportSection>
  );
};
