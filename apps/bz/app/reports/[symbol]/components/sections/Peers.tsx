import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import { MappedPeer, mapPeerInfo } from '../../../../../src/components/Report';
import ReportSection from '../../../../../src/components/ReportSection';

export const PeersSection = ({ data, index }) => {
  const buildTopPeersSection = (data: Stock) => {
    return {
      peers: data?.peer_info?.top_peers.map(mapPeerInfo),
      title: 'Peer Ratings',
      type: 'top_peers',
    };
  };

  const section = buildTopPeersSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="grid grid-cols-2 bg-white">
        {section?.peers?.map((peer: MappedPeer, idx: number) => (
          <div className="flex flex-col items-center py-4" key={idx}>
            <div className="w-[300px] h-[200px] mx-6">
              <EChart chartOptions={peer.options} />
            </div>
            <div className="text-bzblue-800 text-center rounded-lg bg-bzblue-200 px-4 py-2">
              <div className="text-lg font-bold">
                {peer.symbol} : {peer.exchange}
              </div>
              <div className="text-sm">{peer.name}</div>
            </div>
          </div>
        ))}
      </div>
    </ReportSection>
  );
};
