import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';
import { FaCheck } from 'react-icons/fa6';
import { EChartsOption } from 'echarts-for-react';

import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';

export const HealthSection = ({ data, index, name, symbol }) => {
  const buildHealthSection = (
    data: Stock,
  ): {
    debt_to_equity: number;
    debt_to_equity_options: EChartsOption;
    operating_margin: number;
    operating_margin_options: EChartsOption;
    quick_ratio: number;
    quick_ratio_options: EChartsOption;
    title: string;
    type: string;
  } => {
    const debtToEquity = data?.health?.debt_to_equity ?? 0;
    const debtToEquityAvg = data?.peer_info?.debt_to_equity_avg;
    const operatingMargin = data?.health?.operating_margin ?? 0;
    const operatingMarginAvg = data?.peer_info?.op_margin_avg;
    const quickRatio = data?.health?.quick_ratio ?? 0;
    const quickRatioAvg = data?.peer_info?.quick_ratio_avg;

    const waterMark = {
      right: '5%',
      style: {
        image: '/next-assets/images/Benzinga-logo-navy.svg',
        opacity: 0.2,
        width: 80,
      },
      top: '35%',
      type: 'image',
      z: 100,
    };

    return {
      debt_to_equity: debtToEquity,
      debt_to_equity_options: {
        graphic: [waterMark],
        grid: {
          bottom: '0%',
          containLabel: true,
          left: '0%',
          right: '2%',
          top: '0%',
        },
        series: [
          {
            backgroundStyle: {
              color: 'rgba(16, 96, 255, 0.1)',
            },
            barWidth: '50%',
            data: [debtToEquity],
            itemStyle: {
              borderRadius: [0, 5, 5, 0],
              color: 'rgb(16, 96, 255)',
            },
            markPoint: {
              data: [
                {
                  coord: [debtToEquityAvg, 'a'],
                  itemStyle: {
                    color: 'rgb(41,60,85)',
                  },
                  name: 'Debt to Equity Average',
                  symbol: 'diamond',
                  symbolSize: 20,
                },
              ],
            },
            showBackground: true,
            type: 'bar',
          },
        ],
        xAxis: {
          max: 0,
          min: Math.max(Math.ceil(debtToEquityAvg), Math.ceil(debtToEquity), 5),
          type: 'value',
        },
        yAxis: {
          boundaryGap: true,
          data: ['a'],
          show: false,
          type: 'category',
        },
      },
      operating_margin: operatingMargin,
      operating_margin_options: {
        graphic: [waterMark],
        grid: {
          bottom: '0%',
          containLabel: true,
          left: '0%',
          right: '2%',
          top: '0%',
        },
        series: [
          {
            backgroundStyle: {
              color: 'rgba(16, 96, 255, 0.1)',
            },
            barWidth: '50%',
            data: [operatingMargin * 100],
            itemStyle: {
              borderRadius: [0, 5, 5, 0],
              color: 'rgb(16, 96, 255)',
            },
            markPoint: {
              data: [
                {
                  coord: [operatingMarginAvg * 100, 'a'],
                  itemStyle: {
                    color: 'rgb(41,60,85)',
                  },
                  name: 'Operating Margin Average',
                  symbol: 'diamond',
                  symbolSize: 20,
                },
              ],
            },
            showBackground: true,
            type: 'bar',
          },
        ],
        xAxis: {
          max: 100,
          min: -100,
          type: 'value',
        },
        yAxis: {
          boundaryGap: true,
          data: ['a'],
          show: false,
          type: 'category',
        },
      },
      quick_ratio: quickRatio,
      quick_ratio_options: {
        graphic: [waterMark],
        grid: {
          bottom: '0%',
          containLabel: true,
          left: '0%',
          right: '2%',
          top: '0%',
        },
        series: [
          {
            backgroundStyle: {
              color: 'rgba(16, 96, 255, 0.1)',
            },
            barWidth: '50%',
            data: [quickRatio],
            itemStyle: {
              borderRadius: [0, 5, 5, 0],
              color: 'rgb(16, 96, 255)',
            },
            markPoint: {
              data: [
                {
                  coord: [quickRatioAvg, 'a'],
                  itemStyle: {
                    color: 'rgb(41,60,85)',
                  },
                  name: 'Quick Ratio Average',
                  symbol: 'diamond',
                  symbolSize: 20,
                },
              ],
            },
            showBackground: true,
            type: 'bar',
          },
        ],
        xAxis: {
          max: 0,
          min: Math.max(Math.ceil(quickRatioAvg), Math.ceil(quickRatio), 5),
          type: 'value',
        },
        yAxis: {
          boundaryGap: true,
          data: ['a'],
          show: false,
          type: 'category',
        },
      },
      title: 'Financial Health',
      type: 'health',
    };
  };

  const section = buildHealthSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="w-full h-full p-6 border-b border-bzblue-400">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            What is the current state of the company&apos;s financial situation?
          </Balancer>
          <p>
            We measure the health of a company based on how profitable they are and their ability to cover both their
            short-term and long-term debts. The key indicators that we use are the Operating Margin, Quick Ratio, and
            Debt-to-Equity ratio relative to the companies peers
          </p>
        </div>
        <div className="w-full h-full p-6 border-b border-bzblue-400">
          <p className="text-2xl mb-4">
            Operational Margin <strong>{numeral(section.operating_margin).format('0,0.0000')}</strong>
          </p>
          <div className="w-full h-[140px] p-4">
            <EChart chartOptions={section.operating_margin_options} />
          </div>
          <p>
            The operating margin measures how much profit a company makes after it spends money on wages, materials or
            other administrative expenses but before interest and taxes. It is a good representation of how efficiently
            a company is able to generate profit from its core operations.
          </p>
        </div>
        <div className="w-full h-full p-6 border-b border-bzblue-400">
          <p className="text-2xl mb-4">
            Quick Ratio <strong>{numeral(section.quick_ratio).format('0,0.0000')}</strong>
          </p>
          <div className="w-full h-[140px] p-4">
            <EChart chartOptions={section.quick_ratio_options} />
          </div>
          <p>
            The quick ratio measures how much of a company&apos;s debt, that is due in less than 1 year, can be covered
            using its cash equivalents, marketable securities, and money that is currently owed to them (accounts
            receivables).
          </p>
          <p>
            A company with a quick ratio of less than 1.00 does not, in many cases, have the capital on hand to meet its
            short-term obligations if they were all due at once, while a quick ratio greater than one indicates the
            company has the financial resources to remain solvent in the short term.
          </p>
        </div>
        <div className="w-full h-full p-6 border-b border-bzblue-400">
          <p className="text-2xl mb-4">
            Debt-to-Equity <strong>{numeral(section.debt_to_equity).format('0,0.0000')}</strong>
          </p>
          <div className="w-full h-[140px] p-4">
            <EChart chartOptions={section.debt_to_equity_options} />
          </div>
          <p>
            Debt-to-equity is calculated by dividing a company&apos;s total liabilities by its shareholders equity. It
            is a measure of the degree to which a company is financing its operations through debt versus wholly owned
            funds. Generally speaking, a D/E ratio below 1.0 would be seen as relatively safe, whereas ratios of 2.0 or
            higher would be considered risky.
          </p>
          <div className="flex items-center">
            <div className="w-12 h-12 min-w-12 min-h-12 mr-4 bg-bzblue-300 flex justify-center items-center rounded-full text-bzblue-700 font-bold">
              <FaCheck />
            </div>
            <Balancer className="text-sm font-bold">
              The chart above shows {name || symbol} ({symbol}) operating margin, quick ratio, and debt-to-equity ratio
              compared to its peers. The black markers represent the peer averages for each ratio and the blue bars
              represent {name || symbol} ({symbol}) ratio values.
            </Balancer>
          </div>
        </div>
      </div>
    </ReportSection>
  );
};
