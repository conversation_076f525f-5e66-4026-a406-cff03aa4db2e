import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';

import { Stock } from '@benzinga/stock-reports-manager';
import ReportSection from '../../../../../src/components/ReportSection';
import { parseFinancials } from '../../../../../src/components/Report';

export const FinancialSection = ({ data, index, symbol }) => {
  const buildFinancialsSection = (
    data: Stock,
  ): {
    financials: {
      balance_sheet: (string | number)[][];
      cash_flow: (string | number)[][];
      income_statement: (string | number)[][];
      years: string[];
    };
    title: string;
    type: string;
  } => {
    return {
      financials: {
        balance_sheet: data?.auxiliary?.financials ? parseFinancials(data?.auxiliary?.financials, 'Balance Sheet') : [],
        cash_flow: data?.auxiliary?.financials ? parseFinancials(data?.auxiliary?.financials, 'Cash Flow') : [],
        income_statement: data?.auxiliary?.financials
          ? parseFinancials(data?.auxiliary?.financials, 'Income Statement')
          : [],
        years: Object.keys(data?.auxiliary?.financials ?? {}).sort(),
      },
      title: 'Company Financials',
      type: 'financials',
    };
  };

  const section = buildFinancialsSection(data);

  const TableHeaders = () => {
    return (
      <div className="grid grid-cols-8">
        <div className="bg-bzblue-700 text-white py-1 font-bold col-span-3 pl-2 lg:pl-6">FISCAL YEAR (BIL.)</div>
        {section?.financials?.years.map(year => (
          <div className="bg-bzblue-700 text-white py-1 font-bold" key={year}>
            {year} ($)
          </div>
        ))}
      </div>
    );
  };

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <div className="pb-4 font-bold">INCOME STATEMENT</div>
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            The income statement also known as the profit and loss statement, primarily focuses on the company&apos;s
            revenues and expenses during a particular period.
          </Balancer>
          <p>
            The main purpose of an income statement is to convey details of profitability and business activities.
            Below, is {symbol}&apos;s income statement for the previous four years along with its trailing-twelve- month
            profit & loss.
          </p>
        </div>
        <div>
          <TableHeaders />
          {section?.financials?.income_statement?.map((incomeStatement: (string | number)[], idx: number) => (
            <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
              <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                <Balancer>{incomeStatement[0]}</Balancer>
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {numeral(incomeStatement[1]).format('0,0.00')}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {numeral(incomeStatement[2]).format('0,0.00')}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {numeral(incomeStatement[3]).format('0,0.00')}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {numeral(incomeStatement[4]).format('0,0.00')}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {numeral(incomeStatement[5]).format('0,0.00')}
              </div>
            </div>
          ))}
        </div>
        <div className="flex flex-col w-full h-full p-6">
          <div className="pb-4 font-bold">BALANCE SHEET</div>
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            The balance sheet is a snapshot of a companies financials during a particular period in time.
          </Balancer>
          <p>
            It breaks down what company owns (assets) and what a company owes (liabilities), in order to give investors
            an overview of its capital structure.
          </p>
        </div>
        <div>
          <TableHeaders />
          {section?.financials?.balance_sheet?.map((balanceSheet: (string | number)[], idx: number) => (
            <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
              <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                <Balancer>{balanceSheet[0]}</Balancer>
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">{numeral(balanceSheet[1]).format('0,0.00')}</div>
              <div className="border-b border-bzblue-300 py-1 text-sm">{numeral(balanceSheet[2]).format('0,0.00')}</div>
              <div className="border-b border-bzblue-300 py-1 text-sm">{numeral(balanceSheet[3]).format('0,0.00')}</div>
              <div className="border-b border-bzblue-300 py-1 text-sm">{numeral(balanceSheet[4]).format('0,0.00')}</div>
              <div className="border-b border-bzblue-300 py-1 text-sm">{numeral(balanceSheet[5]).format('0,0.00')}</div>
            </div>
          ))}
        </div>
        <div className="flex flex-col w-full h-full p-6">
          <div className="pb-4 font-bold">CASH FLOW STATEMENTS</div>
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            A companies statement of cash flows gives an investor a break down of the cash inflows and outflows from a
            companies operations and investment activities.
          </Balancer>
        </div>
        <div>
          <TableHeaders />
          {section?.financials?.cash_flow?.map((cashFlow: (string | number)[], idx: number) => (
            <div className={`grid grid-cols-8${idx % 2 === 0 ? ' bg-bzblue-300' : ''}`} key={idx}>
              <div className="col-span-3 pl-2 lg:pl-6 pr-4 border-b border-bzblue-300 py-1 text-sm">
                <Balancer>{cashFlow[0]}</Balancer>
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {cashFlow[1] ? numeral(cashFlow[1]).format('0,0.00') : '—'}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {cashFlow[2] ? numeral(cashFlow[2]).format('0,0.00') : '—'}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {cashFlow[3] ? numeral(cashFlow[3]).format('0,0.00') : '—'}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {cashFlow[4] ? numeral(cashFlow[4]).format('0,0.00') : '—'}
              </div>
              <div className="border-b border-bzblue-300 py-1 text-sm">
                {cashFlow[5] ? numeral(cashFlow[5]).format('0,0.00') : '—'}
              </div>
            </div>
          ))}
        </div>
      </div>
    </ReportSection>
  );
};
