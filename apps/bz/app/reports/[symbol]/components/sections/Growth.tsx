import Balancer from 'react-wrap-balancer';
import numeral from 'numeral';
import dayjs from 'dayjs';
import { EChartsOption } from 'echarts-for-react';

import { Rating, Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';
import { Candle } from '../../../../../src/components/Report';

interface AccumulatorCandle {
  total: number;
  count: number;
}

export const GrowthSection = ({ data, candles, index, name, symbol }) => {
  const buildGrowthSection = (
    data: Stock,
  ): {
    average_dollars: number;
    change_percent: number;
    max_dollars: number;
    min_dollars: number;
    num_ratings: number;
    options: EChartsOption;
    ratings: Rating[];
    title: string;
    type: string;
  } => {
    const minDollars = data?.growth?.min_pt;
    const maxDollars = data?.growth?.max_pt;
    const averageDollars = data?.growth?.average_pt;
    const changePercent = data?.growth?.pct_chg_pt / 100;
    const recentRatings = data?.growth?.recent_ratings;

    const accumulators = new Map<string, AccumulatorCandle>();
    candles.slice(-52).forEach((candle: Candle) => {
      const dateKey = dayjs(candle.dateTime).format("MMM 'YY");
      if (!accumulators.has(dateKey)) {
        accumulators.set(dateKey, { count: 0, total: 0 });
      }
      const accumulator = accumulators.get(dateKey);
      if (accumulator) {
        accumulator.total += candle.close;
        accumulator.count += 1;
      }
    });

    const yearAverages = new Map<string, number>();
    accumulators.forEach((accumulator, dateKey) => {
      yearAverages.set(dateKey, accumulator.total / accumulator.count);
    });

    const months: string[] = [];
    const averageValues: number[] = [];

    yearAverages.forEach((value, key) => {
      months.push(key);
      averageValues.push(value);
    });

    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const lastDate = months && months.length > 0 ? months[months.length - 1] : monthNames[new Date().getMonth()];
    const lastValue = months && months.length > 0 ? averageValues[averageValues.length - 1] : 0;

    if (lastDate) {
      while (!lastDate?.includes(monthNames[monthNames.length - 1])) {
        const shiftedName = monthNames.shift();

        if (shiftedName) {
          monthNames.push(shiftedName);
        }
      }
    }

    months.push(...monthNames);

    return {
      average_dollars: averageDollars,
      change_percent: changePercent || 0,
      max_dollars: maxDollars,
      min_dollars: minDollars,
      num_ratings: recentRatings?.length || 0,
      options: {
        graphic: [
          {
            bottom: '20%',
            left: '12%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '5%',
        },
        series: [
          {
            areaStyle: {
              color: {
                colorStops: [
                  { color: 'rgba(16, 96, 255, 0.3)', offset: 0 },
                  { color: 'rgba(255, 255, 255, 0)', offset: 1 },
                ],
                global: false,
                type: 'linear',
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
              },
            },
            data: averageValues,
            lineStyle: {
              color: 'rgb(16, 96, 255)',
              width: 2,
            },
            markLine: {
              data: [
                [
                  {
                    coord: [lastDate, lastValue],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    symbol: 'none',
                    symbolSize: 20,
                  },
                  {
                    coord: [monthNames[monthNames.length - 1], maxDollars],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    lineStyle: {
                      color: 'rgb(19, 220, 76)',
                      width: 2,
                    },
                    symbol: 'circle',
                    symbolSize: 20,
                  },
                ],
                [
                  {
                    coord: [lastDate, lastValue],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    symbol: 'none',
                    symbolSize: 20,
                  },
                  {
                    coord: [monthNames[monthNames.length - 1], averageDollars],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    lineStyle: {
                      color: 'rgb(230, 234, 9)',
                      width: 2,
                    },
                    symbol: 'circle',
                    symbolSize: 20,
                  },
                ],
                [
                  {
                    coord: [lastDate, lastValue],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    symbol: 'none',
                    symbolSize: 20,
                  },
                  {
                    coord: [monthNames[monthNames.length - 1], minDollars],
                    emphasis: {
                      label: {
                        show: false,
                      },
                    },
                    label: {
                      show: false,
                    },
                    lineStyle: {
                      color: 'rgb(255, 0, 0)',
                      width: 2,
                    },
                    symbol: 'circle',
                    symbolSize: 20,
                  },
                ],
              ],
            },
            markPoint: {
              data: [
                {
                  coord: [monthNames[monthNames.length - 1], maxDollars],
                  itemStyle: {
                    color: '#FFF',
                  },
                  name: 'Max',
                  symbol: 'circle',
                  symbolSize: 10,
                },
                {
                  coord: [monthNames[monthNames.length - 1], averageDollars],
                  itemStyle: {
                    color: '#FFF',
                  },
                  name: 'Average',
                  symbol: 'circle',
                  symbolSize: 10,
                },
                {
                  coord: [monthNames[monthNames.length - 1], minDollars],
                  itemStyle: {
                    color: '#FFF',
                  },
                  name: 'Min',
                  symbol: 'circle',
                  symbolSize: 10,
                },
              ],
            },
            symbol: 'none',
            type: 'line',
          },
        ],
        xAxis: {
          boundaryGap: false,
          data: months,
          type: 'category',
        },
        yAxis: {
          max: Math.max(...averageValues, maxDollars),
          min: Math.min(...averageValues, minDollars),
          type: 'value',
        },
      },
      ratings: recentRatings.slice(0, 9),
      title: 'Future Growth',
      type: 'growth',
    };
  };

  const section = buildGrowthSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            Our estimate of future price growth is based on{' '}
            {section.num_ratings !== 1
              ? 'an aggregation of ' + section.num_ratings + ' analyst ratings'
              : section.num_ratings + ' analyst rating'}{' '}
            over the past 3 months and their 12-month price targets.
          </Balancer>
          <p>
            Below, you can see that analysts are estimating a 12-month price target range of{' '}
            <strong>{numeral(section.min_dollars).format('$0,0.00')}</strong> -{' '}
            <strong>{numeral(section.max_dollars).format('$0,0.00')}</strong> with an average of{' '}
            <strong>{numeral(section.average_dollars).format('$0,0.00')}</strong>
          </p>
        </div>
        <div className="w-full h-[300px] p-4">
          <EChart chartOptions={section.options} />
        </div>
        {section?.change_percent && (
          <div
            className={`flex items-center bg-white bg-gradient-to-r ${
              section?.change_percent >= 0 ? 'from-green-100' : 'from-red-100'
            }`}
          >
            <div className={`text-4xl p-4 ${section.change_percent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {numeral(section.change_percent).format('0,0.00%')}
            </div>
            <div>
              <div className="font-bold">
                Expected movement for {name || symbol} ({symbol}) over the next 12 months
              </div>
              <div>Based on these rankings</div>
            </div>
          </div>
        )}
        <div className="p-6">
          <p>
            Recent Ratings for {name || symbol} ({symbol})
          </p>
          <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
            {section?.ratings?.map((rating: Rating, idx: number) => (
              <div className="rounded-md overflow-hidden border border-bzblue-400" key={'rating-' + idx}>
                <div className="bg-bzblue-400 text-bzblue-800 font-bold py-2 px-4 text-sm">{rating.firm_name}</div>
                <div className="p-4 text-sm">
                  <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                    <div>Date:</div>
                    <div className="lg:text-right font-bold">{dayjs(rating.date).format('MMM D, YYYY')}</div>
                  </div>
                  <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                    <div>Action:</div>
                    <div className="lg:text-right font-bold">{rating.action}</div>
                  </div>
                  <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                    <div>Prev. Target:</div>
                    <div className="lg:text-right font-bold">{numeral(rating.pt_prior).format('$0,0.00')}</div>
                  </div>
                  <div className="flex mb-2 lg:mb-0 lg:justify-between flex-col lg:flex-row">
                    <div>New Target:</div>
                    <div className="lg:text-right font-bold">{numeral(rating.pt_current).format('$0,0.00')}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </ReportSection>
  );
};
