import Balancer from 'react-wrap-balancer';
import { <PERSON>a<PERSON>he<PERSON> } from 'react-icons/fa6';
import { EChartsOption } from 'echarts-for-react';

import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import { compareValues } from '../../../../../src/components/Report';
import ReportSection from '../../../../../src/components/ReportSection';

export const RelativeVolumeSection = ({ data, index, symbol }) => {
  const buildRelativeValueSection = (
    data: Stock,
  ): {
    callout: string;
    options: EChartsOption;
    pe_avg: number;
    pe_multiple: number;
    rank: string;
    title: string;
    type: string;
  } | null => {
    const values = data?.valuation?.eps_history?.map(e => Object.values(e)[0]);

    if (!values) {
      return null;
    }

    const years = data?.valuation?.eps_history?.map(e => Object.keys(e)[0]);

    let callout = '';

    if (values?.length > 2) {
      const direction = compareValues(values[0], values[1], values[2]);
      switch (direction) {
        case 'up':
          callout = `As you can see from the chart above, ${symbol}'s earnings have increased for the past three years, this is a positive sign for the stock.`;
          break;
        case 'down':
          callout = `As you can see from the chart above, ${symbol}'s earnings have decreased for the past three years, this is a negative sign for the stock.`;
          break;
        case 'stable':
          callout = `As you can see from the chart above, ${symbol}'s earnings held steady for the past three years, this is a neutral sign for the stock.`;
          break;
        case 'fluctuating_up':
          callout = `As you can see from the chart above, ${symbol}'s earnings have fluctuated but has increased since, this is a positive sign for the stock.`;
          break;
        case 'fluctuating_down':
          callout = `As you can see from the chart above, ${symbol}'s earnings have fluctuated but has decreased since, this is a negative sign for the stock.`;
          break;
        default:
          callout = '';
          break;
      }
    }

    return {
      callout,
      options: {
        graphic: [
          {
            bottom: '12%',
            right: '0%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
            z: 100,
          },
        ],
        grid: {
          bottom: '0%',
          containLabel: true,
          left: '0%',
          right: '0%',
          top: '2%',
        },
        series: [
          {
            backgroundStyle: {
              color: 'rgba(16, 96, 255, 0.1)',
            },
            data: values,
            itemStyle: {
              borderRadius: [5, 5, 0, 0],
              color: 'rgb(16, 96, 255)',
            },
            showBackground: true,
            type: 'bar',
          },
        ],
        xAxis: {
          data: years,
          type: 'category',
        },
        yAxis: {
          type: 'value',
        },
      },
      pe_avg: data?.peer_info?.pe_avg,
      pe_multiple: data?.valuation?.pe_multiple,
      rank: '23',
      title: 'Valuation',
      type: 'relative_value',
    };
  };

  const section = buildRelativeValueSection(data);

  if (!section) {
    return null;
  }

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="w-full h-full p-6">
          <p className="text-2xl mb-4">Earnings History (3 years)</p>
          <Balancer className="text-sm font-bold">
            It is important to look at a companies earnings history to see not only if they are profitable, but if their
            earnings are growing.
          </Balancer>
          <div className="w-full h-[400px] p-4">
            <EChart chartOptions={section.options} />
          </div>
          <div className="flex items-center">
            <div className="w-12 h-12 min-w-12 min-h-12 mr-4 bg-bzblue-300 flex justify-center items-center rounded-full text-bzblue-700 font-bold">
              <FaCheck />
            </div>
            <Balancer className="text-sm font-bold">{section.callout}</Balancer>
          </div>
        </div>
      </div>
    </ReportSection>
  );
};
