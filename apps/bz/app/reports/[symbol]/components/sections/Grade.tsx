import { EChartsOption } from 'echarts-for-react';
import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';

export const GradeSection = ({ data, index, name, symbol }) => {
  const buildGradeSection = (
    data: Stock,
  ): {
    options: EChartsOption;
    title: string;
    type: string;
  } => {
    return {
      options: {
        graphic: [
          {
            bottom: '25%',
            right: '40%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.25,
              width: 60,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '0%',
          containLabel: true,
          left: '0%',
          right: '0%',
          top: '5%',
        },
        radar: {
          indicator: [
            { max: 5, name: 'VALUE' },
            { max: 5, name: 'GROWTH' },
            { max: 5, name: 'HEALTH' },
            { max: 5, name: 'PAST' },
            { max: 5, name: 'DIVIDEND' },
          ],
          shape: 'circle',
        },
        series: [
          {
            data: [
              {
                areaStyle: {
                  opacity: 0.1,
                },
                itemStyle: {
                  color: 'rgb(16, 96, 255)',
                },
                label: {
                  show: false,
                },
                value: [
                  data?.scores?.value || 0,
                  data?.scores?.growth || 0,
                  data?.scores?.health || 0,
                  data?.scores?.past || 0,
                  data?.scores?.dividend || 0,
                ],
              },
            ],
            type: 'radar',
          },
        ],
      },
      title: `How We Grade ${name || symbol} (${symbol})`,
      type: 'grade',
    };
  };

  const section = buildGradeSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="flex flex-col lg:flex-row items-center bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <p>
            We grade stocks based on past performance, their future growth potential, intrinsic value, dividend history,
            and overall financial health.
          </p>
          <p>
            The chart below shows how we grade{' '}
            <strong>
              {name || symbol} ({symbol})
            </strong>{' '}
            across the board compared to its closest peers.
          </p>
        </div>
        <div className="flex flex-col items-center py-4">
          <div className="w-[300px] h-[200px] mx-6">
            <EChart chartOptions={section.options} />
          </div>
        </div>
      </div>
    </ReportSection>
  );
};
