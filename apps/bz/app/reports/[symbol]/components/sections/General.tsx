import Balancer from 'react-wrap-balancer';
import { Stock } from '@benzinga/stock-reports-manager';
import { splitDescription } from '../../../../../src/components/Report';
import ReportSection from '../../../../../src/components/ReportSection';

export const GeneralSection = ({ data, index }) => {
  const buildGeneralSection = (
    data: Stock,
  ): {
    blurb: string;
    description: string;
    title: string;
    type: string;
  } => {
    const description = data?.general.company_description || '';
    const [blurb, remainingDescription] = splitDescription(description);

    return {
      blurb,
      description: remainingDescription,
      title: 'Company Description',
      type: 'general',
    };
  };

  const section = buildGeneralSection(data);

  return (
    <ReportSection sectionIndex={index} title="General Information">
      <div className="flex bg-white">
        <div className="w-full h-full p-6">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">{section.blurb}</Balancer>
          <p>{section.description}</p>
        </div>
        <div className="">
          <div className="w-16 h-16"></div>
        </div>
      </div>
    </ReportSection>
  );
};
