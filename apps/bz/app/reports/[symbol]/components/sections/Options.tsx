import Balancer from 'react-wrap-balancer';
import { EChartsOption } from 'echarts-for-react';

import { Stock } from '@benzinga/stock-reports-manager';
import { EChart } from '../EChart';
import ReportSection from '../../../../../src/components/ReportSection';

export const OptionsSection = ({ data, index, symbol, name }) => {
  const buildOptionsSection = (
    data: Stock,
  ): {
    options: EChartsOption;
    sentiment: string;
    title: string;
    type: string;
  } => {
    const positiveSentiment = Object.values(data?.auxiliary?.options?.options_sentiment).filter(e => e > 0).length;
    const negativeSentiment = Object.values(data?.auxiliary?.options?.options_sentiment).filter(e => e < 0).length;
    return {
      options: {
        graphic: [
          {
            bottom: '20%',
            left: '8%',
            style: {
              image: '/next-assets/images/Benzinga-logo-navy.svg',
              opacity: 0.3,
              width: 100,
            },
            type: 'image',
          },
        ],
        grid: {
          bottom: '3%',
          containLabel: true,
          left: '2%',
          right: '4%',
          top: '5%',
        },
        series: [
          {
            color: 'rgb(16, 96, 255)',
            data: Object.values(data?.auxiliary?.options?.options_sentiment),
            type: 'bar',
          },
        ],
        xAxis: {
          boundaryGap: false,
          data: Object.keys(data?.auxiliary?.options?.options_sentiment),
          type: 'category',
        },
        yAxis: {
          type: 'value',
        },
      },
      sentiment:
        positiveSentiment === negativeSentiment
          ? 'neutral'
          : positiveSentiment > negativeSentiment
            ? 'positive'
            : 'negative',
      title: `Analyzing ${name || symbol} (${symbol}) Recent Options Activity`,
      type: 'options_sentiment',
    };
  };

  const section = buildOptionsSection(data);

  return (
    <ReportSection sectionIndex={index} title={section.title}>
      <div className="bg-white">
        <div className="flex flex-col w-full h-full p-6">
          <Balancer className="text-xl font-bold lg:font-medium lg:text-3xl mb-4">
            Below, you can see the trend in options sentiment over the past 30 days.
          </Balancer>
          {section.sentiment === 'positive' && (
            <p>
              Based on our data, {symbol}&apos;s options trades have recently carried more positive sentiment than
              negative.
            </p>
          )}
          {section.sentiment === 'negative' && (
            <p>
              Based on our data, {symbol}&apos;s options trades have recently carried more negative sentiment than
              positive.
            </p>
          )}
          {section.sentiment === 'neutral' && (
            <p>Based on our data, {symbol}&apos;s options trades have recently carried more neutral sentiment.</p>
          )}
        </div>
        <div className="w-full h-[300px] p-4">
          <EChart chartOptions={section.options} />
        </div>
      </div>
    </ReportSection>
  );
};
