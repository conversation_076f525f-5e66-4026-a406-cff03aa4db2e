'use client';
import React from 'react';
import { ErrorBoundary } from '@benzinga/core-ui';
import { RaptiveAdPlaceholder, TaboolaPlacement } from '@benzinga/ads';
import { usePermission } from '@benzinga/user-context';

export const ReportSidebar = () => {
  const hasPermission = usePermission('com/reports', '#');
  const url = typeof window !== 'undefined' ? window.location.href : '';

  return (
    <ErrorBoundary name="ReportSidebar">
      <React.Suspense fallback={<div />}>
        <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" type="static-sidebar" />
      </React.Suspense>
      <TaboolaPlacement
        id="right-rail-thumbnails"
        mode="thumbnails-right"
        placement="Right Rail Thumbnails"
        settings={{
          article: 'auto',
          target_type: 'mix',
          url: url,
        }}
        shouldFlush={false}
        url={url}
        vizSensor={false}
      />
      {!hasPermission && (
        <React.Suspense fallback={<div />}>
          <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" type="sticky-sidebar" />
        </React.Suspense>
      )}
    </ErrorBoundary>
  );
};
