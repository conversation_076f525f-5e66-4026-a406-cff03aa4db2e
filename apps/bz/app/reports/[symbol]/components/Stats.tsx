import { BzImage } from '@benzinga/image';
import { EChart } from './<PERSON>hart';
import numeral from 'numeral';

export const Stats = ({ stats, logoUrl, symbol }) => {
  const { companyInfo, chartOptions, yesterdayCandle, todayCandle, yearHi = 0, yearLo = 0 } = stats || {};

  return (
    <>
      <div className="w-full my-4 flex flex-col lg:flex-row border border-bzblue-800 rounded-md">
        <div className="flex-1 p-4 flex border-r border-bzblue-800">
          {logoUrl && (
            <div className="flex justify-center items-center bg-white p-2 rounded-lg mr-4">
              <BzImage alt={`${symbol} Logo`} height={50} layout="fixed" objectFit="contain" src={logoUrl} width={50} />
            </div>
          )}
          <div className="flex-1 flex justify-between">
            {companyInfo && (
              <>
                <div>
                  <a
                    className="align-left text-xl font-bold lg:font-medium lg:text-3xl text-bzblue-200 hover:cursor-pointer"
                    href={`/quote/${symbol}`}
                    target="_blank"
                  >
                    {companyInfo?.company_name || symbol}
                  </a>
                  <div className="text-sm text-bzblue-500">
                    {companyInfo?.exchange}:{symbol}
                  </div>
                </div>
              </>
            )}
            {todayCandle && yesterdayCandle && (
              <div className="lg:hidden">
                <div className="flex-1 flex flex-col justify-center items-center">
                  <div className="text-center">
                    <div className="text-2xl">{numeral(todayCandle.close).format('$0,0.00')}</div>
                    <div
                      className={`text-sm ${
                        todayCandle.close >= yesterdayCandle.close ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      {numeral(todayCandle.close - yesterdayCandle.close).format('$0,0.00')} |{' '}
                      {numeral((todayCandle.close - yesterdayCandle.close) / yesterdayCandle.close).format('0,0.00%')}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex-1 pr-4 flex">
          <div className="w-full lg:w-2/3">{chartOptions && <EChart chartOptions={chartOptions} />}</div>
          {todayCandle && yesterdayCandle && (
            <div className="flex-1 flex-col justify-center items-center hidden lg:flex">
              <div className="text-center">
                <div className="text-2xl">{numeral(todayCandle.close).format('$0,0.00')}</div>
                <div
                  className={`text-sm ${
                    todayCandle.close >= yesterdayCandle.close ? 'text-green-400' : 'text-red-400'
                  }`}
                >
                  {numeral(todayCandle.close - yesterdayCandle.close).format('$0,0.00')} |{' '}
                  {numeral((todayCandle.close - yesterdayCandle.close) / yesterdayCandle.close).format('0,0.00%')}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-3 lg:grid-cols-5 text-sm mb-4 mx-6 text-left">
        {todayCandle && (
          <div className="flex lg:justify-center my-2">
            <div>
              <div className="text-bzblue-500">Day Range:</div>
              <div>
                {numeral(todayCandle.low).format('$0,0.00')} - {numeral(todayCandle.high).format('$0,0.00')}
              </div>
            </div>
          </div>
        )}
        {companyInfo?.market_cap && (
          <>
            <div className="flex lg:justify-center my-2">
              <div>
                <div className="text-bzblue-500">Market Cap:</div>
                <div>{numeral(companyInfo?.market_cap).format('0.00a').toUpperCase()}</div>
              </div>
            </div>
            <div className="flex lg:justify-center my-2">
              <div>
                <div className="text-bzblue-500">P/E Ratio:</div>
                <div>{numeral(companyInfo?.pe).format('0,0.0000')}</div>
              </div>
            </div>
          </>
        )}
        <div className="flex lg:justify-center my-2">
          <div>
            <div className="text-bzblue-500">Avg Value:</div>
            <div>{numeral((yearLo + yearHi) / 2).format('$0,0.00')}</div>
          </div>
        </div>
        <div className="flex lg:justify-center my-2 white-space-nowrap">
          <div>
            <div className="text-bzblue-500">Year Range:</div>
            <div>
              {numeral(yearLo).format('$0,0.00')} - {numeral(yearHi).format('$0,0.00')}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Stats;
