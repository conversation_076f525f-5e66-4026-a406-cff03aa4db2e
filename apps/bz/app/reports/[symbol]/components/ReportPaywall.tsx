'use client';
import { useEffect, useState, useMemo } from 'react';
import { AuthContainer } from '@benzinga/auth-ui';
import { usePermission } from '@benzinga/user-context';
import { checkDeviceType } from '@benzinga/device-utils';

export const ReportPaywall = () => {
  const [showPaywall, setShowPaywall] = useState(false);
  const hasPermission = usePermission('com/reports', '#');
  const isBot = useMemo(() => checkDeviceType().isBot(), []);

  useEffect(() => {
    if (!isBot) {
      setShowPaywall(!hasPermission);
    }
  }, [hasPermission, isBot]);

  if (showPaywall) {
    return (
      <>
        <AuthContainer authMode="login" iterationStyle="edge-hard" placement="report" preventRedirect={true} />
        <div className="absolute top-0 h-full w-full left-0" style={{ backdropFilter: 'blur(8px)' }}></div>
      </>
    );
  } else {
    return null;
  }
};

export default ReportPaywall;
