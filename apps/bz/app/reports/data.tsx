import { StockReportsManager } from '@benzinga/stock-reports-manager';
import { getGlobalSession } from '../../pages/api/session';

export const getSupportedTickers = async () => {
  const session = getGlobalSession();

  try {
    const response = await session.getManager(StockReportsManager).getSupportedTickers(false, { hour: 1 });

    if (!response?.ok || !response?.ok?.tickers) {
      return {
        allTickers: [],
      };
    }
    const allTickers = (response?.ok?.tickers ?? []).map(value => value);

    return {
      allTickers,
    };
  } catch (error) {
    console.log(error);
    return {
      allTickers: [],
    };
  }
};
