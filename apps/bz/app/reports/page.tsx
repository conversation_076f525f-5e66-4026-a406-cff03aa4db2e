import Image from 'next/image';
import PageLayout from '../_components/PageLayout';
import { PageType } from '@benzinga/seo';

import { getSupportedTickers } from './data';
import { UpdatedBadge } from './components/UpdatedBadge';
import ReportsList from './components/ReportsList';

import styles from './reports.module.scss';

export function generateMetadata() {
  return {
    alternates: {
      canonical: 'https://www.benzinga.com/reports',
    },
    description: '',
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    openGraph: {
      description: '',
      title: 'All Stock Analysis Reports',
      url: 'https://www.benzinga.com/reports',
    },
    pageType: PageType.Tool,
    title: 'All Stock Analysis Reports',
  };
}

export default async function ReportsPage() {
  const props = await getSupportedTickers();

  return (
    <PageLayout pageProps={{ isSSR: true }}>
      <>
        <div className={styles.header}>
          <Image
            src="/next-assets/images/headerbg.jpg"
            layout="fill"
            objectFit="cover"
            alt={'reports hero background'}
            fetchPriority="high"
          />
          <UpdatedBadge />
          <div className={styles.headerContent}>
            <h1>ALL REPORTS</h1>
            <p>Explore the latest stock analysis reports and insights</p>
          </div>
        </div>
        <div className={styles.contentWrapper}>
          <ReportsList allTickers={props.allTickers} />
        </div>
      </>
    </PageLayout>
  );
}
