
.header {
  position: relative;
  width: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .headerContent {
    max-width: 780px;
    margin: 1rem auto;
    text-align: center;
    color: white;
    z-index: 10;

    h1 {
      font-size: 30px;
      white-space: nowrap;
      font-weight: bold;
      color: white;
      margin-bottom: 0;
      @media (min-width: 1024px) {
        font-size: 60px;
      }
    }

    p {
      color: #ceddf2;
    }
  }
}

.contentWrapper {
  padding: 0.5rem;
  background-color: #f1f8fe;
  color: #1d456b;
}

.listContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 300px;
}

.reportsList {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .reportsListHeader {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    width: 100%;
    background-color: #2e79f6;
    color: white;

    div {
      padding: 0.5rem 1rem;
      text-align: start;
      font-weight: bold;
      grid-column: span 3;
    }

    div:nth-child(2) {
      grid-column: span 6;
    }
  }

  .reportListRow {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    width: 100%;
    background-color: #e1ebfa;
    border-bottom: 1px solid #e1ebfa;
    color: #1d456b;

    &:hover {
      background-color: #ceddf2;
    }

    div {
      padding: 0.5rem 1rem;
      text-align: start;
      grid-column: span 3;
    }

    div:nth-child(2) {
      grid-column: span 6;
    }

    &:nth-child(even) {
      background-color: white;
      &:hover {
        background-color: #f9fafb;
      }
    }
  }

  .companyNameSkeleton {
    width: 100%;
    height: 1.5rem;
    background-color: #e1ebfa6e;
    border-radius: 0.25rem;
    margin: 0.5rem;
    animation: pulse 1.5s infinite ease-in-out;
  }
}

.paginationContainer {
  display: flex;
  align-items: center;
  margin: 1rem 0;

  .paginationButton {
    background-color: white;
  }

  .paginationButton.active {
    background-color: #2e79f6;
    color: white;
  }
}
