'use client';
import { useCallback, useContext, useEffect, useState } from 'react';
import Link from 'next/link';

import { SessionContext } from '@benzinga/session-context';
import { DelayedQuote, QuotesManager } from '@benzinga/quotes-manager';
import { Pagination } from '@benzinga/core-ui';
import { SupportedTickersSearch } from './SupportedTickersSearch.tsx';

import styles from '../reports.module.scss';

export const ReportsList = ({ allTickers }: { allTickers: string[] }) => {
  const session = useContext(SessionContext);
  const quotesManager = session.getManager(QuotesManager);
  const pageSize = 15;

  const [searchValue, setSearchValue] = useState('');
  const [searchResult, setSearchResult] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [tickersList, setTickersList] = useState(allTickers.slice(0, pageSize));
  const [listDetails, setListDetails] = useState<Record<string, DelayedQuote> | null>(null);

  const handlePageChange = ({ nextPage, pageSize }) => {
    setCurrentPage(nextPage);
    const startIdx = (nextPage - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    const tickers = (
      searchValue.length > 0 ? allTickers.filter(ticker => ticker.includes(searchValue.toUpperCase())) : allTickers
    ).slice(startIdx, endIdx);
    setListDetails(null);
    setTickersList(tickers);
  };

  const handleSearch = val => {
    const value = val.trim();
    if (value) {
      const possibleTickers = allTickers.filter(ticker => ticker.startsWith(value.toUpperCase()));
      allTickers.forEach(ticker => {
        if (ticker.includes(value.toUpperCase()) && !possibleTickers.find(tckr => tckr == ticker)) {
          possibleTickers.push(ticker);
        }
      });
      setCurrentPage(1);
      setSearchResult(possibleTickers);
      setTickersList(possibleTickers.slice(0, pageSize));
      return possibleTickers;
    } else {
      const startIdx = (currentPage - 1) * pageSize;
      const endIdx = startIdx + pageSize;
      const tickers = allTickers.slice(startIdx, endIdx);
      setTickersList(tickers);
      return tickers;
    }
  };

  const fetchDelayedQuotes = useCallback(
    async tickersList => {
      try {
        const listQuotes = await quotesManager.getDelayedQuotes(tickersList);
        return listQuotes.ok;
      } catch (error) {
        console.error('Error fetching delayed quotes:', error);
        return undefined;
      }
    },
    [quotesManager],
  );

  useEffect(() => {
    if (tickersList) {
      fetchDelayedQuotes(tickersList).then(details => {
        setListDetails(details ?? {});
      });
    }
  }, [allTickers, currentPage, fetchDelayedQuotes, tickersList]);

  return (
    <div className={styles.listContainer}>
      <SupportedTickersSearch onSearch={handleSearch} value={searchValue} setValue={setSearchValue} />
      <div className={styles.reportsList}>
        <div className={styles.reportsListHeader}>
          <div>Ticker Name</div>
          <div>Company Name</div>
          <div>Report</div>
        </div>
        {tickersList.map((ticker, index) => (
          <Link className={styles.reportListRow} href={`/quote/${ticker}/report`} key={index}>
            <div>{ticker}</div>
            {listDetails === null ? (
              <div className={styles.companyNameSkeleton}></div>
            ) : (
              <div>{listDetails?.[ticker]?.companyStandardName ?? '-'}</div>
            )}
            <div>View</div>
          </Link>
        ))}
        {searchValue.length > 0 && searchResult.length === 0 && (
          <div>No results found for "{searchValue}". Please try a different ticker to search.</div>
        )}
      </div>
      <div className={styles.paginationContainer}>
        <Pagination
          buttonClassName={styles.paginationButton}
          defaultPage={currentPage}
          onPageChanged={handlePageChange}
          pageSize={pageSize}
          totalItems={searchValue.length > 0 ? searchResult.length : allTickers.length}
        />
      </div>
    </div>
  );
};

export default ReportsList;
