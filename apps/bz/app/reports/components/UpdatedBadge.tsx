'use client';
import dayjs from 'dayjs';
import React from 'react';

type Props = {
  date?: number | null;
};

export const UpdatedBadge: React.FC<Props> = ({ date = null }) => {
  return (
    <div className="bg-bzblue-900/80 text-white py-2 px-3 rounded-sm text-sm absolute top-4 right-4">
      <span className="text-bzblue-500 text-xs mr-2">UPDATED:</span>{' '}
      <strong>{dayjs(date ?? new Date().getTime()).format('MMM D, YYYY')}</strong>
    </div>
  );
};

export default UpdatedBadge;
