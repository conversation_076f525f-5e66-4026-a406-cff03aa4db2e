import { HiSearch } from 'react-icons/hi';

export const SupportedTickersSearch = ({
  value,
  setValue,
  onSearch,
}: {
  value: string;
  setValue: (value: string) => void;
  onSearch?: (value: string) => void;
}): JSX.Element => {
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value);
    if (onSearch) {
      onSearch(event.target.value);
    }
  };

  return (
    <div className="flex flex-row justify-end mb-5">
      <div className="flex">
        <div className="text-bzblue-700 bg-white p-3 font-bold border-solid border-2 rounded-l-full border-bzblue-500 border-r-0">
          <HiSearch />
        </div>
        <div>
          <input
            className="h-full border-solid border-2 rounded-r-full border-bzblue-500 border-l-0"
            name="supported report search"
            onChange={handleInputChange}
            placeholder="search by ticker"
            type="text"
            value={value}
          />
        </div>
      </div>
    </div>
  );
};

export default SupportedTickersSearch;
