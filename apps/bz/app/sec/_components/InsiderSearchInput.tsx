'use client';
import { useContext, useEffect, useState } from 'react';
import { FiSearch } from 'react-icons/fi';

import { SessionContext } from '@benzinga/session-context';
import { InsiderTradesManager, InsidersV2 } from '@benzinga/insider-trades-manager';
import { TrackingManager } from '@benzinga/tracking-manager';

export const InsiderSearchInput = () => {
  const [results, setResults] = useState<InsidersV2[]>([]);
  const [search, setSearch] = useState('');
  const session = useContext(SessionContext);

  useEffect(() => {
    const handleTextSearch = async text => {
      const results = await session.getManager(InsiderTradesManager).getInsiderTradesTextSearch(text);
      if (results.ok) {
        setResults(results?.ok?.insiders || []);
      }
    };
    if (search.trim()) {
      handleTextSearch(search);
      session.getManager(TrackingManager).trackSearchEvent('search', 'insider trades search', {
        query: search,
      });
    } else {
      setResults([]);
    }
  }, [search, session]);

  const handleSearchClick = () => {
    if (results.length > 0) {
      const firstResult = results[0];
      const ticker = firstResult.tickers[0];

      session.getManager(TrackingManager).trackSearchEvent('result_click', ticker ? 'company_cik' : 'insider_cik', {
        query: firstResult.cik,
      });

      const url = `/sec/insider-trades/search?${ticker ? 'company_cik=' : 'insider_cik='}${firstResult.cik}`;
      window.location.href = url;
    }
  };

  return (
    <div className="flex flex-row items-center relative">
      <input
        className="px-4 py-2 placeholder:text-sm placeholder:font-thin"
        onChange={e => setSearch(e.target.value)}
        placeholder="Search By Ticker or Insider..."
        value={search}
      />
      <div className="bg-black text-white w-10 h-10 p-2 hover:bg-bzblue-700">
        <FiSearch color="white" onClick={handleSearchClick} size={20} />
      </div>
      {results.length > 0 && (
        <div className="absolute top-12 left-0 bg-white border border-black/80 shadow-lg w-96 max-h-60 overflow-y-auto z-10">
          <ul className="divide-y divide-gray-200">
            {results.map((result, index) => {
              const ticker = result.tickers[0];
              const url = `/sec/insider-trades/search?${ticker ? 'company_cik=' : 'insider_cik='}${result.cik}`;
              return (
                <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer" key={index}>
                  <a className="flex flex-col text-black" href={url}>
                    <div className="font-bold uppercase">{result.name}</div>
                    {ticker ? (
                      <span className="text-[10px] text-gray-800">
                        Formerly: {result.formerNames.map(fm => fm.name).join(', ')}
                      </span>
                    ) : (
                      <span className="text-[10px] text-gray-800">Insider: {result.cik}</span>
                    )}
                  </a>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};
