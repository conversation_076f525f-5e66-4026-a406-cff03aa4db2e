'use client';
import { EChartsOption } from 'echarts';
import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

export const InsiderLineChart = ({ datasets, labels }) => {
  const [isReady, setIsReady] = React.useState(false);
  const series = datasets?.map(set => {
    const chart = {
      color: set.pointBackgroundColor,
      data: set.data,
      name: set.label,
      smooth: true,
      type: 'line',
    };
    return chart;
  });
  const options = React.useMemo(() => {
    return {
      grid: {
        bottom: '20%',
        height: 'auto',
        left: 100,
        right: 0,
        top: '20%',
        width: 'auto',
      },
      series: series,
      title: {
        show: false,
      },
      tooltip: {
        axisPointer: {
          animation: false,
        },
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: labels,
      },
      yAxis: {
        stacked: true,
        type: 'value',
      },
    };
  }, [labels, series]);

  if (!datasets || datasets.length === 0) {
    return (
      <div className="flex items-center justify-center h-full min-h-24">
        <p className="text-sm text-slate-500">No data available</p>
      </div>
    );
  }

  return (
    <div className="relative">
      {!isReady && (
        <div className="absolute w-full h-full bg-slate-100 animate-pulse rounded-md flex flex-col items-center justify-center text-sm text-slate-500">
          Loading...
        </div>
      )}
      <ReactECharts option={options as EChartsOption} onChartReady={() => setIsReady(true)} />
    </div>
  );
};
