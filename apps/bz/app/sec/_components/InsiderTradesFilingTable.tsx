'use client';
import React, { useState, useCallback, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import dayjs from 'dayjs';
import { DatePicker } from 'antd';
const { RangePicker } = DatePicker;
import { ErrorBoundary } from '@benzinga/core-ui';

import { Calendars, InsiderTradesCalendar } from '@benzinga/calendars';
import { InsiderTradePresetV2, InsiderTradesManager, InsidersTradeFiling } from '@benzinga/insider-trades-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { getGlobalSession } from '../../../pages/api/session';

export const getInsiderCalendar = calendarSlug => {
  return Calendars?.[calendarSlug];
};

interface InsiderPresetProps extends InsiderTradePresetV2 {
  isGated?: boolean;
  hideDatePicker?: boolean;
  showAllButton?: boolean;
  calendarSlug?: string;
  ticker?: string;
  titles?: [any];
  tableIndex?: number;
}

export const InsiderTradesFilingTable = (props: InsiderPresetProps) => {
  const {
    calendarSlug = 'insider-trades-v2',
    description,
    filings,
    id,
    isGated,
    hideDatePicker,
    showAllButton,
    tableIndex = 0,
    title,
    url,
  } = props;
  const calendarData = getInsiderCalendar(calendarSlug);
  const session = getGlobalSession();
  const searchParams = useSearchParams();
  const [filteredData, setFilteredData] = useState<InsidersTradeFiling[]>(filings ?? []);

  const handleDateSelect = useCallback(
    async (dates: any) => {
      const dateFrom = dates[0].format('YYYY-MM-DD');
      const dateTo = dates[1].format('YYYY-MM-DD');

      const allParams = searchParams && Object.fromEntries(searchParams?.entries());
      const query = {
        ...allParams,
        last_filing_date: dateFrom + ', ' + dateTo,
        limit: 100,
      };

      session.getManager(TrackingManager).trackSearchEvent('filter', 'insider filings dates', {
        query: `${dateFrom} to ${dateTo}`,
      });

      if (allParams?.preset_name) {
        const filter = await session.getManager(InsiderTradesManager).getPresetInsiderTrade(query);
        if (filter.ok?.filings) {
          setFilteredData(filter.ok?.filings);
        }
      } else {
        const filtered = await session.getManager(InsiderTradesManager).getInsiderTradesV2(query);
        if (filtered.ok?.filings) {
          setFilteredData(filtered.ok?.filings);
        }
      }
    },
    [searchParams, session],
  );

  const QuoteButton = () => {
    const company_ticker = searchParams?.get('company_ticker');
    const company_cik = searchParams?.get('company_cik') ? filings?.[0]?.company_ticker : null;
    const ticker = company_ticker || company_cik || props?.ticker;

    if (ticker && filings && filings.length > 0) {
      return (
        <div className="relative group">
          <a
            className="bg-bzblue-600 text-white px-4 rounded-sm group text-sm relative my-2 cursor-pointer"
            href={ticker ? `https://www.benzinga.com/quote/${ticker.toUpperCase()}` : undefined}
          >
            View Quote Details
          </a>
        </div>
      );
    }
    return null;
  };

  const getInsiderTitle = () => {
    const titles = props?.titles?.reduce((agg, title) => {
      if (title.value && title.value.length > 0) {
        agg = title.value.join(', ');
      }
      return agg;
    }, 'Investor');
    return titles || 'Investor';
  };

  return (
    <div className="border border-gray-200 bg-white p-2 flex flex-col w-full relative pb-4" id={id}>
      <div className="px-4 py-2 mb-2 bg-gray-100/40 border-b border-black">
        <div className="flex flex-row gap-2 items-center">
          <h2 className="">{title}</h2>
          {props.titles && (
            <div className="bg-black text-white text-sm rounded-sm px-2 w-fit h-fit">{getInsiderTitle()}</div>
          )}
        </div>
        <div className="text-sm font-bold">Updated {dayjs(filings?.[0]?.last_filing_date).format('MMM DD, YYYY')}</div>
        <div className="flex flex-row flex-wrap justify-between items-center">
          <div>
            {description ? (
              <p className="mt-2 text-slate-800 font-thin">{description}</p>
            ) : (
              <Suspense>
                <QuoteButton />
              </Suspense>
            )}
          </div>
          {!hideDatePicker && (
            <ErrorBoundary name="InsiderTradesFilingTableDatePicker">
              <RangePicker defaultValue={[null, dayjs()]} maxDate={dayjs()} onChange={handleDateSelect} />
            </ErrorBoundary>
          )}
        </div>
      </div>
      <div className="relative pb-4">
        <ErrorBoundary name="InsiderTradesFilingTable">
          <InsiderTradesCalendar
            calendar={calendarSlug}
            calendarData={calendarData}
            calendarType="server_side"
            hasStockTableStyling={true}
            height={showAllButton ? 240 : 300}
            hideFilters
            initialData={filteredData}
            isGated={isGated}
            isV2={true}
            tableIndex={tableIndex}
          />
        </ErrorBoundary>
        {!showAllButton && filteredData && filteredData.length > 0 && (
          <div className="text-sm float-right absolute -bottom-2 right-2 font-thin text-gray-500">
            Showing {filteredData.length === 100 ? 'a maximum of 100' : filteredData.length} results
          </div>
        )}
      </div>
      {showAllButton && (
        <a className="bg-bzblue-700 text-white w-full text-center p-2" href={url}>
          + CLICK HERE to See All {title?.replace('All', '')}
        </a>
      )}
    </div>
  );
};
