'use client';
import { useContext, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { InsiderTradePresetV2 } from '@benzinga/insider-trades-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

export const PresetDropdown = ({ presets = [] }: { presets: InsiderTradePresetV2[] }) => {
  const searchParams = useSearchParams();
  const [selected, setSelected] = useState<string | null>(null);
  const session = useContext(SessionContext);

  const handleSelect = preset => {
    session.getManager(TrackingManager).trackSearchEvent('filter', `insider trades preset`, {
      query: preset.id,
    });
    setSelected(preset);
    window.location.href = preset.url;
  };

  useEffect(() => {
    const selectedPreset = searchParams?.get('preset_name');
    if (selectedPreset) {
      const preset = presets.find(p => p.id === selectedPreset);
      if (preset) {
        setSelected(preset.id);
      }
    }
  }, [presets, searchParams]);

  const grouped = [
    { count: 5, label: 'Latest' },
    { count: 8, label: 'Top Insiders' },
    { count: 7, label: 'Top Officers' },
  ];

  const GroupedPresets = grouped.map((group, idx) => {
    const groupPresets = presets.slice(idx * group.count, (idx + 1) * group.count);
    return (
      <optgroup key={idx} label={group.label}>
        {groupPresets.map(preset => (
          <option key={preset?.id} value={preset?.id} onClick={() => handleSelect(preset)}>
            {preset?.title}
          </option>
        ))}
      </optgroup>
    );
  });

  return (
    <div className="">
      <select
        className="w-full bg-black text-white border border-slate-300 rounded-sm p-2"
        onChange={e => handleSelect(presets.find(p => p.id === e.target.value))}
        value={selected ?? ''}
      >
        {presets.length === 0 && <label className="w-full bg-slate-100 animate-pulse">Loading...</label>}
        <option className="hidden" disabled value="">
          Preset Insider Trade Ideas
        </option>
        {GroupedPresets}
      </select>
    </div>
  );
};
