'use client';
import { FiSliders } from 'react-icons/fi';
import { CompareBrokersButton, QuoteBuyButton } from '@benzinga/quotes-ui';
import { FAQs } from '../../../src/components/FAQs';
import { Tooltip } from '@benzinga/core-ui';

export const BuyAndBrokerButtons = () => {
  return (
    <div className="flex flex-row items-center gap-2">
      <QuoteBuyButton
        link={`https://www.benzinga.com/go/start-investing/?pl=buy&utm_source=/sec/insider-trades`}
        variant={'success'}
      />
      <CompareBrokersButton
        link={'https://www.benzinga.com/money/compare-online-brokers&utm_source=/sec/insider-trades'}
        variant={'primary'}
      />
    </div>
  );
};

export const InsiderTradesFAQs = () => {
  const dataset = [
    {
      answer:
        'Insider trading is legal so long as the insiders report those trades to the SEC. Illegal securities trading occurs when the insider is violating a fiduciary duty or fails to report their trades.',
      question: 'Is insider trading legal?',
    },
    {
      answer:
        'Insider trading, pump and dump schemes, front running and wash trades are all examples of market manipulation. Investors who track insider asset movement should think carefully about why these insiders are trading their stocks before moving forward.',
      question: 'What are some examples of market manipulation?',
    },
  ];
  return <FAQs FAQsDataSet={dataset} title="Insider Trades Frequently Asked Questions" />;
};

export const AttentionProCTA = () => {
  return (
    <p>
      <strong>Attention insiders:</strong>Dive deeper into market movements and stay ahead of the curve with Benzinga
      Pro! From up-to-the-second news to audio squawks and interactive data tools, it&apos;s everything you need to
      supercharge your trading strategy. Experience the{' '}
      <a href="https://www.benzinga.com/go/insider-trades-inline-promo" target="_blank">
        Benzinga Pro advantage today and transform the way you trade with our exclusive free trial!
      </a>
    </p>
  );
};

export const AdvancedFilterButton = () => {
  return (
    <Tooltip content="Get Advanced Filters using Benzinga Pro!">
      <a
        className="bg-black text-white w-10 h-10 p-2 hover:bg-bzblue-700 hover:cursor-pointer"
        href="https://www.benzinga.com/go/insider-trades-inline-promo?utm_source=filter_btn"
        target="_blank"
      >
        <FiSliders color="white" size={20} />
      </a>
    </Tooltip>
  );
};
