import React from 'react';
import { Metadata } from 'next';
import { AntdRegistry } from '@ant-design/nextjs-registry';

import { getInsiderTradesSearch } from './../data';
import { BuyAndBrokerButtons, AttentionProCTA } from '../../_components';
import { InsiderTradesFilingTable } from '../../_components/InsiderTradesFilingTable';
import { BENZINGA_LOGO_URL } from '@benzinga/seo';
import { toTitleCase } from '@benzinga/utils';

export const generateMetadata = async ({ searchParams }) => {
  const params = await searchParams;
  let title = 'Search Results for Insider Trading Activity';
  let canonical = 'https://www.benzinga.com/sec/insider-trades/search';
  const description = 'Insider trade filings from the SEC.';

  if (params.preset_name) {
    title = `${toTitleCase(params.preset_name.replaceAll('_', ' '))} Insider Trading Activity`;
    canonical = `https://www.benzinga.com/sec/insider-trades/search?preset_name=${params.preset_name}`;
  }

  const metadata: Metadata = {
    alternates: {
      canonical,
    },
    authors: [{ name: 'Benzinga' }],
    description,
    openGraph: {
      description,
      images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
      title,
      type: 'website',
      url: canonical,
    },
    title,
  };

  return metadata;
};

export default async function Page({ searchParams }: any) {
  const params = await searchParams;
  const data = await getInsiderTradesSearch({ ...params, limit: 100 });
  const isPreset = data && data.url;

  return (
    <div className="insider-layout">
      <div className="flex flex-col gap-2">
        <div className="flex flex-row flex-wrap justify-between">
          <h1>{isPreset ? data.title + ' Insider Trading Activity' : 'Custom Search Insider Trading Activity'}</h1>
          <BuyAndBrokerButtons />
        </div>
        <AttentionProCTA />
      </div>
      <div className="flex flex-col gap-4 mt-8">
        <AntdRegistry>
          <InsiderTradesFilingTable {...data} id={data?.id ?? ''} />
        </AntdRegistry>
      </div>
    </div>
  );
}
