import { safeTimeout } from '@benzinga/safe-await';
import { getGlobalSession } from '../../../pages/api/session';
import { InsiderTradesManager, InsidersRequestOptions } from '@benzinga/insider-trades-manager';

export const getInsiderTradesDefault = async () => {
  try {
    const session = await getGlobalSession();
    const presets = await safeTimeout(session.getManager(InsiderTradesManager).getDefaultInsiderTrade(), 1000);
    return presets.ok;
  } catch (error) {
    console.log('Error fetching insider trades default:', error);
    return [];
  }
};

export const getInsiderTradesPreset = async options => {
  try {
    const session = await getGlobalSession();
    const results = await safeTimeout(session.getManager(InsiderTradesManager).getPresetInsiderTrade(options), 1000);
    return results.ok;
  } catch (error) {
    console.log('Error fetching insider trades preset:', error);
    return null;
  }
};

export const getInsiderTradesPresets = async () => {
  try {
    const session = await getGlobalSession();
    const results = await safeTimeout(session.getManager(InsiderTradesManager).getInsiderTradesPresets(), 1000);
    return results?.ok?.presets ?? [];
  } catch (error) {
    console.log('Error fetching insider trades presets:', error);
    return null;
  }
};

export const getInsiderTradesSearch = async (options: InsidersRequestOptions) => {
  try {
    const session = await getGlobalSession();

    if (options?.preset_name) {
      const results = await safeTimeout(session.getManager(InsiderTradesManager).getPresetInsiderTrade(options), 1000);
      return results?.ok ?? null;
    }

    const results = await session.getManager(InsiderTradesManager).getInsiderTradesV2(options);
    return results?.ok ?? null;
  } catch (error) {
    console.log('Error fetching insider trades search:', error);
    return null;
  }
};

export const getInsiderTradesTextSearch = async options => {
  try {
    const session = await getGlobalSession();
    const results = await safeTimeout(
      session.getManager(InsiderTradesManager).getInsiderTradesTextSearch(options),
      1000,
    );
    return results?.ok ?? null;
  } catch (error) {
    console.log('Error fetching insider trades text search:', error);
    return null;
  }
};

export const getInsiderTradesNetWorth = async cik => {
  try {
    const session = await getGlobalSession();

    const results = await safeTimeout(session.getManager(InsiderTradesManager).getInsiderTradesNetWorth(cik), 1000);
    return results?.ok ?? null;
  } catch (error) {
    console.log('Error fetching insider trades net worth:', error);
    return null;
  }
};
