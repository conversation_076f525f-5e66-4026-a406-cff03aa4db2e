import Link from 'next/link';
import { FiHome } from 'react-icons/fi';

import PageLayout from '../_components/PageLayout';
import { getInsiderTradesPresets } from './insider-trades/data';
import { InsiderSearchInput } from './_components/InsiderSearchInput';
import { AdvancedFilterButton } from './_components';
import { PresetDropdown } from './_components/PresetDropdown';

import styles from './styles.css';

export default async function InsiderTradesLayout({ children }: { children: React.ReactNode }) {
  const presets = await getInsiderTradesPresets();
  await import('@ag-grid-community/styles/ag-grid.css');
  await import('@ag-grid-community/styles/ag-theme-alpine.css');

  return (
    <PageLayout pageProps={{ isSSR: true }}>
      <div className={'mx-auto max-w-[1400px] flex flex-row flex-wrap justify-between items-center my-8 ' + styles}>
        <div className="border-2 border-black flex flex-row items-center bg-white rounded-sm">
          <Link className="bg-black text-white w-10 h-10 p-2 hover:bg-bzblue-700" href="/sec/insider-trades">
            <FiHome color="white" size={20} />
          </Link>
          <InsiderSearchInput />
          <AdvancedFilterButton />
        </div>
        <PresetDropdown presets={presets ?? []} />
      </div>
      {children}
    </PageLayout>
  );
}
