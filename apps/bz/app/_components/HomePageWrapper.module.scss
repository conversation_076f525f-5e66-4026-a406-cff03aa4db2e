/* Container styles - migrated from HomePageContainer styled component */
.root {
  padding: 12px;
  margin: auto;

  /* Large screens (1400px+) */
  @media (min-width: 1400px) {
    max-width: 1400px;
    padding: 16px 0;
  }

  .home-page-wrapper {
    display: flex;
  }

  .main {
    flex: 1;
  }

  .sidebar {
    flex: 0;
    min-width: 300px;
    margin-left: 16px;
  }

  /* Mobile styles (800px and below) */
  @media (max-width: 800px) {
    .sidebar {
      margin-left: 0;
    }
    .home-page-wrapper {
      display: block;
    }
  }

  /* Existing styles */
  .iframe-wrapper {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    width: 100%;
    margin-bottom: 24px;
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .ad-wrapper {
    overflow: hidden;
    margin: 16px 0;
    iframe {
      margin: auto;
    }
  }

  .carousel-block {
    max-width: 990px;
  }

  .news-list-group-wrapper {
    .load-more-button {
      text-align: left;
      display: inline-block;
    }
  }
}

@media (max-width: 1300px) {
  .root {
    .layout-container {
      margin-top: 0.25rem;
    }
  }
}

.noPage {
  .raptive-ad-placement {
    margin: 1rem 0;
  }
}


