'use client';
import React, { useMemo } from 'react';
import { ToolsPageMain } from '../tools/ToolsPageMain';

import { NewsTemplate, EntitiesTemplate, MoneyPostTemplate, MoneyPageTemplate, PageProps } from '@benzinga/money';
import Error from 'next/error';
import { DynamicScript, DynamicStyle } from '../_utils/DynamicAssets';
import { PARTNER_CONTENT } from '@benzinga/internal-news-manager';

const DefaultSidebar = React.lazy(() => import('../../src/components/Sidebars/DefaultSidebar'));

interface TemplateProps {
  template: 'money' | 'page' | 'entities' | 'topic' | 'channel';
  brokerWidget?: boolean;
}

const SlugPageLayout: React.FC<{ props: PageProps }> = ({ props }) => {
  const currentTemplate = useMemo(() => {
    const tags = Array.isArray(props?.newsQuery?.tags) ? props?.newsQuery?.tags : [props?.newsQuery?.tags];
    const isPartnerContentQuery = tags?.includes(PARTNER_CONTENT.toString());
    const templates: Record<TemplateProps['template'], React.ReactNode> = {
      channel: (
        <NewsTemplate
          {...props}
          layoutFooter={
            props.brokerWidget && (
              <div className="px-4">
                <h2>Explore Benzinga&apos;s Financial Tools</h2>
                <ToolsPageMain brokerWidget={props.brokerWidget} />
              </div>
            )
          }
          layoutSidebar={
            <div>
              <React.Suspense>
                <DefaultSidebar />
              </React.Suspense>
            </div>
          }
        />
      ),
      entities: <EntitiesTemplate {...props} />,
      money: <MoneyPostTemplate {...props} />,
      page: (
        <MoneyPageTemplate
          {...props}
          layoutFooter={
            props.brokerWidget && (
              <>
                <h2>Explore Benzinga&apos;s Financial Tools</h2>
                <ToolsPageMain brokerWidget={props.brokerWidget} />
              </>
            )
          }
        />
      ),
      topic: (
        <NewsTemplate
          {...props}
          disableDefaultAd={true}
          layoutSidebar={
            <div>
              <React.Suspense>
                <DefaultSidebar enableRaptiveAd={!isPartnerContentQuery} />
              </React.Suspense>
            </div>
          }
          showCommentsIcon={true}
        />
      ),
    };

    return props.template && templates[props.template] ? templates[props.template] : <Error statusCode={404} />;
  }, [props]);

  return (
    <>
      {props.metaProps?.css && <DynamicStyle cssContent={props.metaProps.css} id="dynamic-style-slug-page" />}
      {props.metaProps?.js && <DynamicScript id="dynamic-script-slug-page" jsContent={props.metaProps.js} />}
      {currentTemplate}
    </>
  );
};
export default SlugPageLayout;
