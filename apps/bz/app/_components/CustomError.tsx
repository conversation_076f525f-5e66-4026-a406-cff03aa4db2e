'use client';
import React, { useContext } from 'react';
import { NextPage } from 'next';
import Hooks from '@benzinga/hooks';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

export type CustomErrorProps = {
  statusCode?: number;
  title?: string;
};

const ContentDefault: React.FC = () => {
  return (
    <>
      <p>Oops! Something went wrong. Please try again soon <NAME_EMAIL>.</p>
      <div>
        {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
        <a href="/">
          <span>Go to Home</span>
        </a>
      </div>
    </>
  );
};

const ContentNotFound: React.FC = () => {
  return (
    <>
      <PageNotFoundImage />
      <h1>Oops! the page you were looking for could not be found</h1>
      <p>This page doesn&apos;t exist or was removed. We suggest you head back to home</p>
      {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
      <a href="/">
        <span>Go to Home</span>
      </a>
    </>
  );
};

const pageTitle = (statusCode?: number, title?: string) => {
  if (title) {
    return title;
  }

  if (!statusCode) {
    return 'Uh-Oh! Something went wrong!';
  }

  return `Uh-Oh! ${statusCode}`;
};

const CustomError: NextPage<CustomErrorProps> = ({ statusCode, title }) => {
  const session = useContext(SessionContext);
  const trackError = statusCode => {
    if (statusCode === 404) {
      session.getManager(TrackingManager).trackErrorEvent('emit', {
        error_message: '404 Page Not Found',
      });
    }
  };

  Hooks.useEffectDidMount(() => {
    trackError(statusCode);
  });

  return (
    <div className={statusCode === 404 ? 'block-404' : 'block-error'}>
      <div>
        {statusCode === 404 ? null : <h1>{pageTitle(statusCode, title)}</h1>}
        {statusCode === 404 ? <ContentNotFound /> : <ContentDefault />}
      </div>
    </div>
  );
};

CustomError.getInitialProps = ({ err, res }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default CustomError;

const PageNotFoundImage = () => {
  return (
    <span className="page-not-found-image">
      <img alt="404 Space Image" src="/next-assets/images/404-space.svg" />
    </span>
  );
};
