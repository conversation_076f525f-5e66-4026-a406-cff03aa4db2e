import React from 'react';
import styles from './HomePageWrapper.module.scss';

export interface HomePageWrapperProps {
  children?: React.ReactNode;
  page?: string;
  className?: string;
}

export default function HomePageWrapper(props: HomePageWrapperProps): JSX.Element {
  const { children, page, className: additionalClassName } = props;
  const baseClassName = page ? styles.root : `${styles.root} ${styles.noPage}`;
  const className = additionalClassName ? `${baseClassName} ${additionalClassName}` : baseClassName;

  return <div className={className}>{children}</div>;
}
