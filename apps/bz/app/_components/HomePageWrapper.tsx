import React from 'react';
import styles from './HomePageWrapper.module.scss';

export interface HomePageWrapperProps {
  children?: React.ReactNode;
  page?: string;
}

export default function HomePageWrapper(props: HomePageWrapperProps): JSX.Element {
  const { children, page } = props;
  const className = page ? styles.root : `${styles.root} ${styles.noPage}`;
  return <div className={className}>{children}</div>;
}
