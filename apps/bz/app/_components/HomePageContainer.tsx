import React from 'react';

export interface HomePageContainerProps {
  children?: React.ReactNode;
  className?: string;
}

/**
 * HomePageContainer - Tailwind CSS replacement for the styled HomePageContainer
 * 
 * This component provides the main container layout for home pages with:
 * - Responsive padding and margins
 * - Max width constraints for large screens
 * - Flex layout for main content and sidebar
 * - Mobile-responsive behavior
 */
export default function HomePageContainer({ children, className }: HomePageContainerProps): JSX.Element {
  const baseClasses = [
    // Base container styles
    'p-3', // padding: 12px
    'mx-auto', // margin: auto
    
    // Large screen styles (1400px+)
    'xl:max-w-[1400px]', // max-width: 1400px on xl screens
    'xl:py-4', // padding: 16px 0 on xl screens
    'xl:px-0', // remove horizontal padding on xl screens
  ].join(' ');

  const finalClassName = className ? `${baseClasses} ${className}` : baseClasses;

  return (
    <div className={finalClassName}>
      <div className="home-page-wrapper flex lg:flex-row flex-col">
        {children}
      </div>
    </div>
  );
}

/**
 * HomePageMain - Main content area component
 */
export function HomePageMain({ children, className }: { children?: React.ReactNode; className?: string }) {
  const baseClasses = 'main flex-1';
  const finalClassName = className ? `${baseClasses} ${className}` : baseClasses;
  
  return <div className={finalClassName}>{children}</div>;
}

/**
 * HomePageSidebar - Sidebar component
 */
export function HomePageSidebar({ children, className }: { children?: React.ReactNode; className?: string }) {
  const baseClasses = [
    'sidebar',
    'flex-none', // flex: 0
    'min-w-[300px]', // min-width: 300px
    'lg:ml-4', // margin-left: 16px on large screens
    'ml-0', // margin-left: 0 on mobile
  ].join(' ');
  
  const finalClassName = className ? `${baseClasses} ${className}` : baseClasses;
  
  return <div className={finalClassName}>{children}</div>;
}
