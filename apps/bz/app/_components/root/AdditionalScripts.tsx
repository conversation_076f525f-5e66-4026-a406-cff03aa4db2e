'use client';
import Script from 'next/script';
import { PagePropsType } from '../../../utils/getServerProps';
//import { checkDeviceType } from '@benzinga/device-utils';
//import { PushSubscriber } from '@benzinga/auth-ui';

export default function AdditionalScripts({ pageProps }: { pageProps?: PagePropsType }) {
  //const isDesktop = Hooks.useHydrate(checkDeviceType().isDesktop(), false);

  return (
    !pageProps?.embeddedWidget && (
      <>
        <Script async defer key="" src="https://accounts.google.com/gsi/client" type="text/javascript"></Script>
        <link rel="preconnect" href="https://logx.optimizely.com" />
        {/* {isDesktop && !pageProps?.disablePushPrompt && (
          <NoFirstRender>
            <PushSubscriber />
          </NoFirstRender>
        )} */}
      </>
    )
  );
}
