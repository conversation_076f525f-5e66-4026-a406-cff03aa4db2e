:global(.bz-featured-ad) {
  position: relative;
  &::after {
    position: absolute;
    content: 'Featured Ad';
    padding: 1px 10px;
    vertical-align: super;
    font-size: 12px;
    display: inline-block;
    background: #f5f5f5;
    border-radius: 16px;
    margin-left: 3px;
    border: 1px solid #cacaca;
    bottom: 5px;
    left: inherit;
  }
}

.home-page-wrapper {
  @media (max-width: 1300px) {
    .layout-container {
      margin-top: 0.25rem;
    }
  }
  .iframe-wrapper {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    width: 100%;
    margin-bottom: 24px;
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .ad-wrapper {
    overflow: hidden;
    margin: 16px 0;
    iframe {
      margin: auto;
    }
  }
  .carousel-block {
    max-width: 990px;
  }
  .news-list-group-wrapper {
    .load-more-button {
      text-align: left;
      display: inline-block;
    }
  }
}
.loader-wrap {
  position: relative;
  height: calc(100vh);
}

.loader {
  height: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f2f8ff;
  text-align: center;
  width: 100%;
  z-index: 9999;
  h1 {
    font-size: 30px;
    color: #17253a;
    font-weight: 600;
    text-transform: uppercase;
  }
}

.block-404,
.block-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 132px);
  background-color: #F2F8FF;
  mix-blend-mode: normal;
  padding: 10px;
  text-align: center;

  h1 {
    font-size: 30px;
    color: #17253A;
    font-weight: 600;
    text-transform: uppercase;
    margin-top: 20px;
  }

  p {
    font-size: 16px;
    color: #A0AEC0;
    margin-top: 10px;
    margin-bottom: 20px;
  }

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .page-not-found-image {
    display: inline-grid;
    max-width: 450px;
    width: 100%;
  }

  a {
    color: #FFFFFF;
    background-color: #007BFF;
    font-weight: 600;
    width: 160px;
    height: 50px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
