'use client';
import { ErrorBoundary } from '@benzinga/core-ui';
import { MainMenu, NavigationHeader } from '@benzinga/navigation-ui';
import { HomeProps } from '../../../src/components/Home/interface';
import { startTransition, useEffect, useState } from 'react';
// eslint-disable-next-line
import { getLocalUserRecentTickers } from '@benzinga/quotes-manager';
import { disableOptinMonsterCampaigns } from '@benzinga/ads-utils';
// eslint-disable-next-line

import '../../styles/header.scss';

export interface RootHeaderProps {
  headerProps: HomeProps['headerProps'];
}

const RootHeader: React.FC<
  Pick<HomeProps, 'headerProps'> & {
    isLoggedIn?: boolean;
  }
> = ({ headerProps }) => {
  const [recentTickers, setRecentTickers] = useState<string[]>();

  useEffect(() => {
    document.addEventListener('om.Scripts.init', event => {
      if (event && event['detail']) {
        event['detail'].Scripts.enabled.fonts = false;
      }
    });

    // if (headerProps?.disableOptinMonster || isLoggedIn) {
    //   disableOptinMonsterCampaigns();
    // }

    disableOptinMonsterCampaigns();

    const recentTickers = getLocalUserRecentTickers();
    startTransition(() => {
      setRecentTickers(recentTickers.splice(0, 7));
    });
  }, []);

  if (headerProps?.hideNavigationBar) {
    return null;
  }

  return (
    <ErrorBoundary name="header">
      <NavigationHeader
        hideBanner={headerProps?.hideBanner}
        hideQuoteBar={headerProps?.hideQuoteBar}
        initialBreakingsNews={headerProps?.breakingNews}
        logoVariant={headerProps?.navigationLogoVariant || 'default'}
        marketTickers={headerProps?.marketTickers}
        menus={MainMenu}
        quotes={headerProps?.quotes}
        recentTickers={recentTickers}
        showAboveHeaderBlock={false}
        raptiveExpandedHeader={headerProps?.raptiveExpandedHeader}
        showRaptiveBanner={headerProps?.showRaptiveBanner}
        showRotatingBanner={
          typeof headerProps?.showRotatingBanner === 'boolean' ? headerProps?.showRotatingBanner : true
        }
      />
    </ErrorBoundary>
  );
};

export default RootHeader;
