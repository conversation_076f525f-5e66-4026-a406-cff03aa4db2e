import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next/initReactI18next';
import { initResources, LOCALES, TRANSLATION_RESOURCES } from '@benzinga/translate';

export default async function initTranslations(locale, namespaces, i18nInstance?, resources?) {
  i18nInstance = i18nInstance || createInstance();

  i18nInstance.use(initReactI18next);

  await i18nInstance.init({
    debug: false,
    defaultNS: 'common',
    fallbackLng: 'en',
    fallbackNS: 'common',
    lng: locale,
    namespaces,
    preload: resources ? false : true,
    resources: resources ?? TRANSLATION_RESOURCES,
    supportedLngs: LOCALES,
  });

  if (!resources) {
    await initResources(locale, namespaces, i18nInstance);
  }

  return {
    i18n: i18nInstance,
    resources: i18nInstance.services.resourceStore.data,
    t: i18nInstance.t,
  };
}
