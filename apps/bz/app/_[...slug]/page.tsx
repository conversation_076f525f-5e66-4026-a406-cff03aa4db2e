import SlugPageLayout from '../_components/SlugPageLayout';
import { getServerProps, SlugServerProps } from './serverProps';
import React from 'react';
import PageLayout from '../_components/PageLayout';
import { MetaProps, metaV2 } from '@benzinga/seo';
import CustomError from '../_components/CustomError';

export const generateMetadata = async props => {
  const params = await props.params;
  const { props: serverProps } = (await getServerProps({ params })) as SlugServerProps;

  return await metaV2(serverProps?.metaProps as MetaProps);
};
const SlugPage = async props => {
  const params = await props.params;
  const data = await getServerProps({ params });

  if (data.props.error) {
    return (
      <PageLayout pageProps={data.props}>
        <CustomError
          statusCode={data.props.error}
          title={data.props.error === 410 ? 'Page has been removed' : undefined}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout pageProps={data.props}>
      <SlugPageLayout {...data} />
    </PageLayout>
  );
};
export default SlugPage;
