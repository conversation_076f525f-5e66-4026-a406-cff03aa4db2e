'use client';

import React from 'react';
import { BenzingaLogo } from '@benzinga/logos-ui';
import { ServiceProduct } from '../../../pages/api/services-products';
import styles from './ServiceCard.module.scss';

export const ServiceCard: React.FC<{ product: ServiceProduct }> = ({ product }) => {
  return (
    <div className={styles.root} style={{ ['--accent' as any]: product.logoText.accentColor ?? '#1693E6' }}>
      <div className="product-card-top">
        <div className="product-card-header-logo">
          <BenzingaLogo variant="dark" />
          <div>{product.logoText.brandName}</div>
        </div>
        <div className="product-card-header-content">
          <h2>{product.title}</h2>
          <p>{product.description}</p>
        </div>
      </div>
      <div className="product-card-bottom">
        <div className="product-card-body-highlight">
          <h3 className={product.highlight.shrink ? 'trial-text' : ''}>{product.highlight.value}</h3>
          <span>{product.highlight.title}</span>
          <p>{product.highlight.description}</p>
        </div>
        <a className="product-card-button" href={product.url} rel="noreferrer" target="_blank">
          <button>get started here</button>
        </a>
      </div>
    </div>
  );
};
