import { sanitizeHTML } from '@benzinga/frontend-utils';
import Script from 'next/script';

interface DynamicScriptProps {
  jsContent: string;
  strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload';
  id: string;
}

interface DynamicStyleProps {
  cssContent: string;
  id?: string;
}

export const DynamicScript = ({
  id = 'dynamic-script',
  jsContent = '',
  strategy = 'beforeInteractive',
}: DynamicScriptProps) => {
  return <Script dangerouslySetInnerHTML={{ __html: sanitizeHTML(jsContent) }} id={id} strategy={strategy} />;
};

export const DynamicStyle = ({ cssContent = '', id = 'dynamic-style' }: DynamicStyleProps) => {
  return <style dangerouslySetInnerHTML={{ __html: cssContent }} id={id} />;
};
