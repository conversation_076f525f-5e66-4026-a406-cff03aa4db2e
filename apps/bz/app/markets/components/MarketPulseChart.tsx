'use client';
import { useState } from 'react';
import { FaArrowUpRightFromSquare, FaCaretDown, FaCaretUp } from 'react-icons/fa6';

import styles from './../styles.module.scss';
import { MenuList } from './index';
import { ErrorBoundary } from '@benzinga/core-ui';
import { TradingViewWidget } from '@benzinga/charts-ui';

type ChartRanges = '1D' | '5D' | '1M' | '3M' | '12M' | 'ALL';

export const MarketPulseChart = ({ markets }) => {
  const [chartRange, setChartRange] = useState<ChartRanges>('1D');
  const [symbol, setSymbol] = useState('SPY');
  const ranges = [
    {
      label: 'Day',
      value: '1D',
    },
    {
      label: 'Week',
      value: '5D',
    },
    {
      label: 'Month',
      value: '1M',
    },
    {
      label: 'Year',
      value: '12M',
    },
    {
      label: 'Max',
      value: 'ALL',
    },
  ];

  return (
    <ErrorBoundary name="market-pulse-chart">
      <div className={styles['block-container']}>
        <h1 className="">Markets Pulse</h1>
        <div className={styles['block-content']}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
            {markets?.map((ticker, idx) => {
              return (
                <div
                  className={
                    'border border-[#CEDDF2] rounded-md hover:bg-white hover:border-none p-2 cursor-pointer ' +
                    (ticker?.symbol === symbol ? 'bg-white border-none' : '')
                  }
                  key={idx}
                  onClick={() => setSymbol(ticker?.symbol)}
                >
                  <div className="flex flex-row justify-between items-center font-bold">
                    {ticker?.symbol}{' '}
                    <a href={`/quote/${ticker?.symbol}`} target="_blank">
                      <FaArrowUpRightFromSquare />
                    </a>
                  </div>
                  <div>
                    <span className="text-lg">${ticker?.lastTradePrice?.toFixed(2)}</span>
                    <span
                      className={`flex flex-row items-center text-sm ${ticker?.changePercent > 0 ? 'text-green-500' : 'text-red-500'}`}
                    >
                      {ticker?.changePercent > 0 ? <FaCaretUp /> : <FaCaretDown />} {ticker?.changePercent} %
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
          {/* check widget in other pages to see if dep on widgetProps will cause double renders */}
          <div className="min-h-[250px] mb-4">
            <TradingViewWidget
              widgetProps={{
                allow_symbol_change: true,
                backgroundColor: '#F2F8FF',
                height: 250,
                hide_legend: true,
                hide_side_toolbar: true,
                hide_top_toolbar: true,
                hide_volume: true,
                range: chartRange,
                style: 3,
                symbol: symbol,
                theme: 'light',
                withdateranges: false,
              }}
            />
          </div>
          <MenuList currentItem={chartRange} items={ranges} onClick={setChartRange} whiteBg />
        </div>
      </div>
    </ErrorBoundary>
  );
};
