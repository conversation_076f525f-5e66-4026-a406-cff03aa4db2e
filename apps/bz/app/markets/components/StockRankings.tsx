'use client';
import { useState } from 'react';
import { FaChevronRight } from 'react-icons/fa6';
import { BiBarChartAlt2 } from 'react-icons/bi';

import styles from './../styles.module.scss';
import { QuoteCard, MenuList } from './index';
import { EdgeCTA } from './EdgeCTA';
import { usePermission } from '@benzinga/user-context';

export const StockRankings = ({ rankings }) => {
  const [index, setIndex] = useState(0);
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');

  const handleViewAll = () => {
    const slug = rankings?.[index]?.slug;
    if (slug) {
      window.open(`https://www.benzinga.com/screener/${slug}`, '_blank');
    } else {
      window.open('https://www.benzinga.com/screener', '_blank');
    }
  };

  return (
    <div className={styles['block-container']}>
      <div className={styles['block-header']}>
        <h2>
          <BiBarChartAlt2 className="fill-bzblue-700" size={16} />
          Benzinga Stock Rankings
        </h2>
        <div className={styles['block-header-actions']} onClick={handleViewAll}>
          View All Rankings <FaChevronRight size={12} />
        </div>
      </div>
      <div className={styles['block-content']}>
        <MenuList
          currentItem={index}
          items={
            rankings && rankings.length > 0
              ? rankings?.map((table, idx) => {
                  return {
                    label: table.title,
                    value: idx,
                  };
                })
              : []
          }
          onClick={setIndex}
        />
        <div className={styles['divider']}></div>
        {rankings?.[index]?.tickers?.length === 0 && <div className="p-2 text-gray-500">No data available</div>}
        <div className="grid grid-cols-2 lg:grid-cols-4 mt-4 gap-2">
          {rankings?.[index]?.tickers?.map((ticker, index) => (
            <QuoteCard hideTicker={!hasBzEdge} key={index} ticker={ticker} />
          ))}
        </div>
        <EdgeCTA variant="ranking" />
      </div>
    </div>
  );
};

export default StockRankings;
