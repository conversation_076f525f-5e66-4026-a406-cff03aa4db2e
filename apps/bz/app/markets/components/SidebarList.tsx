import { FaChevronRight } from 'react-icons/fa6';

import { ErrorBoundary } from '@benzinga/core-ui';
import styles from './../styles.module.scss';
import { EdgeCTA, EdgeCTAVariant } from './EdgeCTA';

export const SidebarList = ({ action, children, edgeVariant = '', title }) => {
  return (
    <ErrorBoundary name={`SideBar-${title}`}>
      <div className={styles['block-container'] + ' bg-[#F2F8FF]'}>
        <div className={styles['block-header']}>
          <h5>{title}</h5>
          <a className={styles['block-header-actions']} href={action} target="_blank">
            View More <FaChevronRight size={12} />
          </a>
        </div>
        <div className={styles['block-list-wrapper']}>{children ?? null}</div>
        {edgeVariant && (
          <div className="rounded-b-md">
            <EdgeCTA variant={edgeVariant as EdgeCTAVariant} />
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};
