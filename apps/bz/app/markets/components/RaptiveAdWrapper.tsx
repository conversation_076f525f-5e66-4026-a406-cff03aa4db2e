'use client';
import React, { Suspense, useEffect, useState } from 'react';
import { usePermission } from '@benzinga/user-context';

import { type RaptiveAdPlacementType } from '@benzinga/ads-utils';

export const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.RaptiveAdPlaceholder,
  })),
);

export const RaptiveAdWrapper = ({ placement }: { placement: RaptiveAdPlacementType }) => {
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (hasBzEdge) {
      setShow(false);
    }
  }, [hasBzEdge]);

  if (!show) return null;

  return (
    <Suspense fallback={<div className="h-[90px] w-[400px] mb-2" />}>
      <RaptiveAdPlaceholder
        className="flex items-center justify-center mb-4 min-h-[50px]"
        onlyDesktop={true}
        type={placement ?? 'static-sidebar'}
      />
    </Suspense>
  );
};

export default RaptiveAdWrapper;
