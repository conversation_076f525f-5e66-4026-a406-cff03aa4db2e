'use client';
import styles from './../styles.module.scss';
import { CircularChart } from '@benzinga/charts-ui';
import { ErrorBoundary } from '@benzinga/core-ui';
import { FaCaretDown, FaCaretUp } from 'react-icons/fa6';

import { PostElapsed } from '@benzinga/news';
import { formatImageUrl } from '@benzinga/utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import React, { useContext, useEffect, useState } from 'react';
import { QuotesManager } from '@benzinga/quotes-manager';
import { SessionContext } from '@benzinga/session-context';

export const QuoteCard = ({ hideTicker = false, showLogo = false, ticker, variant = '' }) => {
  const [logoUrl, setLogoUrl] = useState(null);
  const session = useContext(SessionContext);

  useEffect(() => {
    const fetchQuoteLogo = async () => {
      const quoteManager = session.getManager(QuotesManager);
      const logo = await quoteManager.getQuotesLogos([ticker?.symbol]);
      if (logo.ok) {
        const url = logo.ok[0]?.files?.['mark_light'] ?? logo.ok[0]?.files?.['logo_light'] ?? null;
        setLogoUrl(url);
      }
    };
    if (showLogo) {
      fetchQuoteLogo();
    }
  }, [session, showLogo, ticker?.symbol]);

  return (
    <ErrorBoundary name="QuoteCard">
      <a
        className={styles['quote-card'] + (variant === 'list' ? ' ' + styles['list-item'] : '')}
        href={hideTicker ? `/markets` : `/quote/${ticker?.symbol}`}
        rel="noopener noreferrer"
        target="_blank"
      >
        <div className={styles['quote-card-header']}>
          <div className="flex flex-row items-center">
            {logoUrl && (
              <div className={styles['quote-card-logo']}>
                <img alt={ticker?.symbol} className="bg-white rounded-full w-8 h-8" src={logoUrl} />
              </div>
            )}
            <div className={styles['quote-card-name'] + `${hideTicker ? ' blur-md' : ''}`}>
              <h4>{ticker?.symbol}</h4>
              {(ticker?.name || ticker?.companyName) && <div>{ticker?.name ?? ticker?.companyName}</div>}
              {ticker?.type === 'CRYPTO' && <div>{ticker?.description}</div>}
            </div>
          </div>
          {ticker?.rankingValue && (
            <div className="min-w-10 min-h-10">
              <React.Suspense fallback={<div className="w-10 h-10"></div>}>
                <CircularChart
                  backgroundColor={'rgba(48, 191, 96, 0.1)'}
                  indicatorColor={'rgba(48, 191, 96, 0.1)'}
                  showPercent={false}
                  textColor={'#0F993D'}
                  trackColor={'#30BF60'}
                  value={parseInt(ticker?.rankingValue)}
                />
              </React.Suspense>
            </div>
          )}
        </div>
        <div className={styles['quote-card-price']}>
          <div>${ticker?.lastTradePrice ?? ticker?.price}</div>
          <span className={ticker?.changePercent > 0 ? 'text-green-500' : 'text-red-500'}>
            {ticker?.changePercent > 0 ? <FaCaretUp size={12} /> : <FaCaretDown size={12} />}
            {ticker?.changePercent}%
          </span>
        </div>
      </a>
    </ErrorBoundary>
  );
};

export const ArticleListItem = ({ article, showTeaser = false }) => {
  const imageSrc = article?.image && formatImageUrl({ image: article.image }, true, false);

  if (!article) {
    return null;
  }
  return (
    <a
      className={styles['article-list-item'] + ' flex flex-row gap-2'}
      href={`/article/${article?.id}`}
      key={article?.id}
      rel="noopener noreferrer"
      target="_blank"
    >
      {article.image && (
        <div className={'overflow-hidden ' + showTeaser ? styles['full-item'] : styles['short-item']}>
          <div className={styles['article-thumb']}>
            <img alt="" className="post-thumb w-full h-full object-cover" src={imageSrc} />
          </div>
        </div>
      )}
      <div className={styles['article-post-card']}>
        <div className="flex flex-col">
          <h4
            className="w-full overflow-ellipsis line-clamp-2"
            dangerouslySetInnerHTML={{ __html: sanitizeHTML(article.title) }}
          ></h4>
          {showTeaser && (
            <div
              className="text-sm text-[#192940] w-full items-center overflow-hidden overflow-ellipsis line-clamp-2"
              dangerouslySetInnerHTML={{ __html: sanitizeHTML(article.teaser) }}
            ></div>
          )}
        </div>
        <div className="flex flex-row justify-between items-center mt-1">
          <div className="flex flex-row gap-1 text-xs">
            {article?.tickers?.slice(0, 2)?.map(ticker => {
              return (
                <div className="p-1 bg-bzblue-600/10 rounded-sm" key={ticker.name}>
                  {ticker?.name}
                </div>
              );
            })}
            {article?.tickers?.length > 3 && (
              <div className="p-1 bg-bzblue-600/10 rounded-sm">+ {article.tickers.length - 2} more</div>
            )}
          </div>
          <PostElapsed
            className="font-semibold text-gray-500 post-created-at mr-1"
            created={article?.created}
            dateType={article?.dateType}
          />
        </div>
      </div>
    </a>
  );
};

export const MenuList = ({ currentItem, items, onClick, whiteBg = false }) => {
  return (
    <ErrorBoundary name="MenuList">
      <div
        className={
          'p-1 flex flex-row divide-x divide-[#3f83f8]/30 w-fit rounded-md gap-1' + (whiteBg ? ' bg-white' : '')
        }
      >
        {items?.map(item => {
          return (
            <div
              className={`whitespace-nowrap text-sm text-center text-[#3F83F8] cursor-pointer px-4 py-1 capitalize font-bold ${
                item?.value === currentItem ? 'bg-[#3F83F8] text-white rounded-md' : ''
              }`}
              key={item?.value}
              onClick={() => onClick && onClick(item?.value)}
            >
              {item?.label}
            </div>
          );
        })}
      </div>
    </ErrorBoundary>
  );
};
