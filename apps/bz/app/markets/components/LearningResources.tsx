'use client';
import { FaChevronRight, FaRegBookmark } from 'react-icons/fa6';
import styles from './../styles.module.scss';

const resources = [
  {
    links: [
      { label: 'How to Buy Stocks', url: 'https://www.benzinga.com/money/how-to-buy-stocks' },
      { label: 'Do I Need a Financial Advisor', url: 'https://www.benzinga.com/money/do-i-need-a-financial-advisor' },
      { label: 'How to Buy ETFs', url: 'https://www.benzinga.com/money/how-to-buy-etf' },
    ],
    title: 'Getting Started',
  },
  {
    links: [
      { label: 'Are Bonds a Good Investment', url: 'https://www.benzinga.com/money/are-bonds-a-good-investment' },
      { label: 'How to Trade Forex with $100', url: 'https://www.benzinga.com/money/how-to-trade-forex-with-100' },
      { label: 'How to Trade Options', url: 'https://www.benzinga.com/money/options-trading-beginners' },
    ],
    title: 'Advanced Topics',
  },
  {
    links: [
      {
        label: 'Complete Guide to Pre-Market Trading',
        url: 'https://www.benzinga.com/pro/blog/complete-guide-pre-market-trading',
      },
      {
        label: 'How to Choose a Stock Screener',
        url: 'https://www.benzinga.com/pro/blog/how-to-choose-a-stock-screener',
      },
      {
        label: 'How to Use Stock Scanning Software',
        url: 'https://www.benzinga.com/pro/blog/how-to-use-stock-scanning-software',
      },
    ],
    title: 'Benzinga Pro',
  },
];

export const LearningResources = () => {
  return (
    <div className={styles['block-container']}>
      <div className={styles['block-header']}>
        <h2>
          <FaRegBookmark className="text-bzblue-700" size={16} /> Learn &amp; Grow
        </h2>
        <a className={styles['block-header-actions']} href="/money" target="_blank">
          More Education <FaChevronRight size={12} />
        </a>
      </div>
      <div className="grid grid-cols-1 gap-2 px-2 pb-2">
        {resources.map((group, index) => (
          <div className={styles['resource-group']} key={index}>
            <h4 className="uppercase mb-2">{group.title}</h4>
            <div className="grid grid-cols-1 gap-2">
              {group.links.map((link, idx) => (
                <a href={link.url} key={idx}>
                  {link.label}
                </a>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LearningResources;
