'use client';
import { useContext, useEffect, useRef, useState } from 'react';
import styles from './../styles.module.scss';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { SessionContext } from '@benzinga/session-context';
import { QuotesManager } from '@benzinga/quotes-manager';

export const TrendingStocksTape = ({ trending }) => {
  const scrollRef = useRef<HTMLUListElement | null>(null);
  const [logos, setLogos] = useState({});
  const session = useContext(SessionContext);

  useEffect(() => {
    const fetchQuoteLogo = async () => {
      const quoteManager = session.getManager(QuotesManager);
      const logos = await quoteManager.getQuotesLogosMapped([trending.map(stock => stock?.symbol)]);
      if (logos.ok) {
        console.log('logos', logos.ok);
        setLogos(logos.ok);
      }
    };

    fetchQuoteLogo();
  }, [session, trending]);

  const getLogoUrl = logo => {
    if (logo) {
      return logo?.files?.['mark_light'] ?? logo?.files?.['logo_light'] ?? null;
    }
    return null;
  };

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({
        behavior: 'smooth',
        left: -scrollRef.current.clientWidth,
      });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({
        behavior: 'smooth',
        left: scrollRef.current.clientWidth,
      });
    }
  };

  return (
    <div className={styles['trending-stocks-tape']}>
      <div className="flex flex-row items-center justify-between px-2 py-2 w-full">
        <h2>Trending Stocks</h2>
        <div className="flex flex-row items-center gap-2">
          <button className="rounded-full bg-[#3F83F8]/5 text-[#5B7292] p-2" onClick={scrollLeft}>
            <FaChevronLeft size={12} />
          </button>
          <button className="rounded-full bg-[#3F83F8]/5 text-[#5B7292] p-2" onClick={scrollRight}>
            <FaChevronRight size={12} />
          </button>
        </div>
      </div>
      <ul className={styles['tape']} ref={scrollRef}>
        {trending?.length === 0 && <li>No data available</li>}
        {trending?.map((stock, idx) => (
          <a
            className={styles['tape-item'] + ' z-10'}
            href={`/quote/${stock?.symbol}`}
            key={stock?.symbol ?? idx}
            rel="noopener noreferrer"
            target="_blank"
          >
            {logos[stock?.symbol] && (
              <img className="bg-white rounded-full w-5 h-5" src={getLogoUrl(logos[stock?.symbol])} />
            )}
            <span>{stock?.symbol}</span>
            <div className={stock?.changePercent > 0 ? 'text-green-500' : 'text-red-500'}>{stock?.changePercent}%</div>
          </a>
        ))}
      </ul>
    </div>
  );
};

export default TrendingStocksTape;
