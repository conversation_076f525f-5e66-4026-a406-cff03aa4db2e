'use client';
import { useState } from 'react';
import { FaChevronRight } from 'react-icons/fa6';
import { FiGlobe } from 'react-icons/fi';

import styles from './../styles.module.scss';
import { MenuList, QuoteCard } from './index';
import { EdgeCTA } from './EdgeCTA';

export const GlobalMarkets = ({ commodities, global }) => {
  const tabs = [
    { action: '/topic/global', key: 'global', label: 'International Indices', tickers: global ?? [] },
    { action: '/markets/commodities', key: 'commodities', label: 'Commodities', tickers: commodities ?? [] },
  ];
  const [index, setIndex] = useState(0);

  return (
    <div className={styles['block-container']}>
      <div className={styles['block-header']}>
        <h2>
          <FiGlobe className="text-bzblue-700" size={16} />
          Global Markets &amp; Commodities
        </h2>
        <a className={styles['block-header-actions']} href={tabs[index].action} target="_blank">
          View {tabs[index].key} News <FaChevronRight size={12} />
        </a>
      </div>
      <div className={styles['block-content']}>
        <MenuList
          currentItem={index}
          items={tabs.map((tab, idx) => {
            return {
              label: tab.label,
              value: idx,
            };
          })}
          onClick={setIndex}
        />
        <div className={styles['divider']}></div>
        {tabs?.[index]?.tickers?.length === 0 && <div className="p-2 text-gray-500">No data available</div>}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 mt-4">
          {tabs?.[index]?.tickers?.map((ticker, index) => <QuoteCard key={index} ticker={ticker} />)}
        </div>
        <EdgeCTA variant="market" />
      </div>
    </div>
  );
};

export default GlobalMarkets;
