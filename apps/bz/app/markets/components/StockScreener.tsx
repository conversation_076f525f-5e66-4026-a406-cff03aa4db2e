'use client';
import { useState, useEffect } from 'react';
import { FaChevronRight } from 'react-icons/fa6';

import styles from './../styles.module.scss';
import { QuoteCard } from './index';

const SignalIcon = () => {
  return (
    <svg fill="none" height="16" viewBox="0 0 20 16" width="20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.3418 10.5899C6.80764 10.0903 7.37089 9.6915 7.99682 9.41804C8.62274 9.14458 9.29856 9.00246 9.98161 9.00007C10.6647 8.99769 11.3409 9.13534 11.9687 9.40443C12.5965 9.67351 13.1627 10.0683 13.6321 10.5646M4.14844 7.54393C4.89378 6.74465 5.79499 6.10655 6.79647 5.66902C7.79795 5.23148 8.87829 5.00389 9.97116 5.00007C11.064 4.99626 12.1456 5.21651 13.1501 5.64704C14.1546 6.07757 15.0608 6.70942 15.8117 7.50348M1.22266 4.81635C2.34068 3.61742 3.69249 2.66028 5.19471 2.00398C6.69693 1.34768 8.31694 1.0058 9.95626 1.00007C11.5956 0.994351 13.2199 1.32472 14.7266 1.97052C16.2334 2.61632 17.5921 3.56458 18.7185 4.75568M9.99902 15.0001C9.44674 15.0001 8.99902 14.5524 8.99902 14.0001C8.99902 13.4478 9.44674 13.0001 9.99902 13.0001C10.5513 13.0001 10.999 13.4478 10.999 14.0001C10.999 14.5524 10.5513 15.0001 9.99902 15.0001Z"
        stroke="#3F83F8"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
      />
    </svg>
  );
};

export const StockScreener = ({ initialScreener }) => {
  const [screenerConfig, setScreenerConfig] = useState({
    'market-cap': initialScreener?.marketCap,
    'price-range': initialScreener?.priceRange,
    sector: initialScreener?.sector,
  });
  const [results, setResults] = useState(initialScreener?.results ?? []);

  useEffect(() => {
    // need to pull the options for 3 dropdowns or define them
    // store value to state
    // and then fetch results based on the selected values
    const fetchResults = async () => {
      // screener manager
      // const data = await response.json();
      // setResults(data.results);
      console.log('setup screener manager');
    };
    fetchResults();
  }, [screenerConfig]);

  return (
    <div className={styles['block-container']}>
      <div className={styles['block-header']}>
        <h2>
          <SignalIcon />
          Global Markets & Commodities
        </h2>
        <a className={styles['block-header-actions']} href="/screener" target="_blank">
          View full screener <FaChevronRight size={12} />
        </a>
      </div>
      <div className={styles['block-content']}>
        <div>
          <select>market cap</select>
          <select>sector</select>
          <select>price range</select>
        </div>
        <div className={styles['divider']}></div>
        {results.length === 0 && <div className="p-2 text-gray-500">No data available</div>}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-4">
          {results.map((ticker, index) => (
            <QuoteCard key={index} ticker={ticker} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default StockScreener;
