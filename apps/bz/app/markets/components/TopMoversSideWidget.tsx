'use client';
import { useState } from 'react';
import { FaChevronRight } from 'react-icons/fa6';

import styles from './../styles.module.scss';
import { MenuList, QuoteCard } from './index';
import { ErrorBoundary } from '@benzinga/core-ui';
import { ClientNewsListElement } from './ClientNewsListElement';

export const TopMoversSideWidget = ({ movers, wiims }) => {
  const tabs = [
    { action: '/movers', items: movers ?? [], key: 'movers', label: 'Price Movers' },
    { action: '/topic/why-its-moving', items: wiims ?? [], key: 'WIIMS', label: 'Why Is It Moving' },
  ];
  const [index, setIndex] = useState(0);

  return (
    <ErrorBoundary name="top-movers">
      <div className={styles['block-container']}>
        <div className={styles['block-header']}>
          <h2>Top Movers</h2>
          <a className={styles['block-header-actions']} href={tabs[index].action} target="_blank">
            View {tabs[index].key} News <FaChevronRight size={12} />
          </a>
        </div>
        <div className={styles['block-content']}>
          <MenuList
            currentItem={index}
            items={tabs.map((tab, idx) => {
              return {
                label: tab.label,
                value: idx,
              };
            })}
            onClick={setIndex}
          />
          <div className={styles['divider']}></div>
          {tabs?.[index]?.items?.length === 0 && (
            <div className="p-2 text-gray-500 text-center min-h-52">No data available</div>
          )}
          <div className="flex flex-col gap-2 mt-4">
            {tabs?.[index]?.items?.map((item, index) => {
              if (item.author) {
                return <ClientNewsListElement article={item} key={index} showFull={false} />;
              } else {
                return <QuoteCard key={index} showLogo ticker={item} variant="list" />;
              }
            })}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default TopMoversSideWidget;
