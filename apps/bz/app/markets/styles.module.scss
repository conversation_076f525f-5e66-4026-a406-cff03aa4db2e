.block-container {
  border: 1px solid #CEDDF2;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;

  h1 {
    font-family: Manrope, sans-serif;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
    text-transform: capitalize;
    padding: 0.75rem 1rem;
  }
}

.block-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  flex-wrap: wrap;

  h2 {
    font-family: Manrope, sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    vertical-align: middle;
    white-space: nowrap;
    padding-right: 0.5rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.block-header-actions {
  font-family: Manrope, sans-serif;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 0.25rem;
  color: #3F83F8;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  text-transform: capitalize;
}

.block-content {
  background-color: #F2F8FF;
  padding: 0.5rem;
  font-family: Manrope, sans-serif;
}

.block-list-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
}

.divider {
  margin-top: 0.5rem;
  border-bottom: 1px solid #CEDDF2;
}

.trending-stocks-tape {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  margin-bottom: 1rem;
  flex-wrap: nowrap;
  align-items: center;
  position: relative;
  border: 1px solid #CEDDF2;
  border-radius: 8px;

  h2 {
    font-family: Manrope, sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    vertical-align: middle;
    white-space: nowrap;
    padding-left: 0.5rem;
  }

  .tape-overlay {
    position: absolute;
    top: 0;
    right: 88px;
    bottom: 0;
    background: linear-gradient(
      to right,
      white 0%,
      transparent 5%,
      transparent 95%,
      white 100%
    );
    z-index: 1;
  }

  .tape {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    list-style: none;
    padding: 0;
    overflow-x: auto;
    scrollbar-width: 0;
    width: 100%;
    height: 100%;
    padding: 8px;

    .tape-item {
      display: flex;
      justify-content: space-between;
      gap: 0.25rem;
      align-items: center;
      padding: 4px;
      border-top-left-radius: 50px;
      border-top-right-radius: 50px;
      border-bottom-right-radius: 50px;
      border-bottom-left-radius: 50px;
      background-color: rgba(63, 131, 248, 0.1);
      font-family: Manrope, sans-serif;
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0%;
      vertical-align: middle;
      min-width: fit-content;
      padding-right: 8px;

      span {
        color: #1E293B;
      }

      &:hover {
        background-color: #E2E8F0;
        cursor: pointer;
      }
      &:active {
        background-color: #CBD5E1;
      }
    }
  }
}

.quote-card {
  border: 1px solid #CEDDF2;
  border-radius: 8px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
  justify-content: space-between;
  font-family: Manrope, sans-serif;

  &:hover {
    background-color: #F2F8FF;
    cursor: pointer;
  }

  .quote-card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;

    .quote-card-name {
      padding-left: 4px;
      h4 {
        font-family: Manrope, sans-serif;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        vertical-align: middle;
        white-space: nowrap;
        padding-right: 0.5rem;
        margin-bottom: 0;
      }

      div {
        font-family: Manrope, sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #5B7292;
      }
    }
  }

  .quote-card-price {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    div {
      font-weight: 800;
      color: #283D59;
    }
    span {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 14px;
    }
  }
}

.quote-card.list-item {
  background-color: white;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: none;
  padding-left: 0.25rem;
  .quote-card-header {
    align-items: center;
  }
  .quote-card-price {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0;
    span {
      font-size: 12px;
    }
  }
}

.article-list-item {
  border-radius: 8px;
  background-color: white;
  overflow: hidden;
  width: 100%;

  .short-item {
    width: 40px !important;
    max-width: 40px !important;
    min-width: 40px !important;
  }
  .full-item {
    width: 100px !important;
    max-width: 100px !important;
    min-width: 100px !important;
  }

  .article-thumb {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .article-post-card {
    padding: 0.5rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;

    h4 {
      font-family: Manrope, sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0%;
      vertical-align: middle;
      margin-bottom: 0.25rem;
    }
  }
}

.resource-group {
  background-color: #F2F8FF;
  border-radius: 8px;
  padding: 0.5rem;

  h4 {
    font-family: Manrope, sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    vertical-align: middle;
    white-space: nowrap;
    padding-right: 0.5rem;
  }
  a {
    background-color: white;
    padding: 0.5rem;
    border-radius: 8px;
    font-size: 14px;
    color: #3F83F8;
    font-weight: 700;
    flex-grow: 1;
  }
}

