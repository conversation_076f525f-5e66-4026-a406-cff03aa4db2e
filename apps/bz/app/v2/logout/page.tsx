import { BzImage } from '@benzinga/image';
import { MetaProps } from '@benzinga/seo';

import styles from './styles.module.scss';
import <PERSON>goutHandler from './LogoutHandler';
import PageLayout from '../../_components/PageLayout';
import { getLanguageByHost } from '../../i18n/utils';
import initTranslations from '../../i18n/translations';
import TranslationsProvider from '../../i18n/TranslationProvider';

export async function generateMetadata() {
  const language = await getLanguageByHost();
  const { t } = await initTranslations(language, ['common', 'auth']);

  return {
    author: 'Benzinga',
    canonical: 'https://www.benzinga.com/logout',
    description: t('Auth.Meta.logout-from-benzinga', { ns: 'auth' }),
    title: t('Auth.Meta.logout-from-benzinga', { ns: 'auth' }),
  };
}

function getPageProps() {
  const props = {
    disablePushPrompt: true,
    headerProps: {
      hideBanner: true,
      hideFooter: false,
      hideNavigationBar: false,
      hideQuoteBar: true,
    },
  };
  return { props };
}

const LogoutPage = async () => {
  const { props } = getPageProps();
  const language = await getLanguageByHost();
  const { i18n, resources, t } = await initTranslations(language, ['common', 'auth']);

  return (
    <TranslationsProvider locale={i18n.options.lng} namespaces={i18n.options.namespaces} resources={resources}>
      <PageLayout pageProps={props}>
        <div className={styles.logoutWrapper}>
          <div className={styles.messageWrapper}>
            <BzImage alt="logout icon" height="100" src="/next-assets/images/logout-graphic.png" width="100" />
            <h1>{t('Auth.Logout.logout-successful', { ns: 'auth' })}</h1>
            <div>
              <div className={styles.spinnerWrapper}>
                <svg
                  className="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    fill="currentColor"
                  ></path>
                </svg>
              </div>
              <p>{t('Auth.Logout.returning-to-prev-page', { ns: 'auth' })}</p>
            </div>
          </div>
          <LogoutHandler />
        </div>
      </PageLayout>
    </TranslationsProvider>
  );
};

export default LogoutPage;
