'use client';
import React from 'react';
import { Product } from '@benzinga/content-manager';
import {
  FusionHelpCard,
  FusionSection,
  FusionSectionContent,
  FusionSectionCaption,
  FusionSectionTitle,
  FusionServiceCards,
} from '@benzinga/ui';

import styles from '../../styles.module.scss';

interface Props {
  products: Product[];
}
const OurServices = ({ products }: Props) => {
  return (
    <FusionSection sectionPadding={false} whiteBackground>
      <FusionSectionContent paddingHorizontal={false} paddingVertical sectionPadding>
        <FusionSectionCaption>OUR SERVICES</FusionSectionCaption>
        <FusionSectionTitle className={styles.ourServiceSectionTitle}>
          We have created tools that help hundreds of our clients every day
        </FusionSectionTitle>
      </FusionSectionContent>
      {/*<FusionSectionGrid columns={{ lg: 3, md: 2 }} mobileScroll>*/}
      {/*  {products?.map((card, i) => (*/}
      {/*    <FusionServiceCard key={i} {...card} />*/}
      {/*  ))}*/}
      {/*</FusionSectionGrid>*/}
      <FusionServiceCards columns={{ lg: 3, md: 2 }} products={products} withScroll />
      <FusionSectionContent
        className={styles.helpSectionContent}
        paddingHorizontal={false}
        paddingVertical={false}
        sectionPadding
      >
        <FusionHelpCard />
      </FusionSectionContent>
    </FusionSection>
  );
};

export default OurServices;
