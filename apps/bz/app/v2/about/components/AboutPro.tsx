'use client';
import { Button } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { proLinks } from './mock';
import React from 'react';
import styled from '@benzinga/themetron';
import Separator from './OurServices/Separator';
import { BzImage } from '@benzinga/image';
import { FusionSection, FusionSectionContent, fusionSectionContentPadding } from '@benzinga/ui';
import Image from 'next/image';

import styles from '../styles.module.scss';

const AboutPro = () => {
  return (
    <FusionSection className={styles.aboutProSection} sectionPadding={false}>
      <FusionSectionContent
        className="flex flex-col items-center"
        paddingHorizontal={false}
        paddingVertical
        sectionPadding
      >
        <div className={styles.proFlexContainer}>
          <span className={styles.proSubtitle}>Profit with</span>
          <BzImage height={30} src="/next-assets/images/about/logo.svg" width={126} />
        </div>
        <h5 className={styles.proTitle}>ACTIONABLE STOCK NEWS, TRADING SIGNALS AND INTELLIGENT CHATROOMS</h5>
        <p className={styles.proDescription}>
          The most powerful news and data software for retail traders, delivers news before significant price action
          occurs.
        </p>
        <SmartLink className="inline-block mt-8" href="https://pro.benzinga.com/pricing/">
          <Button size="lg" variant="flat-blue">
            GET STARTED
          </Button>
        </SmartLink>
      </FusionSectionContent>
      <Separator className="opacity-15" marginBottom={32} marginTop={40} />
      <ScrollWrapper>
        <GridContainer paddingHorizontal={false} paddingVertical={false}>
          {proLinks.map((item, i) => (
            <a className={styles.proCategoryButton} href={item.link} key={i} rel="noreferrer" target="_blank">
              <item.icon className="pro-icon w3 h3 shrink-0" />
              <span className="ml-1">{item.title}</span>
            </a>
          ))}
        </GridContainer>
      </ScrollWrapper>
      <StylesImageWrapper paddingHorizontal={false} paddingVertical={false}>
        <Image
          alt="BenzingaPro"
          className={styles.imageProWrapper}
          height={444}
          src="/next-assets/images/about/bzpro-lg-bg.png"
          width={1184}
        />
      </StylesImageWrapper>
    </FusionSection>
  );
};

export default AboutPro;

const ScrollWrapper = styled.div`
  & {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
  }

  &::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
  }

  @media (max-width: 1280px) {
    overflow: auto hidden;
  }
`;

const GridContainer = styled(FusionSectionContent)`
  display: flex;
  grid-gap: 16px;
  width: 100%;
  max-width: 1144px;
  margin: 0 auto 32px;
  min-width: max-content;
  ${fusionSectionContentPadding};
`;

const StylesImageWrapper = styled(FusionSectionContent)`
  position: relative;
  ${fusionSectionContentPadding};
  @media screen and (max-width: 567px) {
    padding-right: 0;
  }
`;
