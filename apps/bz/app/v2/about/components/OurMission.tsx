'use client';
import React from 'react';
import { Button } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { FusionSectionCaption, FusionSectionContent, FusionSectionText, FusionSectionTitle } from '@benzinga/ui';

import styles from '../styles.module.scss';

const OurMission = () => {
  return (
    <FusionSectionContent className={styles.ourMissionSectionContent} paddingHorizontal paddingVertical whiteBackground>
      <FusionSectionCaption className="!mb-16">OUR MISSION</FusionSectionCaption>
      <FusionSectionTitle className="max-w-xl">
        Explore the news, the data, and learn what makes your path to financial prosperity easier every day.
      </FusionSectionTitle>
      <FusionSectionText className={styles.ourMissionSectionText}>
        Discover Benzinga tools and resources to inform your investing and trading decisions today.
      </FusionSectionText>
      <SmartLink href="/tools" target="_self">
        <Button size="lg" variant="flat-blue">
          DISCOVER TOOLS
        </Button>
      </SmartLink>
    </FusionSectionContent>
  );
};

export default OurMission;
