import { ArticlePageProps, ArticlePageTemplate } from '@benzinga/templates';
import { getArticles } from '../../../_libs/getArticleData';
import { Metadata } from 'next';
import { MetaProps } from '@benzinga/seo';
import { setMetadata } from '../../../_libs/setMetadata';
type Props = {
  params: Promise<{ id: string }>;
};

export async function generateMetadata({ params }): Promise<Metadata> {
  const id = (await params).id;
  const { props } = await getArticles(id);

  const meta = props?.article?.metaProps || ({} as MetaProps);
  return await setMetadata(meta);
}

const ArticlePage = async ({ params }: Props) => {
  const { id } = await params;
  const { props } = await getArticles(id);

  const passingProps = { ...props } as ArticlePageProps;

  return (
    <ArticlePageTemplate
      {...passingProps}
      enableConnatixScript={true}
      loadInfiniteArticles={true}
      raptiveEnabled={true}
      showCommentButton={true}
      taboolaSettings={{
        placementMethod: 'localStorage',
        unitKey: 'benzinga-benzinga1',
        unitMode: 'widget',
      }}
    />
  );
};
export default ArticlePage;
