import ArticlePage from '../../[id]/page';
import { getArticles } from '../../../../_libs/getArticleData';

import { Metadata } from 'next';
import { MetaProps } from '@benzinga/seo';
import { setMetadata } from '../../../../_libs/setMetadata';
type Props = {
  params: Promise<{ id: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params;
  const { props } = await getArticles(id);

  const meta = props?.article?.metaProps || ({} as MetaProps);
  return await setMetadata(meta);
}

const PressReleasesPage = props => {
  return (
    <ArticlePage /* @next-codemod-error 'props' is used with spread syntax (...). Any asynchronous properties of 'props' must be awaited when accessed. */
    {...props} isTaggedPressRelease={true} />
  );
};

export default PressReleasesPage;
