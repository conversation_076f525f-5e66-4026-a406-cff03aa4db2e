import React from 'react';
import { Layout } from '@benzinga/core-ui';
import { PresetType } from '@benzinga/scanner-widget';

import { LayoutAbove } from './components/LayoutAbove';
import { LayoutMain } from './components/LayoutMain';
import { LayoutSidebar } from './components/LayoutSidebar';

import { getPageData } from './data';
import { PageMeta } from './meta';
import PageLayout from '../../_components/PageLayout';

export default async function Page({ params }: { params: Promise<{ preset: PresetType }> }) {
  const { preset } = await params;
  const { layoutProps, post, scannerData } = await getPageData({ preset: preset });

  return (
    <PageLayout pageProps={layoutProps}>
      <PageMeta preset={preset} />
      <Layout
        layoutAbove={<LayoutAbove preset={preset} scannerData={scannerData} />}
        layoutMain={<LayoutMain post={post} />}
        layoutSidebar={<LayoutSidebar sidebar={post?.sidebar} />}
      />
    </PageLayout>
  );
}
