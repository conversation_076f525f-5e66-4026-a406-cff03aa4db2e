import { MetaProps } from '@benzinga/seo';
import styles from './styles.module.scss';
import <PERSON>ginHandler from './LoginHandler';
// import { getLanguageCodeByHost } from '@benzinga/translate';
// Import i18next directly for server components
import { headers } from 'next/headers';
import PageLayout from '../../_components/PageLayout';
import initTranslations from '../../i18n/translations';
import { getLanguageCodeByHost } from '@benzinga/translate';
import TranslationProvider from '../../i18n/TranslationProvider';
import { getLanguageByHost } from '../../i18n/utils';

export async function generateMetadata() {
  const language = await getLanguageByHost();
  const { t } = await initTranslations(language, ['common', 'auth']);

  return {
    author: 'Benzinga',
    canonical: 'https://www.benzinga.com/login',
    description: t('Auth.Meta.login-to-access-features'),
    title: t('Auth.Meta.login-to-benzinga', { ns: 'auth' }),
  };
}

async function getPageProps() {
  const headersList = await headers();
  const host = headersList.get('host') ?? '';
  const language = getLanguageCodeByHost(host);
  const referer = headersList.get('referer');

  const props = {
    disablePushPrompt: true,
    headerProps: {
      hideBanner: true,
      hideFooter: true,
      hideNavigationBar: true,
      hideQuoteBar: true,
    },
    referer: referer ?? language !== 'en' ? `https://${host}/` : null,
  };
  return { props };
}

const LoginPage = async () => {
  const { props } = await getPageProps();
  const language = await getLanguageByHost();
  const { i18n, resources } = await initTranslations(language, ['common', 'auth']);

  return (
    <TranslationProvider locale={i18n.options.lng} namespaces={i18n.options.namespaces} resources={resources}>
      <PageLayout pageProps={props}>
        <div className={styles.pageWrapper}>
          <LoginHandler referer={props.referer} />
        </div>
      </PageLayout>
    </TranslationProvider>
  );
};

export default LoginPage;
