'use client';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

export const Close: React.FC = () => {
  const { t } = useTranslation();
  const close = () => {
    window.opener = null;
    window.open('about:blank', '_self');
    window.close();
  };

  useEffect(() => {
    close();
  }, []);

  return (
    <button className="font-bold text-underline" onClick={close}>
      {t('Buttons.close-window')}
    </button>
  );
};
