import React from 'react';
import PageLayout from '../../../_components/PageLayout';
import { Close } from './components/Close';
import TranslationsProvider from '../../../i18n/TranslationProvider';
import initTranslations from '../../../i18n/translations';
import { getLanguageByHost } from '../../../i18n/utils';

const PostLoginPage: React.FC = async () => {
  const language = await getLanguageByHost();
  const { i18n, resources, t } = await initTranslations(language, ['common', 'auth']);

  return (
    <TranslationsProvider locale={i18n.options.lng} namespaces={i18n.options.namespaces} resources={resources}>
      <PageLayout>
        <div className="p-8">
          {t('Auth.Login.logged-in-success', { ns: 'auth' })}
          <Close />
        </div>
      </PageLayout>
    </TranslationsProvider>
  );
};

export default PostLoginPage;
