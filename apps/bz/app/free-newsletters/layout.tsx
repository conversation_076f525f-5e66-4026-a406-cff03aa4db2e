import React from 'react';
import <PERSON>ript from 'next/script';
import { Metadata } from 'next';
import { BENZINGA_LOGO_URL } from '@benzinga/seo';

const title = 'Free Newsletters | Benzinga';
const description =
  'Join Hundreds of Thousands Of Subscribers To Our Free Newsletters! Indispensable news, professional analysis and insights delivered straight to your inbox.';
const canonical = 'https://www.benzinga.com/free-newsletters';

export const metadata: Metadata = {
  alternates: {
    canonical,
  },
  authors: [{ name: 'Benz<PERSON>' }],
  description,
  openGraph: {
    description,
    images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
    title,
    type: 'website',
    url: canonical,
  },
  title,
};

export default function FreeNewslettersLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Script
        dangerouslySetInnerHTML={{
          __html: `
          !function() {
            var t, o, c, e = window, n = document, r = arguments, a = "script",
                i = ["call", "cancelAction", "config", "identify", "push", "track", "trackClick", "trackForm", "update", "visit"],
                s = function() {
                  var t, o = this, c = function(t) {
                    o[t] = function() {
                      return o._e.push([t].concat(Array.prototype.slice.call(arguments, 0))), o
                    }
                  };
                  for (o._e = [], t = 0; t < i.length; t++) c(i[t])
                };
            for (e.__woo = e.__woo || {}, t = 0; t < r.length; t++) e.__woo[r[t]] = e[r[t]] = e[r[t]] || new s;
            (o = n.createElement(a)).async = 1, o.src = "https://static.woopra.com/w.js", (c = n.getElementsByTagName(a)[0]).parentNode.insertBefore(o, c)
          }("woopra");

          woopra.config({
            domain: "benzinga.com",
            cookie_domain: ".benzinga.com"
          });

          woopra.track();
        `,
        }}
        id="woopra-tracking"
      />
      <section>{children}</section>
    </>
  );
}
