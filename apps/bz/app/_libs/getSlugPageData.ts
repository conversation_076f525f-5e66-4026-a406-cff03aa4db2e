import { notFound } from 'next/navigation'; // For simulating a 404 response

import { ContentManager, formatTermsSurrogateKeys, Term } from '@benzinga/content-manager';
import { getGlobalSession } from '../../pages/api/session';
import { getRequestInfo } from '../_utils/serverUtils';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { generateTargeting, injectBlockInLayout, moneyMetaInfo } from '@benzinga/money';
import { safeTimeout, SafeType } from '@benzinga/safe-await';
import { combineStoryObjectArrays, filterDuplicateArticles, StoryObject } from '@benzinga/advanced-news-manager';
import { getTermByPath } from '../../pages/api/term';
import { DateTime } from 'luxon';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { getTaboolaBlock } from '@benzinga/ads-utils';
import { toTitleCase } from '@benzinga/utils';
import { PageType, StructuredDataI, MetaProps } from '@benzinga/seo';

export const getStructuredData = (term: Term): StructuredDataI => {
  const structuredData = { pageType: 'WebPage' };
  if (term.vid === '1') {
    structuredData['keywords'] = [
      `"category: ${term?.name?.replace(/"/g, '&quot;')}"`,
      // `"PageIsBzPro: BZ"`
    ];
  }
  if (term.vid === '3') {
    structuredData['keywords'] = [
      `"tag: ${term?.name?.replace(/"/g, '&quot;')}"`,
      // `"PageIsBzPro: BZ"`
    ];
  }
  return structuredData;
};

export const termMetaInfo = (term: Term): MetaProps => {
  // Testing SEO, will remove after swapping all URLs
  const canonical = `https://www.benzinga.com/${term.url_public}`;
  // ToDo: Allow page, dispaly title overrides via the CMS
  const termTitle =
    term?.name?.toLowerCase() === 'news'
      ? `Latest Stock Market News and Breaking Headlines`
      : `${toTitleCase(term?.name) || 'Benzinga'} - Latest News and breaking headlines`;
  return {
    author: 'Benzinga',
    canonical,
    description: '',
    dimensions: {
      contentType: term.vid === '3' ? PageType.Channel : PageType.Topic,
    },
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: term.vid === '3' ? PageType.Channel : PageType.Topic,
    structuredData: getStructuredData(term),
    title: termTitle,
  };
};

export async function getSlugPageData(searchParams: string | string[]) {
  const slug = Array.isArray(searchParams) ? searchParams.join('/') : searchParams || '';
  const { cookies, headers } = await getRequestInfo();

  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const brokerWidgetRes = await contentManager.getWordpressPost(154603);

  try {
    // Check for Money Page
    const postResponse = await contentManager.getPageWithPath(slug);
    const postData = postResponse.ok;

    if (postData && postData?.success !== false && postData.blocks?.length) {
      if (Array.isArray(postData?.blocks)) {
        postData.blocks = await loadServerSideBlockData(session, postData?.blocks, headers, cookies);
      }

      if (Array.isArray(postData?.sidebar?.blocks)) {
        postData.sidebar.blocks = await loadServerSideBlockData(session, postData.sidebar.blocks, headers, cookies);
      }

      if (postData?.template !== 'channel') {
        const targeting = postData?.template ? generateTargeting(postData, postData?.template) : {};

        return {
          props: {
            brokerWidget: brokerWidgetRes?.ok || null,
            headerProps: {
              hideBanner: true,
              hideFooter: !!postData?.layout?.settings?.hide_site_footer,
              hideNavigationBar: !!postData?.layout?.settings?.hide_navigation_bar,
              hideQuoteBar: !!postData?.layout?.settings?.hide_quotes_searchbar,
              hideSearchBar: true,
              isMainDrawerVisible: true,
            },
            metaProps: moneyMetaInfo(postData),
            pageTargeting: targeting,
            post: postData,
            template: postData?.template ?? 'money',
          },
        };
      }
    }

    if (Array.isArray(postData?.sidebar?.blocks)) {
      postData.sidebar.blocks = await loadServerSideBlockData(session, postData.sidebar.blocks, headers, cookies);
    }

    // News Listing Page
    let featuredNewsResponse: SafeType<StoryObject[]> | null = null;
    let newsResponse: SafeType<StoryObject[]> | null = null;
    let featuredNews: StoryObject[] = [];

    const result = await getTermByPath(`${slug}`);

    if (!result?.ok || result?.ok?.response_code === 400) {
      return {
        props: {
          error: 404,
          featuredNews: [],
          news: [],
          topic: '',
        },
      };
    }

    const term = result?.ok?.data && result.ok.data[0];
    const metaProps = postData ? moneyMetaInfo(postData) : termMetaInfo(term);

    let template = '';

    let pageTargeting = {};

    if (term.vid === '1') {
      template = 'channel';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: [138079], primaryChannel: term.tid },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            excludeAutomated: true,
            limit: 7,
          },
        ),
        3000,
      );
      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { channels: [term.tid] },
          {
            excludeAutomated: true,
            limit: 20,
          },
        ),
        3000,
      );
    }

    const getTopicByNameRes = await session.getManager(ContentManager).getTopicByName(term?.name);

    const isIsraelPage = term?.tid === '21365';

    if (term.vid === '3') {
      template = 'topic';
      const basicNewsManager = await session.getManager(BasicNewsManager);
      const now = DateTime.now();

      featuredNewsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            after: now.minus({ days: 2 }).toFormat('yyyy-MM-dd'),
            displayOutput: 'abstract',
            excludeAutomated: true,
            limit: 7,
            type: 'story',
          },
        ),
        3000,
      );

      newsResponse = await safeTimeout(
        basicNewsManager.simplyQueryNews(
          { tags: [term.tid] },
          {
            displayOutput: 'abstract',
            headlines: isIsraelPage ? 'include' : undefined,
            limit: 30, //this is BAD // This parameter is not accurate often return less articles,
            type: 'story',
          },
        ),
        3000,
      );

      if (
        !getTopicByNameRes?.ok?.layout_meta?.force_index &&
        Array.isArray(newsResponse?.ok) &&
        newsResponse.ok.length < 15
      ) {
        metaProps.robots = 'noindex, nofollow';
      }
    }

    // TODO: Fetch related WP Page, use SEO Meta Data and Layout

    // const sidebarRes = await safeAwait(getMoneySidebar(96438));
    if (Array.isArray(featuredNewsResponse?.ok)) {
      featuredNews = featuredNewsResponse.ok;
    }

    let news = newsResponse?.ok ? filterDuplicateArticles(newsResponse?.ok) : [];

    const featuredNewsIds = featuredNews.map(node => node.id);

    if (7 - featuredNews.length) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
      featuredNews = featuredNews.concat(news.splice(0, 7 - featuredNews.length));
    }

    if (news.length > 0) {
      news = news.filter(node => !featuredNewsIds.includes(node.id));
    }

    if (featuredNews.length < 3) {
      news = combineStoryObjectArrays(news, featuredNews);
      featuredNews = [];
    }

    if (Array.isArray(getTopicByNameRes?.ok?.above_content?.blocks)) {
      getTopicByNameRes.ok.above_content.blocks = await loadServerSideBlockData(
        session,
        getTopicByNameRes.ok.above_content.blocks,
        headers,
      );
    }

    if (featuredNews[0]) {
      metaProps.dateCreated = featuredNews[0].updated;
      metaProps.dateUpdated = featuredNews[0].updated;
    }

    // if (term) {
    //   res.setHeader('Surrogate-Key', formatTermsSurrogateKeys(term));
    // }

    if (
      ['topic', 'channel'].includes(template) &&
      (featuredNewsResponse?.err || newsResponse?.err) &&
      process.env.NODE_ENV !== 'development'
    ) {
      return {
        props: {
          error: 500,
          featuredNews: [],
          ...(template === 'topic'
            ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
            : {}),
          news: [],
          slug,
          topic: '',
        },
      };
    }

    news = isIsraelPage ? combineStoryObjectArrays(news, featuredNews) : news;

    if (!news?.length && !getTopicByNameRes?.ok && !postData && !featuredNews?.length) {
      return {
        props: {
          error: 404,
          featuredNews: [],
          ...(template === 'topic'
            ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
            : {}),
          news: [],
          topic: '',
        },
      };
    }

    let layout = postData ?? getTopicByNameRes.ok ?? null;

    if (template === 'channel' || template === 'topic') {
      if (!layout) {
        layout = {
          below_main_content: null,
          header: null,
          in_content: null,
          sidebar: null,
        };
      }

      injectBlockInLayout(layout, template === 'channel' ? 'below_main_content' : 'in_content', getTaboolaBlock());

      pageTargeting = { BZ_PTYPE: template };
      if (term) {
        if (template === 'channel') {
          pageTargeting['BZ_CHANNEL'] = [term.name, term.tid];
        } else if (template === 'topic') {
          pageTargeting['BZ_TAG'] = [term.name, term.tid];
        }
      }
    }

    return {
      props: {
        brokerWidget: brokerWidgetRes?.ok || null,
        ...(template === 'topic'
          ? { headerProps: { showRaptiveBanner: true, showRotatingBanner: false, raptiveExpandedHeader: true } }
          : {}),
        featuredNews,
        layout,
        metaProps,
        news,
        pageTargeting,
        post: postData ?? null,
        slug,
        template: template,
        term: term,
      },
    };
  } catch (error) {
    console.error('Error with [...slug] page data', error);
    return {
      props: {
        error: 404,
        featuredNews: [],
        news: [],
        slug,
        topic: '',
      },
    };
  }
}
