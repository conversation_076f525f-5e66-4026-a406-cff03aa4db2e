import { MetaProps, OpenGraphType, BENZINGA_LOGO_URL } from '@benzinga/seo';

export const setMetadata = async (meta: MetaProps, benzingaLogoAsOgImage = false) => {
  const ogType: OpenGraphType = meta.ogType === 'article' || meta.ogType === 'website' ? meta.ogType : 'website';

  return {
    alternates: {
      canonical: meta.canonical,
    },
    description: meta.description || 'Default description for this article.',
    openGraph: {
      description: meta.description,
      images: meta.ogImage && !benzingaLogoAsOgImage ? [{ url: meta.ogImage }] : [{ url: BENZINGA_LOGO_URL }],
      title: meta.title,
      type: ogType,
      url: meta.canonical || '',
    },
    title: meta.title || 'Default Article Title',
    twitter: {
      card: meta.twitterCard || 'summary_large_image',
      description: meta.description,
      images: meta.ogImage ? [meta.ogImage] : [],
      title: meta.title,
    },
  };
};
