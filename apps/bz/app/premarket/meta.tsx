import { PageType } from '@benzinga/seo';
import { Meta } from '@benzinga/seo';

const metaInfo = () => {
  const canonical = `https://www.benzinga.com/premarket`;
  const twitterCard = 'summary_large_image' as 'summary_large_image';
  return {
    canonical,
    dateCreated: '2020-06-27T03:29:22.000Z',
    dateUpdated: new Date().toISOString(),
    description:
      'Premarket trading coverage for US stocks including news, movers, losers and gainers, upcoming earnings, analyst ratings, economic calendars and futures.',
    image: 'https://cdnwp-s3.benzinga.com/wp-content/uploads/2020/06/30111330/5ggcn2prrtc.jpg',
    ogImage: 'https://cdnwp-s3.benzinga.com/wp-content/uploads/2020/06/30111330/5ggcn2prrtc.jpg',
    pageType: PageType.Tool,
    title: 'Premarket Movers & News',
    twitterCard,
  };
};

export const PageMeta: React.FC<any> = () => <Meta {...metaInfo()} />;
