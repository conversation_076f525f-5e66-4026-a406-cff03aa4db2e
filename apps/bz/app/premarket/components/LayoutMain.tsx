'use client';
import styled from '@benzinga/themetron';
import React from 'react';
import { PremarketPageAPIProps, moversQuery, premarketNewsQuery } from '../../../pages/api/premarket';
import { DateTime } from 'luxon';
import { ErrorBoundary } from '@benzinga/core-ui';

import { Table } from '@benzinga/table';
import { ContentFeed } from '@benzinga/news';

import type { AppPageProps } from '../../../src/entities/app';
import { getLastOpenMarketDay } from '@benzinga/calendars';
import { StockMovers, StockCommodities, GlobalIndexes } from '@benzinga/quotes-ui';
import { EarningsColumnsDef, EconomicsColumnsDef, MoneyPageTemplate, RatingsColumnsDef } from '@benzinga/money';

export interface PremarketPageProps extends PremarketPageAPIProps, AppPageProps {}

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => ({
    default: module.CallToActionForm,
  })),
);

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);
export const LayoutMain: React.FC<PremarketPageProps> = ({
  earningsData,
  economicsData,
  globalIndexesData,
  isWeekend,
  moversData,
  post,
  premarketNews,
  ratingsData,
  stockAndCommoditiesData,
}) => {
  const tabs = [
    {
      key: '/premarket',
      name: 'Overview',
    },
    {
      key: '/premarket-prep',
      name: 'PreMarket Playbook',
    },
  ];
  const now = DateTime.now();
  const lastUpdateTime = getLastOpenMarketDay(now).date.toFormat('MMM d, yyyy');

  return (
    <PremarketPageContainer className="premarket-page">
      {post && (
        <MoneyPageTemplate
          layoutAboveArticle={
            <div className="flex flex-col items-start w-full">
              <div className="top-section w-full flex flex-col mt-[5px]">
                <div className="stock-commodities-and-global-indexes-container">
                  <ErrorBoundary name="premarket-page-stock-commodities">
                    <StockCommodities quotes={stockAndCommoditiesData ?? []} />
                  </ErrorBoundary>
                  <ErrorBoundary name="premarket-page-global-indexes">
                    <GlobalIndexes globalIndexesData={globalIndexesData ?? []} />
                  </ErrorBoundary>
                </div>
                <ErrorBoundary name="premarket-page-stock-movers">
                  <StockMovers
                    gainersLabel="Premarket Gainers"
                    initialMovers={moversData}
                    layout="default"
                    losersLabel="Premarket Losers"
                    query={moversQuery}
                  />
                </ErrorBoundary>
              </div>
              <div className="google-ad-wrapper my-16 desktop">
                <ErrorBoundary name="premarket-page-google-ad-unit">
                  <React.Suspense fallback={<div className="h-[250px] w-[300px] mb-2" />}>
                    <RaptiveAdPlaceholder className="mb-2 overflow-hidden" onlyDesktop={true} type="content-250" />
                  </React.Suspense>
                </ErrorBoundary>
              </div>
              <div className="premarket-news-feed-and-table-container w-full flex flex-col gap-5">
                <div className="premarket-news-feed-and-ad-unit">
                  {/* <div className="h-[220px]">
                    <React.Suspense fallback={<div />}>
                      <Primis id="108026" />
                    </React.Suspense>
                  </div> */}
                  <React.Suspense fallback={<div className="h-[328px]" />}>
                    <CallToActionForm
                      // hubspotFormId="************************************"
                      beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
                      styleOption="secondary"
                      subtitle="The 5-minute newsletter with brief insights on big investment opportunities."
                      title="Beat The Market With Premarket News and Trade Signals"
                    />
                  </React.Suspense>
                  <div className="premarket-news-feed">
                    <ContentFeed
                      limit={4}
                      loadMore={true}
                      nodes={premarketNews}
                      postCardProps={{ imageWidth: 180 }}
                      query={premarketNewsQuery}
                      title="Premarket Top Stories"
                    />
                  </div>
                </div>
                <div className="tables-containers">
                  <ErrorBoundary name="premarket-page-earnings-table">
                    <Table
                      columnsDef={EarningsColumnsDef}
                      height={350}
                      rowData={earningsData}
                      seeMoreLink="https://www.benzinga.com/calendar/earnings"
                      seeMoreText="Earnings"
                      title={!isWeekend ? "Today's Earnings" : 'Upcoming Earnings'}
                    />
                  </ErrorBoundary>
                  <ErrorBoundary name="premarket-page-ratings-table">
                    <Table
                      columnsDef={RatingsColumnsDef}
                      height={350}
                      rowData={ratingsData}
                      seeMoreLink="https://www.benzinga.com/calendar/ratings"
                      seeMoreText="Analyst Ratings"
                      title="Top Analyst Ratings"
                    />
                  </ErrorBoundary>
                  <ErrorBoundary name="premarket-page-economics-table">
                    <Table
                      className="economics-table"
                      columnsDef={EconomicsColumnsDef}
                      rowData={economicsData}
                      seeMoreLink="https://www.benzinga.com/calendar/economic"
                      seeMoreText="Economics Data"
                      title={!isWeekend ? "Today's Economic Data" : 'Upcoming Economic Data'}
                    />
                  </ErrorBoundary>
                </div>
              </div>
              <div className="google-ad-wrapper my-12">
                <ErrorBoundary name="premarket-page-google-ad-unit">
                  <React.Suspense fallback={<div className="h-[250px] w-[300px]" />}>
                    <RaptiveAdPlaceholder className="mb-2 overflow-hidden" type="content-small" />
                  </React.Suspense>
                </ErrorBoundary>
              </div>
            </div>
          }
          layoutTabs={tabs}
          layoutTabsOptions={{ prefetchAllTabs: true }}
          post={post}
          tabOptions={{
            rightSideElement: (
              <div className="right-side-element text-gray-500 text-sm pb-1.5 hidden md:block">
                Data as of {lastUpdateTime}
              </div>
            ),
          }}
          tabsTitle="Premarket Movers & News"
          width="wide"
        />
      )}
    </PremarketPageContainer>
  );
};

// export const podcastPlatforms = {
//   'apple-podcasts': {
//     name: 'Podcasts',
//     url: 'https://podcasts.apple.com/us/podcast/premarket-prep/id915782694',
//   },
//   'google-podcasts': {
//     name: 'Podcasts',
//     url: 'https://podcasts.google.com/?feed=aHR0cHM6Ly9mZWVkcy5zb3VuZGNsb3VkLmNvbS91c2Vycy9zb3VuZGNsb3VkOnVzZXJzOjEwOTEwMDEzNy9zb3VuZHMucnNz',
//   },
//   podbean: {
//     name: 'Podbean',
//     url: 'https://www.podbean.com/podcast-detail/nb6dh-610e6/PreMarket-Prep-Podcast',
//   },
//   spotify: {
//     name: 'Spotify',
//     url: 'https://open.spotify.com/show/6fvsnVzjAOP4PMQWS3Mf3z',
//   },
//   stitcher: {
//     name: 'Stitcher',
//     url: 'https://www.stitcher.com/podcast/benzinga-morning-show',
//   },
//   tunein: {
//     name: 'TuneIn',
//     url: 'https://tunein.com/podcasts/Business--Economics-Podcasts/PreMarket-Prep-p1122264',
//   },
//   youtube: {
//     name: 'YouTube',
//     url: 'https://www.youtube.com/playlist?list=PL4k0fH8EgI3aKwWI3oZZ1cDyPkZv_agYN',
//   },
// };

const PremarketPageContainer = styled.div`
  &.premarket-page {
    .stock-commodities-and-global-indexes-container {
      width: 100%;
      margin-right: 2rem;
      margin-bottom: 2rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .header-container.stock-commodities {
      border-bottom: 2px solid ${({ theme }) => theme.colorPalette.gray100};
    }
    .top-section {
      margin-bottom: 2rem;

      @media (min-width: 700px) {
        margin-bottom: 0;
      }
    }
    .google-ad-wrapper {
      height: 250px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: auto;
      margin-right: auto;
      overflow-x: auto;

      &.desktop {
        display: none;

        @media (min-width: 700px) {
          display: flex;
        }
      }
    }
    .premarket-news-feed-and-ad-unit {
      width: 100%;
      gap: 2rem;
      display: flex;
      flex-direction: column;
    }
    .tables-containers {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    .premarket-news-feed {
      .post-card-wrapper {
        .post-card-image {
          min-width: 90px;
          min-height: 90px;
          width: 90px;
          height: 90px;
          max-width: 90px;
          max-height: 90px;
        }
        .post-card-title {
          font-size: ${({ theme }) => theme.fontSize.base};
          line-height: 1.3;
        }
        .post-card-description {
          line-height: 1.2;
        }
      }
    }
    .podcast-platforms {
      flex-wrap: nowrap;
      overflow-x: auto;
    }
    .platform-badges-listen-on-text {
      display: none;
      @media (min-width: 1000px) {
        display: unset;
      }
    }
    .ant-tabs-extra-content {
      display: none;
    }
    .benzinga-core-table-container.economics-table {
      .table-wrapper {
        height: 350px;
        @media (min-width: 700px) {
          height: 700px;
        }
      }
    }
    @media (min-width: 700px) {
      .ant-tabs-extra-content {
        display: block;
      }
      .top-section {
        flex-direction: row;
      }
      .stock-commodities-and-global-indexes-container {
        width: 300px;
        margin-bottom: unset;
        .stock-commodities-container .header-container {
          strong {
            margin-top: -11px;
            padding-right: 4px;
            z-index: 20;
            display: block;
            position: relative;
            background: white;
          }
          &.stock-commodities {
            border-bottom: unset;
          }
        }
        border-right: 1px solid ${({ theme }) => theme.colorPalette.gray300};
        border-bottom: 1px solid ${({ theme }) => theme.colorPalette.gray300};
        border-top: 1px solid ${({ theme }) => theme.colorPalette.gray300};
        padding-right: 10px;
        padding-bottom: 10px;
      }
      .stock-movers-container {
        .header-container .styled-line {
          &:before {
            background-color: ${({ theme }) => theme.colorPalette.gray300};
          }
        }
      }
      .premarket-news-feed-and-ad-unit {
        max-width: 400px;
      }
      .premarket-news-feed-and-table-container {
        flex-direction: row;
      }
      .stock-movers-table .stock-movers-table-wrapper {
        min-height: 340px;
        tbody {
          max-height: 310px;
          font-size: 0.75rem;
        }
      }
    }
  }
`;
