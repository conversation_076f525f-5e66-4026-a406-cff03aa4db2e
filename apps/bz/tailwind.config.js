const { createGlobPatternsForDependencies } = require('@nrwl/react/tailwind');
const { join } = require('path');
const { workspaceRoot } = require('@nrwl/devkit');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(__dirname, '{src,pages,app,components}/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  plugins: [
    require(join(workspaceRoot, 'config', 'tailwind', 'core', 'tailwind-preset.js')),
    require('tailwindcss-animated'),
  ],

  theme: {
    extend: {
      screens: {
        xs: '480px',
      },
      animation: {
        enterFromLeft: 'enterFromLeft 250ms ease',
        enterFromRight: 'enterFromRight 250ms ease',
        exitToLeft: 'exitToLeft 250ms ease',
        exitToRight: 'exitToRight 250ms ease',
        fadeIn: 'fadeIn 200ms ease',
        fadeOut: 'fadeOut 200ms ease',
        scaleIn: 'scaleIn 200ms ease',
        scaleOut: 'scaleOut 200ms ease',
      },
      colors: {
        'bzblue-100': '#f4f8fe',
        'bzblue-1000': '#192940',
        'bzblue-1100': '#152131',
        'bzblue-1200': '#072232',
        'bzblue-200': '#f1f8fe',
        'bzblue-300': '#e1ebfa',
        'bzblue-400': '#ceddf2',
        'bzblue-500': '#92a7c5',
        'bzblue-600': '#43a0f8',
        'bzblue-700': '#2e79f6',
        'bzblue-800': '#1d456b',
        'bzblue-900': '#0a2749',
        // copying bz pro colours over for dark mode
        'bzgrey-100': '#eff1f6',
        'bzgrey-200': '#d4dbe7',
        'bzgrey-300': '#c5ceda',
        'bzgrey-400': '#818b97',
        'bzgrey-500': '#373f49',
        'bzgrey-600': '#28313C',
        'bzgrey-700': '#212731',
        'bzorange-400': '#d9601a',
        'bzorange-500': '#fa8418',
        'bzorange-600': '#ffa500',
        // TODO: Should consolidate this
        // Values from: config/tailwind/core/base.js
        // eslint-disable-next-line sort-keys-fix/sort-keys-fix
        bzgray: {
          100: '#F2F7FF',
          200: '#E1EBFA',
          300: '#CEDDF2',
          400: '#B8CBE5',
          500: '#99AECC',
          600: '#5B7292',
          700: '#395173',
          800: '#283D59',
          900: '#192940',
        },
        bzred: {
          100: '#FFECEE',
          200: '#FFCFD3',
          300: '#FFB3B9',
          400: '#FF7985',
          50: '#FFF5F6',
          500: '#FF4050',
          600: '#E63A48',
          700: '#992630',
          800: '#731D24',
          900: '#4D1318',
        },
        // eslint-disable-next-line sort-keys-fix/sort-keys-fix
        bzgreen: {
          100: '#DCFCE7',
          200: '#BBF7D0',
          300: '#86EFAC',
          400: '#4ADE80',
          50: '#F0FDF4',
          // 500: '#22C55E',
          500: '#30BF60',
          600: '#16A34A',
          700: '#15803D',
          800: '#166534',
          900: '#14532D',
        },
      },
      keyframes: {
        enterFromLeft: {
          from: { opacity: 0, transform: 'translateX(-200px)' },
          to: { opacity: 1, transform: 'translateX(0)' },
        },
        enterFromRight: {
          from: { opacity: 0, transform: 'translateX(200px)' },
          to: { opacity: 1, transform: 'translateX(0)' },
        },
        exitToLeft: {
          from: { opacity: 1, transform: 'translateX(0)' },
          to: { opacity: 0, transform: 'translateX(-200px)' },
        },
        exitToRight: {
          from: { opacity: 1, transform: 'translateX(0)' },
          to: { opacity: 0, transform: 'translateX(200px)' },
        },
        fadeIn: {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
        fadeOut: {
          from: { opacity: 1 },
          to: { opacity: 0 },
        },
        scaleIn: {
          from: { opacity: 0, transform: 'rotateX(-10deg) scale(0.9)' },
          to: { opacity: 1, transform: 'rotateX(0deg) scale(1)' },
        },
        scaleOut: {
          from: { opacity: 1, transform: 'rotateX(0deg) scale(1)' },
          to: { opacity: 0, transform: 'rotateX(-10deg) scale(0.95)' },
        },
      },
      transitionProperty: {
        in: 'opacity, transform',
        padding: 'padding, margin',
        size: 'width, height',
      },
    },
  },
};
