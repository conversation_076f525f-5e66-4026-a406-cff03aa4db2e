/* eslint-disable @typescript-eslint/no-explicit-any */
declare module '*.svg' {
  import React = require('react');
  const content: string;
  export const ReactComponent: React.FunctionComponent<React.SVGAttributes<SVGElement>>;
  export default content;
}

declare module '*.scss' {
  const content: Record<string, string>;
  export default content;
}

declare module '*.css' {
  const content: Record<string, string>;
  export default content;
}

declare const jobbio_display: any;

declare global {
  interface Window {
    env: Environments;
    // TODO
    intercomSettings: any;
    google: google;
  }
}

declare namespace NodeJS {
  interface ProcessEnv {
    readonly NODE_ENV: 'development' | 'production' | 'test' | 'staging';
    readonly RUNTIME_ENV: 'development' | 'production' | 'test' | 'staging' | 'sandbox';
    CORALOGIX_RUM_APPLICATION: string;
    CORALOGIX_RUM_DOMAIN: 'EU1' | 'EU2' | 'US1' | 'US2' | 'AP1' | 'AP2';
    CORALOGIX_RUM_PUBLIC_KEY: string;
    CORALOGIX_RUM_SESSION_SAMPLE_RATE?: string;
    CORALOGIX_RUM_SESSION_RECORDING_SAMPLE_RATE?: string;
  }
}

declare interface Window {
  env: {
    AG_GRID_KEY: string;
    API_ROOT: string;
    CALENDAR_KEY: string;
    CHART_API: string;
    CHART_SOCK: string;
    CHART_TRADINGVIEW: string;
    CONTENT_ADDR: string;
    DATAAPI_KEY: string;
    DATAAPI_ROOT: string;
    IAM_ROOT: string;
    INCLUDE_LOGROCKET: string;
    LOG_ROCKET_API_KEY: string;
    MIXPANEL_KEY: string;
    NCHAN_ADDR: string;
    PRO_API: string;
    QUOTE_ADDR: string;
    QUOTE_STORE_API_ROOT: string;
    RELEASE_VERSION: string;
    SEGMENT_KEY: string;
    SERVICES_ROOT: string;
    SIGNALS_RESTFUL_ADDR: string;
    SIGNALS_SOCKET_ADDR: string;
    SIGNALS_KEY: string;
    SQUAWK_ADDR: string;
    STAGING_SEGMENT_KEY: string | undefined;
    STRIPE_PUBLISHABLE_KEY: string;
    TRADE_IT_API_ENDPOINT: string;
    TRADE_IT_API_KEY: string;
    TRADE_IT_ORIGIN_URL: string;
  };
}
