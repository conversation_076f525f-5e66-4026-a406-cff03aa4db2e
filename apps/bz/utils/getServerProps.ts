import { isBrowser } from '@benzinga/device-utils';
import { MetaProps, PageType } from '@benzinga/seo';
import { homeData } from '../pages/api/home';
import { getGlobalSession } from '../pages/api/session';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { HOME_PAGE_TABS } from '../src/components/Home/components/HomeTabsConstants';
import { getLanguageCodeByHost } from '@benzinga/translate';
import { Term } from '@benzinga/content-manager';

// Need to fix below error
export type PagePropsType = Awaited<ReturnType<typeof fetchPageProps>>['props'] & {
  article?: any;
  post?: any;
  page?: any;
  embeddedWidget?: any;
  statusCode?: number;
  disablePageTracking?: boolean;
  error?: number;
  disablePushPrompt?: boolean;
  term?: Term;
};

export const fetchPageProps = async (query, req) => {
  const url = !isBrowser() ? `https://${req.headers.host}` : '';

  if (query?.p) {
    return {
      redirect: {
        destination: `/money/preview/${query.p}`,
      },
    };
  }
  // const homeProps = await homeData(); // homepage hasnt been migrated yet. This will get homepage props for EVERY page in app router
  const homeProps: any = {};

  homeProps.url = url;
  homeProps.activeTab = HOME_PAGE_TABS.TRENDING;
  const session = getGlobalSession();

  if (Array.isArray(homeProps?.post?.blocks)) {
    homeProps.post.blocks = await loadServerSideBlockData(session, homeProps.post.blocks, req.headers, req.cookies);

    // use topStoriesFeed to replace nodes for block
    const topStoriesBlock = homeProps.post.blocks.find(
      block => block?.blockName === 'acf/news-list-group' && block?.attrs?.data.groups[0]?.title === 'Top Stories',
    );
    if (topStoriesBlock) {
      topStoriesBlock.attrs.data.groups[0].topStoriesFeed = homeProps.topStoriesFeed;
    }

    // disperse partner content
    const partnerLength = homeProps?.partnerContentDisperseMap?.length ?? 0;
    if (homeProps.partnerContentDisperseMap && partnerLength > 0) {
      for (const feed of homeProps.partnerContentDisperseMap) {
        const data = findMatchingBlockGroup(homeProps.post.blocks, feed.queryKey, feed.queryValues);
        if (data) {
          data.nodes?.pop();
          data.nodes?.push(feed.posts[0]);
        }
      }
    }
  }
  if (Array.isArray(homeProps?.post?.sidebar?.blocks)) {
    homeProps.post.sidebar.blocks = await loadServerSideBlockData(
      session,
      homeProps.post.sidebar.blocks,
      req.headers,
      req.cookies,
    );
  }

  const metaProps: MetaProps = {
    canonical: 'https://www.benzinga.com/',
    description:
      homeProps.post?.meta?.description ??
      'Stock Market Quotes, Business News, Financial News, Trading Ideas, and Stock Research by Professionals.',
    hrefLanguage: getLanguageCodeByHost(req.headers.host),
    image: homeProps.post?.meta?.image ?? null,
    pageType: PageType.Front,
    title: homeProps.post?.meta?.title ?? 'Actionable Trading Ideas, Real-Time News, Financial Insight',
  };

  if (homeProps?.featuredNews?.[0]) {
    metaProps.dateCreated = homeProps.featuredNews?.[0]?.created ?? null;
    metaProps.dateUpdated = homeProps.featuredNews?.[0]?.updated ?? null;
  }

  return {
    props: {
      ...homeProps,
      metaProps,
      pageTrackerProps: {
        pageLoad: {
          type: 'home_page',
        },
      },
    },
  };
};

const findMatchingBlockGroup = (blocks, queryKey, queryValues) => {
  let index = 0;
  const block = blocks.find(block => {
    if (block.blockName === 'acf/news-list-group') {
      return block.attrs.data.groups.find(group =>
        group.query_and_options.query[queryKey]?.some(value => {
          if (queryValues.includes(value)) {
            index = block.attrs.data.groups.indexOf(group);
            return true;
          }
          return false;
        }),
      );
    }
    return false;
  });
  return block ? block.attrs.data.groups[index] : null;
};
