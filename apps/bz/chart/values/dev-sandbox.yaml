environment: sandbox

nameOverride: ""
fullnameOverride: ""

image:
  repository: "registry.gitlab.benzinga.io/benzinga/fusion/benzinga-next"
  tag: v1.21.0
  pullPolicy: IfNotPresent
  pullSecret: gitlab-registry

service:
  type: ClusterIP
  port: 80
  annotations: {}

ingress:
  host: "benzinga-next.nxt.benzinga.dev"
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  tls: {}
  hosts:
    - benzinga-next.nxt.benzinga.dev
    - benzinga-next.nxt.zingbot.bz
resources: {}
