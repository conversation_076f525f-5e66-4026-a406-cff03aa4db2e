'use client';
import React, { startTransition } from 'react';

import styled from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { BasicNewsManager, BreakingNews } from '@benzinga/basic-news-manager';

import { HeaderMobile } from './HeaderMobile';
import { HeaderDesktop } from './HeaderDesktop';
import { DefaultQuotesList, featuredMenu } from './_mockMenu';
import type { HeaderMobileProps } from './HeaderMobile';

import { appEnvironment, appName } from '@benzinga/utils';
import { isMobile } from '@benzinga/device-utils';
import { DateTime } from 'luxon';
import Hooks from '@benzinga/hooks';

import { NavigationHeaderProps } from '../../entities';

import { SimpleHeaderDesktop } from './Variants/Simple/SimpleHeader';
import { SimpleHeaderMobile } from './Variants/Simple/SimpleHeaderMobile';
import { DelayedQuote, QuotesManager, getLocalUserRecentTickers } from '@benzinga/quotes-manager';
import { StockSymbol } from '@benzinga/session';
import { useBenzingaEdge } from '@benzinga/edge';

// const InvestingChannel = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.InvestingChannel };
//   }),
// );

export const HeaderNavigationComponent: React.FC<NavigationHeaderProps> = ({ variant, ...props }) => {
  const HeadersVariants = {
    simple: SimpleHeaderDesktop,
  };

  const HeaderVariantComponent = HeadersVariants[variant ?? ''];

  return HeaderVariantComponent ? <HeaderVariantComponent {...props} /> : <HeaderDesktop {...props} />;
};

export const HeaderNavigationMobileComponent: React.FC<NavigationHeaderProps & HeaderMobileProps> = ({
  variant,
  ...props
}) => {
  const HeadersVariants = {
    simple: SimpleHeaderMobile,
  };

  const HeaderVariantComponent = HeadersVariants[variant ?? ''];

  return HeaderVariantComponent ? <HeaderVariantComponent {...props} /> : <HeaderMobile {...props} />;
};

export const NavigationHeader = ({
  hideBanner = false,
  hideMenuBar = false,
  hideQuoteBar,
  hideTopBar,
  initialBreakingsNews,
  logoVariant,
  marketTickers = DefaultQuotesList,
  menus,
  moneyBannerLink = '',
  moneyBannerText = '',
  onSearch,
  quotes: initialQuotes,
  //showAboveHeaderBlock = false,
  raptiveExpandedHeader,
  showRaptiveBanner = false,
  showRotatingBanner = false,
  sticky = true,
  variant: headerVariant = 'default',
}: NavigationHeaderProps) => {
  const [recentTickers, setRecentTickers] = React.useState<string[]>();

  const [isMobileHeaderVisible, setIsMobileHeaderVisible] = React.useState(true);
  const [isDesktopHeaderVisible, setIsDesktopHeaderVisible] = React.useState(true);
  const [breakingNews, setBreakingNews] = React.useState<BreakingNews[]>(initialBreakingsNews ?? []);
  const [isBannerShowing, setIsBannerShowing] = React.useState<boolean>(!!initialBreakingsNews?.length);
  const [quotes, setQuotes] = React.useState<Record<StockSymbol, DelayedQuote> | undefined | null>(initialQuotes);
  const session = React.useContext(SessionContext);
  const quoteManager = session.getManager(QuotesManager);
  // const hasAdLight = useBenzingaEdge().adLightEnabled;
  // const isEdgeLoading = useBenzingaEdge().isLoading;
  const shouldShowRaptiveBanner = showRaptiveBanner;
  // const shouldShowRaptiveBanner =
  //   (showRaptiveBanner && !isEdgeLoading && !hasAdLight) || (showRaptiveBanner && isEdgeLoading && !hasAdLight);

  const checkVisibility = React.useCallback(() => {
    if (isMobile()) {
      startTransition(() => {
        setIsMobileHeaderVisible(true);
        setIsDesktopHeaderVisible(false);
      });
    } else {
      startTransition(() => {
        setIsDesktopHeaderVisible(true);
        setIsMobileHeaderVisible(false);
      });
    }
  }, []);

  React.useEffect(() => {
    checkVisibility();
  }, [checkVisibility]);

  React.useEffect(() => {
    const recentTickers = getLocalUserRecentTickers();
    startTransition(() => {
      setRecentTickers(recentTickers.splice(0, 7));
    });
  }, []);

  Hooks.useEventListener('resize', () => {
    checkVisibility();
  });

  React.useEffect(() => {
    if (marketTickers && !initialQuotes) {
      quoteManager.getDelayedQuotes(marketTickers).then(res => {
        if (res?.ok) {
          startTransition(() => {
            setQuotes(res.ok);
          });
        }
      });
    } else {
      startTransition(() => {
        setQuotes(initialQuotes);
      });
    }
  }, [quoteManager, marketTickers, initialQuotes]);

  React.useEffect(() => {
    if (hideBanner) return;

    const dateNowTime = DateTime.now().setZone('America/New_York');

    const getCurrentBreakingNews = (breakingNews: BreakingNews[]): BreakingNews[] => {
      if (!Array.isArray(breakingNews)) return [];
      return breakingNews.filter(breakingNewsItem => {
        const formattedTimeEnd = Number(breakingNewsItem.time_end);
        const timeEndDateTime = DateTime.fromSeconds(formattedTimeEnd);
        return dateNowTime < timeEndDateTime;
      });
    };

    const currentBreakingNews = initialBreakingsNews ? getCurrentBreakingNews(initialBreakingsNews) : [];

    if (currentBreakingNews.length > 0) {
      setIsBannerShowing(true);
      return;
    }

    const basicNewsManager = session.getManager(BasicNewsManager);
    basicNewsManager.getBreakingNews().then(res => {
      const events = ['breaking', 'notice', 'event', 'news'];
      const result = res?.ok?.data;
      if (result) {
        const _data: BreakingNews[] = [];
        if (result.length === 0) return;
        result.forEach(item => {
          const type = item.type.toLowerCase();
          if (events.includes(type)) {
            _data.push(item);
          }
        });
        setBreakingNews(_data);
        setIsBannerShowing(true);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialBreakingsNews, hideBanner]);

  const isMoneyApp = appEnvironment().isApp(appName.money);
  const isIndiaApp = appEnvironment().isApp(appName.india);

  return (
    <Container className={`header-container ${isMoneyApp ? 'money-app' : ''}`}>
      {/* {showAboveHeaderBlock && <AboveHeaderBlock />} */}
      {isMobileHeaderVisible && (
        <MobileHeaderWrapper $isBannerShowing={false} className="mobile-header-wrapper">
          <HeaderNavigationMobileComponent
            breakingNews={isBannerShowing && !hideBanner ? breakingNews : []}
            featuredMenu={!isIndiaApp ? featuredMenu : []}
            hideMenuBar={hideMenuBar}
            hideQuoteBar={hideQuoteBar}
            logoVariant={logoVariant}
            marketTickers={marketTickers}
            menus={menus}
            quotes={quotes}
            shouldRenderRaptiveBanner={showRaptiveBanner}
            showRaptiveBanner={shouldShowRaptiveBanner}
            showRotatingBanner={showRotatingBanner}
            variant={headerVariant}
            //AboveHeaderBlock={showAboveHeaderBlock ? <AboveHeaderBlock /> : null}
          />
        </MobileHeaderWrapper>
      )}
      {isDesktopHeaderVisible && (
        <HeaderNavigationComponent
          breakingNews={isBannerShowing && !hideBanner ? breakingNews : []}
          hideBanner={hideBanner}
          hideMenuBar={hideMenuBar}
          hideQuoteBar={hideQuoteBar}
          hideTopBar={hideTopBar}
          logoVariant={logoVariant}
          marketTickers={marketTickers}
          menus={menus}
          moneyBannerLink={moneyBannerLink}
          moneyBannerText={moneyBannerText}
          onSearch={onSearch}
          quotes={quotes}
          raptiveExpandedHeader={raptiveExpandedHeader}
          recentTickers={recentTickers}
          shouldRenderRaptiveBanner={showRaptiveBanner}
          showRaptiveBanner={shouldShowRaptiveBanner}
          showRotatingBanner={showRotatingBanner}
          sticky={sticky}
          variant={headerVariant}
          //showAboveHeaderBlock={showAboveHeaderBlock}
        />
      )}
    </Container>
  );
};

const Container = styled.div`
  &.header-container {
    .navbar-placeholder {
      height: 132px;
    }
    &.money-app {
      .main-menu-bar {
        .main-menu-wrapper {
          max-width: 1300px;
          .menu-wrapper {
            &:hover {
              background-color: #151f2e;
            }
          }
        }
        .logo-wrapper {
          width: 260px;
        }
        background-color: #192940;
        border: none;
        .menu-item-link {
          color: #ceddf2;
          &:hover {
            background-color: transparent;
          }
        }
      }
    }
    /* .subnav {
      width: 300px;
    } */
  }
`;

// const AboveHeaderBlock: React.FC = () => {
//   return (
//     <AboveHeaderBlockWrapper>
//       <AboveHeaderAdUnit />
//     </AboveHeaderBlockWrapper>
//   );
// };

// const AboveHeaderBlockWrapper = styled.div`
//   vertical-align: middle;
//   justify-content: center;
//   display: flex;
//   border-bottom: solid 1px #ddd;
// `;

export const AboveHeaderAdUnit = () => {
  return (
    <AboveHeaderAdUnitWrapper>
      {/* <React.Suspense fallback={<div className="h-[90px]" />}>
        <InvestingChannel
          className="mb-0 above-header-ad-unit"
          id="above-header"
          mobileSize="300x100,300x50,320x100,320x50"
          sensor={false}
          size="728x90,970x90"
        />
      </React.Suspense> */}
    </AboveHeaderAdUnitWrapper>
  );
};

const AboveHeaderAdUnitWrapper = styled.div`
  height: calc(105px + 1rem);
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 800px) {
    height: calc(123px + 1rem);
  }
`;

const MobileHeaderWrapper = styled('div')<{ $isBannerShowing: boolean }>`
  display: block;
  margin-bottom: ${({ $isBannerShowing }) => ($isBannerShowing ? '3.5rem' : '0')};
  @media screen and (min-width: 800px) {
    display: none;
  }
`;
