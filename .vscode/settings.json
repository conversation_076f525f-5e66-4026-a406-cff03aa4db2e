{"cSpell.words": ["autosize", "Benzinga", "BZQL", "Dispatchable", "financials", "Fulltext", "Newsfeed", "newsfeeds", "pakke", "Permissioned", "Popout", "Protos", "styletron", "themetron", "unsub", "upsert", "verbosities", "watchlist", "watchlists"], "editor.tabSize": 2, "editor.trimAutoWhitespace": true, "eslint.enable": true, "files.trimTrailingWhitespace": true, "files.exclude": {"**/.awcache": true, "**/.DS_Store": true, "**/.git": false, "**/.happypack": true, "**/.hg": true, "**/.next": false, "**/.svn": true, "**/CVS": true}, "javascript.validate.enable": false, "typescript.tsdk": "node_modules/typescript/lib", "editor.rulers": [], "gitlab.instanceUrl": "https://gitlab.benzinga.io", "editor.formatOnSave": true, "eslint.codeActionsOnSave.rules": null, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[xml]": {"editor.defaultFormatter": "DotJoshJohnson.xml"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}, "svg.preview.background": "dark-transparent", "files.eol": "\n", "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "nxConsole.generateAiAgentRules": true}